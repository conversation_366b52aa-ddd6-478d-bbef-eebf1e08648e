# Test Suite Documentation

This document provides comprehensive instructions for running the test suite for all Spark batch processing jobs.

## Overview

The test suite includes:
- **Unit Tests**: Test individual methods and components in isolation
- **Integration Tests**: Test complete execution flows with realistic data
- **No-Spark Tests**: Fast tests for core logic without Spark dependencies
- **Mock Data**: Comprehensive mock data generators for all scenarios
- **Coverage Reporting**: Code coverage analysis and reporting

## Test Structure

```
tests/
├── conftest.py                          # Pytest configuration and shared fixtures
├── test_*_no_spark.py                   # Unit tests without Spark dependencies
├── test_*.py                            # Unit tests with Spark session
├── test_*_integration.py                # Integration tests for full workflows
└── __pycache__/                         # Python cache (auto-generated)

requirements-test.txt                    # Test dependencies
pytest.ini                              # Pytest configuration
run_tests.py                            # Main test runner script
run_simple_tests.py                     # Simple test runner (no Spark)
install_test_deps.sh                    # Dependency installation script
```

## Prerequisites

1. **Python 3.9+** installed
2. **Java 11 or 17** (required for PySpark 3.5.4)
   - **Important**: PySpark 3.5.4 requires Java 11 minimum
   - If you have Java 8, you'll need to upgrade
3. **Git** (for cloning the repository)

### Java Version Compatibility

- **PySpark 3.5.4**: Requires Java 11+
- **PySpark 3.2.x**: Compatible with Java 8+

If you encounter Java version issues:
1. **Upgrade Java** (recommended): Install Java 11 or 17
2. **Check JAVA_HOME**: Ensure it points to the correct Java installation

## Installation

### 1. Create Virtual Environment (Recommended)

```bash
# Create virtual environment with Python 3.9
python3.9 -m venv .venv

# Activate virtual environment
source .venv/bin/activate  # Linux/macOS
# or
.venv\Scripts\activate     # Windows
```

### 2. Install Test Dependencies

```bash
# Option 1: Use installation script (recommended)
./install_test_deps.sh

# Option 2: Install manually
pip install --upgrade pip
pip install -r requirements-test.txt

# Option 3: Use test runner to install dependencies
python run_tests.py --install-deps
```

### 3. Set Environment Variables

```bash
# Set JAVA_HOME (adjust path as needed)
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64  # Linux
export JAVA_HOME=$(/usr/libexec/java_home -v 11)     # macOS

# Set SPARK_LOCAL_IP (for local testing)
export SPARK_LOCAL_IP=127.0.0.1

# Add src to Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
```

## Running Tests

### Quick Start (No Spark Required)

For fast testing of core logic without setting up Spark:

```bash
# Activate virtual environment
source .venv/bin/activate

# Run simple tests that don't require Spark
python run_simple_tests.py
```

This will test core business logic and other non-Spark functionality quickly.

### Option 1: Using the Test Runner Script (Recommended)

The `run_tests.py` script provides various options for running tests:

```bash
# Run all working tests (default)
python run_tests.py

# Run all tests (including potentially unstable ones)
python run_tests.py --all

# Run only working tests
python run_tests.py --working

# Run tests with coverage reporting
python run_tests.py --coverage

# Install dependencies and run tests
python run_tests.py --install-deps
```

### Option 2: Using Pytest Directly

```bash
# Run all tests
python -m pytest tests/ -v

# Run tests without Spark dependencies (fastest)
python -m pytest tests/test_*_no_spark.py -v

# Run tests for a specific job
python -m pytest tests/test_flash_games_pnl*.py -v
python -m pytest tests/test_transaction_transformer*.py -v
python -m pytest tests/test_aum_tier_upgrade*.py -v

# Run with coverage
python -m pytest tests/ -v --cov=src --cov-report=html --cov-report=term-missing

# Run specific test method
python -m pytest tests/test_flash_games_pnl.py::TestFlashGamesPnL::test_init -v

# Run tests matching a pattern
python -m pytest tests/ -k "test_init" -v

# Run integration tests only
python -m pytest tests/ -m integration -v
```

## Test Categories

### No-Spark Tests (`test_*_no_spark.py`)

Fast unit tests that don't require Spark session:
- Core business logic validation
- Data transformation functions
- Configuration and initialization
- Utility functions and helpers
- Edge case handling

### Unit Tests (`test_*.py`)

Tests individual methods and components with Spark session:
- Class initialization and configuration
- Data retrieval and processing methods
- Spark DataFrame operations
- Field casting and transformations
- Database operations (mocked)

### Integration Tests (`test_*_integration.py`)

Tests complete execution workflows:
- End-to-end job execution
- Data flow validation
- Error handling scenarios
- Performance and resource usage
- Complex business scenarios

## Test Data

The tests use comprehensive mock data including:
- **Transaction Data**: Various transaction types across asset classes
- **Asset Data**: Stocks, crypto currencies, options, forex, etc.
- **User Data**: User profiles, balances, and preferences
- **Configuration Data**: Job configurations and business rules
- **Time-based Data**: Historical and real-time data scenarios

## Coverage Reporting

After running tests with coverage, you can view the reports:

```bash
# View coverage in terminal
python -m pytest tests/ --cov=src --cov-report=term-missing

# Generate HTML coverage report
python -m pytest tests/ --cov=src --cov-report=html

# Generate XML coverage report (for CI/CD)
python -m pytest tests/ --cov=src --cov-report=xml

# Set coverage threshold
python -m pytest tests/ --cov=src --cov-fail-under=70

# Open HTML report in browser
open htmlcov/index.html      # macOS
xdg-open htmlcov/index.html  # Linux
start htmlcov/index.html     # Windows
```

## Troubleshooting

### Common Issues

1. **Java Version Compatibility Error**
   ```
   Error: java.lang.UnsupportedClassVersionError: ... has been compiled by a more recent version of the Java Runtime
   ```
   **Solution**: Upgrade to Java 11+ and set JAVA_HOME:
   ```bash
   # Check Java version
   java -version

   # Set JAVA_HOME (adjust path as needed)
   export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64  # Linux
   export JAVA_HOME=$(/usr/libexec/java_home -v 11)     # macOS
   ```

2. **JAVA_HOME Not Set**
   ```bash
   # Linux
   export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

   # macOS with Homebrew
   export JAVA_HOME=$(/usr/libexec/java_home -v 11)

   # Verify JAVA_HOME
   echo $JAVA_HOME
   ls -la $JAVA_HOME/bin/java
   ```

3. **PySpark Issues**
   ```bash
   export SPARK_LOCAL_IP=127.0.0.1
   export PYSPARK_PYTHON=python3
   export PYSPARK_DRIVER_PYTHON=python3
   ```

4. **Import Errors**
   ```bash
   export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
   ```

5. **Permission Issues**
   ```bash
   chmod +x run_tests.py
   chmod +x run_simple_tests.py
   chmod +x install_test_deps.sh
   ```

6. **Virtual Environment Issues**
   ```bash
   # Recreate virtual environment
   rm -rf .venv
   python3.9 -m venv .venv
   source .venv/bin/activate
   pip install --upgrade pip
   pip install -r requirements-test.txt
   ```

### Debug Mode

Run tests with more verbose output:

```bash
# Maximum verbosity
python -m pytest tests/ -vvv --tb=long

# Show print statements
python -m pytest tests/ -v -s

# Stop on first failure
python -m pytest tests/ -v -x

# Run tests in parallel (faster)
python -m pytest tests/ -v -n auto
```

## Continuous Integration

For CI/CD pipelines, use:

```bash
# Install dependencies and run tests with coverage
python run_tests.py --install-deps

# Or with pytest directly
pip install -r requirements-test.txt
python -m pytest tests/ --cov=src --cov-report=xml --cov-fail-under=70 --junit-xml=test-results.xml
```

## Test Configuration

The `pytest.ini` file contains default configuration:
- Test discovery patterns
- Coverage settings and thresholds
- Warning filters
- Markers for test categorization (integration, unit, etc.)
- Output formatting options

## Adding New Tests

When adding tests for new jobs:

1. **Follow naming conventions**:
   - `test_<job_name>.py` - Unit tests with Spark
   - `test_<job_name>_no_spark.py` - Unit tests without Spark
   - `test_<job_name>_integration.py` - Integration tests

2. **Use shared fixtures** from `conftest.py`:
   - `spark_session` - Configured Spark session
   - `mock_config` - Mock configuration data
   - Add new fixtures as needed

3. **Test categories to include**:
   - Initialization and configuration
   - Core business logic (no-spark tests)
   - Data processing methods
   - Integration workflows
   - Error handling scenarios

4. **Best practices**:
   - Mock external dependencies
   - Use realistic test data
   - Test both happy path and edge cases
   - Keep tests fast and independent

## Performance

Typical test execution times:
- **No-Spark tests**: ~5-15 seconds
- **Unit tests**: ~30-90 seconds
- **Integration tests**: ~60-180 seconds
- **Full suite with coverage**: ~2-5 minutes

Use parallel execution (`-n auto`) for faster results on multi-core systems.

## Dependencies

Current test environment:
- **Python**: 3.9+
- **PySpark**: 3.5.4
- **Java**: 11 or 17
- **Pytest**: 7.4.3
- **Coverage**: pytest-cov 4.1.0

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all prerequisites are installed correctly
3. Ensure environment variables are set properly
4. Review test output for specific error messages
5. Check that virtual environment is activated
