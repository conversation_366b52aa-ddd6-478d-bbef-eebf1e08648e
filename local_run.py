# src/main.py
import sys
import os
import importlib
from src.utils.config_loader import ConfigLoader
from src.utils.date_utils import DateUtils
from datetime import date, timedelta, datetime
import argparse
from src.utils.job_metrics import JobMetrics


def get_job_class(full_class_path):
    *module_parts, class_name = full_class_path.split(".")
    module_name = ".".join(module_parts)
    module = importlib.import_module(module_name)
    return getattr(module, class_name)


def main():
    config_loader = ConfigLoader("local")
    config = config_loader.load_config()

    start_time = datetime.now()

    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--job_name", help="fully qualified job class")
    args, unknown = parser.parse_known_args()
    if args.offset:
        offset = args.offset
        config['offset'] = int(offset)

    cutoff_ts, t_1, t_2 = DateUtils.get_cutoff_time_and_jkt_date(config)
    config["cutoff_ts"] = cutoff_ts
    config["t_1"] = t_1
    config["t_2"] = t_2

    if args.job_name:
        job_name = args.job_name

    additional_args = {}
    for i in range(0, len(unknown), 2):
        key = unknown[i].lstrip("--")
        value = unknown[i + 1]
        additional_args[key] = value

    additional_args["snapshot_group"] = "gold"
    job_name = "snapshots.transactions_snapshot.TransactionsSnapshot"
    full_job_name = "src.jobs.{}".format(job_name)
    job_class = get_job_class(full_job_name)
    job_instance = job_class(config=config, **additional_args)
    job_instance.run()

    end_time = datetime.now()
    job_metrics = JobMetrics()
    job_metrics.job_time_metrics(start_time, end_time, job_name)


if __name__ == "__main__":
    main()
