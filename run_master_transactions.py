#!/usr/bin/env python3
"""
Master Transactions Processor Runner
Run this script from the project root directory.
"""

import sys
import os
from datetime import datetime

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.jobs.master_transactions.transformers.transaction_orchestrator import TransactionOrchestrator

def main():
    """Main function to run the master transactions processor."""
    print("🚀 Starting Master Transactions Processor...")
    
    # Load configuration
    config = {
        "bucket_path": "s3://pluang-datalake-calculated-staging/",
        "pluang_partner_id": 1000,
        "transactions_snapshot_path": "s3://pluang-datalake-calculated-staging/raw_data/",
        "prices": {
            "forex": {"price_path": "forex_prices"},
            "gold": {"price_path": "gold_prices"},
            "global_stock": {"price_path": "global_stocks_prices"},
            "crypto_currency": {"price_path": "crypto_currency_prices"},
            "crypto_currency_futures": {"price_path": "crypto_currency_futures_prices"},
            "fund": {"price_path": "fund_prices"},
            "global_stock_options": {
                "price_path": "global_stock_options_price",
                "price_snapshot_folder": "options_snapshots"
            }
        },
        "usdt_coin_id": 10005
    }
    
    # Set date parameters
    today = datetime.now()
    t_1 = today.strftime("%Y-%m-%d")
    h_1 = "00"  # or current hour
    
    print(f"📅 Processing date: {t_1}")
    print(f"🕐 Processing hour: {h_1}")
    
    # Create orchestrator
    orchestrator = TransactionOrchestrator(
        config=config,
        t_1=t_1,
        h_1=h_1
    )
    
    try:
        # Process specific asset types
        asset_types = ["crypto_currency"]
        print(f"🔄 Processing asset types: {asset_types}")
        
        results = orchestrator.process_all_assets(asset_types)
        
        # Print results
        total_transactions = 0
        for asset_type, df in results.items():
            count = df.count()
            total_transactions += count
            print(f"✅ {asset_type.upper()}: {count:,} transactions processed")
            df.show(5, truncate=False)
        
        print(f"\n🎉 Successfully processed {total_transactions:,} total transactions!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        raise
    finally:
        print("🛑 Stopping Spark session...")
        orchestrator.spark_utils.stop_spark(orchestrator.spark)

if __name__ == "__main__":
    main()
