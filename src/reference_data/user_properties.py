from src.utils.spark_utils import *


class UserProperties:
    def __init__(self, spark: SparkSession, config):
        self.spark = spark
        self.config = config
        self.io_utils = IOUtils(self.spark, self.config)
        self.op = Operations(self.spark)

    def get_accounts(self, dt):
        accounts = self.io_utils.read_csv_file("{}/{}/dt={}/".format(self.config["bucket_path"], S3Paths.accounts, dt), None, False)
        accounts = accounts.withColumn("account_id", col("id"))
        return accounts

    def get_pluang_plus_members(self, dt):
        plus_members = self.io_utils.read_csv_file("{}/{}/dt={}/".format(self.config["bucket_path"], S3Paths.user_tag_mappings, dt), None, False)
        plus_members = plus_members.filter(col("tag_name").isin(self.config["plus_member_tag_name"]))
        plus_members = self.op.de_dupe_dataframe(plus_members, ["user_id"], "updated")

        accounts = self.get_accounts(dt)
        accounts = accounts.filter(col("partner_id") == self.config["pluang_partner_id"]).select("user_id", "account_id")

        plus_members = plus_members.join(accounts, on=["user_id"], how="left")
        return plus_members

