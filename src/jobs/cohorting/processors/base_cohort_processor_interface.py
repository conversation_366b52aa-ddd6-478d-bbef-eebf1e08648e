"""
Abstract base class for all cohort processors.

Defines the interface that all cohort processors must implement.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from src.utils.spark_utils import *


class BaseCohortProcessorInterface(ABC):
    """
    Abstract interface for all cohort processors.
    
    All cohort processors must inherit from this class and implement the required methods.
    This defines the common contract that both parameter-based and custom processors follow.
    """
    
    def __init__(self, spark: SparkSession, config: Dict[str, Any], logger, parameter_registry=None):
        """
        Initialize cohort processor.
        
        Args:
            spark: SparkSession instance
            config: Job configuration dictionary
            logger: Logger instance
            parameter_registry: Optional shared parameter registry
        """
        self.spark = spark
        self.config = config
        self.logger = logger
        self.parameter_registry = parameter_registry
    
    @abstractmethod
    def process_cohort(self, cohort_id: str, cohort_config: Dict[str, Any], 
                      cohort_params: Dict[str, Any] = None) -> DataFrame:
        """
        Process a cohort and return the result DataFrame.
        
        Args:
            cohort_id: Unique identifier for the cohort
            cohort_config: Cohort configuration from MongoDB
            cohort_params: Optional cohort parameters
            
        Returns:
            DataFrame containing cohort results
            
        Raises:
            NotImplementedError: Must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement process_cohort()")

