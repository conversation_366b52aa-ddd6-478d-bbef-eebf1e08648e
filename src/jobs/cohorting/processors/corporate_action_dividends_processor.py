"""
Corporate Action Dividends Cohort Processor.

Identifies accounts eligible for stock dividends based on accounts on record date.
Logic:
1. Read stock dividends data for t_1 partition, filter by updated >= t_1 - 1 day
2. For each distinct record_date, read accounts snapshot and filter by (record_date, global_stock_id)
3. Return distinct list of (account_id, global_stock_id) eligible for dividends
"""

import time
from typing import Dict, Any, List
from src.utils.spark_utils import *
from src.utils.io_utils import IOUtils
from src.utils.operations import Operations
from src.utils.date_utils import DateUtils
from src.utils.asset_utils import AssetUtils
from src.jobs.cohorting.processors.base_cohort_processor_interface import BaseCohortProcessorInterface


class CorporateActionDividendsCohortProcessor(BaseCohortProcessorInterface):
    """Custom processor for corporate action dividends cohort."""

    def __init__(self, spark: SparkSession, config: Dict[str, Any], logger, parameter_registry=None):
        """Initialize processor with Spark session and configuration."""
        super().__init__(spark, config, logger, parameter_registry)
        
        self.ops = Operations(spark)
        self.io_utils = IOUtils(spark, config)
        self.asset_utils = AssetUtils(spark, config)
        self.t_1 = config.get("t_1", "")
        self.t_2 = config.get("t_2", "")
        
        # Get S3 paths from config
        self.bucket_path = config.get("bucket_path", "")
        
        # Log initialization details
        self.logger.info(f"CorporateActionDividendsCohortProcessor initialized")
        self.logger.info(f"Processing dates: t_1={self.t_1}, t_2={self.t_2}")
        

    def process_cohort(self, cohort_id: str, cohort_config: Dict[str, Any], 
                      cohort_params: Dict[str, Any] = None) -> DataFrame:
        """
        Process corporate action dividends cohort.
        
        Implements the abstract method from BaseCohortProcessorInterface.
        
        Args:
            cohort_id: Cohort identifier
            cohort_config: Cohort configuration
            cohort_params: Optional cohort parameters (not used)
            
        Returns:
            DataFrame with columns [account_id, user_id, Dividend asset Id list, Dividend asset list]
        """
        start_time = time.time()
        try:
            self.logger.info(f"Processing {cohort_id} cohort for date: {self.t_1}")
            
            # Step 1: Read and filter stock dividends data
            dividends_df = self._read_and_filter_dividends()
            
            if dividends_df.count() == 0:
                self.logger.warning(f"No dividend records found for {self.t_1}")
                schema = StructType([
                    StructField("account_id", LongType(), True),
                    StructField("user_id", LongType(), True),
                    StructField("Dividend asset Id list", StringType(), True),
                    StructField("Dividend asset list", StringType(), True)
                ])
                return self.spark.createDataFrame([], schema)
            
            # Step 2: Get distinct record_dates and process accounts for each
            eligible_accounts_df = self._process_accounts_by_record_date(dividends_df)
            
            # Step 3: Join with assets and aggregate into lists per account/user
            gss_assets = self.asset_utils.get_global_stock_assets().select(
                col("id").alias("global_stock_id"),
                col("pluang_company_code")
            )
            
            final_df = (eligible_accounts_df
                .select("account_id", "user_id", "global_stock_id")
                .distinct()
                .join(gss_assets, on="global_stock_id", how="left")
                .select("account_id", "user_id", 
                        col("global_stock_id"), 
                        col("pluang_company_code"))
                .groupBy("account_id", "user_id")
                .agg(
                    F.array_join(F.collect_set(col("global_stock_id")), ",").alias("Dividend asset Id list"),
                    F.array_join(F.collect_set(col("pluang_company_code")), ",").alias("Dividend asset list")
                ).drop("global_stock_id", "pluang_company_code").distinct()
            )
            
            final_count = final_df.count()
            elapsed_time = time.time() - start_time
            
            self.logger.info(
                f"Cohort {cohort_id}: {final_count} eligible accounts in {elapsed_time:.2f}s"
            )
            
            return final_df
            
        except Exception as e:
            self.logger.error(f"Error processing cohort {cohort_id}: {e}")
            raise

    def _read_and_filter_dividends(self) -> DataFrame:
        """
        Read stock dividends data for t_1 partition and filter by updated timestamp.
        
        Returns:
            DataFrame with columns [global_stock_id, record_date]
        """
        try:
            self.logger.info(f"Reading stock dividends for partition: {self.t_1}")
            dividends_path = "{}/{}/dt={}".format(self.bucket_path, S3Paths.global_stock_dividends_v2, self.t_1)
            
            dividends_df = self.io_utils.read_parquet_data(dividends_path)
            
            # Filter by date(updated) should be either today (t_1) or yesterday (t_1 - 1 day)
            # Using DataFrame transformations to check if date of updated matches t_1 or t_1 - 1
            t_1_date = to_date(lit(self.t_1))
            t_2_date = to_date(lit(self.t_2))
            # t_1_date = to_date(lit("2025-10-14"))
            # t_2_date = to_date(lit("2025-10-13"))
            
            # Filter: date(updated) IN (t_1, t_1 - 1)
            filtered_df = dividends_df.filter(
                (to_date(col("updated")) == t_1_date) | 
                (to_date(col("updated")) == t_2_date)
            )
            
            # Select required columns and drop nulls on key columns
            result_df = filtered_df.select("global_stock_id", "record_date") \
                .dropna(subset=["global_stock_id", "record_date"])
            
            # Cache for reuse
            result_df.cache()
            
            filtered_count = result_df.count()
            self.logger.info(
                f"Filtered dividends: {filtered_count} records "
                f"with date(updated) IN ({self.t_1}, {self.t_2})"
            )
            self.logger.info(f"Result dividends:")
            result_df.show()
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"Error reading/filtering stock dividends: {e}")
            raise

    def _process_accounts_by_record_date(self, dividends_df: DataFrame) -> DataFrame:
        """
        Process accounts snapshots for each distinct record_date.
        
        Args:
            dividends_df: Filtered dividends DataFrame with [global_stock_id, record_date]
            
        Returns:
            DataFrame with columns [account_id, global_stock_id]
        """
        try:
            # Get distinct (record_date, global_stock_id) pairs
            dividends_by_date = dividends_df.select("record_date", "global_stock_id").distinct()
            
            # Collect unique record_dates
            record_dates = [row["record_date"] for row in dividends_df.select("record_date").distinct().collect()]
            
            self.logger.info(f"Processing accounts for {len(record_dates)} distinct record dates")
            
            # Process accounts for each record_date
            accounts_dfs = []
            
            for record_date in record_dates:
                self.logger.info(f"Processing accounts for record_date: {record_date}")
                
                # Convert record_date to partition_date format (YYYY-MM-DD)
                partition_date = str(record_date) if isinstance(record_date, str) else record_date.strftime("%Y-%m-%d")
                
                try:
                    # Read accounts snapshot for this partition
                    accounts_path = "{}/{}/dt={}".format(self.bucket_path, S3Paths.global_stock_accounts, partition_date)
                    self.logger.info(f"Reading accounts from path: {accounts_path}")
                    accounts_df = self.io_utils.read_csv_file(accounts_path)
                    
                    # Select only required columns immediately after reading to reduce data size
                    accounts_df = accounts_df.select("account_id", "user_id", "global_stock_id", "created") \
                        .dropna(subset=["account_id", "user_id", "global_stock_id"])
                    self.logger.info(f"accounts_count before filter: {accounts_df.count()}")
                    
                    # Log distinct created dates in accounts_df before filtering
                    self.logger.info(f"Distinct created dates in accounts_df:")
                    accounts_df.select(to_date(col("created")).alias("created_date")).distinct().orderBy(col("created_date").desc()).show()
                    
                    # Filter accounts where created date matches the record_date
                    filtered_accounts = accounts_df.filter(to_date(col("created")) == lit(record_date))
                    self.logger.info(f"Accounts with created date = {record_date}: {filtered_accounts.count()}")
                    
                    # Filter dividends for this specific record_date
                    date_dividends = dividends_by_date.filter(col("record_date") == lit(record_date))
                    self.logger.info(f"Dividends for {record_date}: {date_dividends.count()}")
                    
                    # Join filtered accounts with dividends on global_stock_id (inner join to filter eligible stocks)
                    result = filtered_accounts.join(
                        date_dividends,
                        on="global_stock_id",
                        how="inner"
                    ).select("account_id", "user_id", "global_stock_id")
                    
                    accounts_count = result.count()
                    self.logger.info(f"Found {accounts_count} eligible accounts for {record_date}")
                    
                    if accounts_count > 0:
                        accounts_dfs.append(result)
                    
                except Exception as e:
                    self.logger.warning(f"Error reading accounts for {partition_date}: {e}")
                    # Continue processing other dates
                    continue
            
            # Union all results
            if not accounts_dfs:
                self.logger.warning("No eligible accounts found across all record dates. Returning empty DataFrame.")
                schema = StructType([
                    StructField("account_id", LongType(), True),
                    StructField("user_id", LongType(), True),
                    StructField("global_stock_id", LongType(), True)
                ])
                return self.spark.createDataFrame([], schema)
            
            # Union all DataFrames
            combined_df = accounts_dfs[0]
            for df in accounts_dfs[1:]:
                combined_df = combined_df.unionByName(df)
            
            return combined_df
            
        except Exception as e:
            self.logger.error(f"Error processing accounts by record date: {e}. Returning empty DataFrame.")
            raise

