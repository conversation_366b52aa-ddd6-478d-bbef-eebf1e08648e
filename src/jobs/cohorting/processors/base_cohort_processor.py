"""Base cohort processor for parameter-based cohort logic."""
import time
from typing import Dict, List, Any, Optional
from src.utils.spark_utils import *
from src.jobs.cohorting.processors.base_cohort_processor_interface import BaseCohortProcessorInterface
from src.jobs.cohorting.engine.parameter_registry import ParameterRegistry
from src.jobs.cohorting.data_sources.data_source_manager import DataSourceManager
from src.jobs.cohorting.engine.sql_query_engine import SQLQueryEngine
from src.jobs.cohorting.data_sources.aum_data_source import AUMDataSource
from src.jobs.cohorting.data_sources.bigquery_app_events_data_source import BigQueryAppEventsDataSource
from src.jobs.cohorting.data_sources.held_assets_data_source import HeldAssetsDataSource


class BaseCohortProcessor(BaseCohortProcessorInterface):
    """Base cohort processor."""

    def __init__(self, spark: SparkSession, config: Dict[str, Any], logger, parameter_registry=None):
        """Initialize processor."""
        super().__init__(spark, config, logger, parameter_registry)
        
        self.ops = Operations(spark)
        self.io_utils = IOUtils(spark, config)

        self.parameter_registry = parameter_registry or ParameterRegistry(spark, config, logger)
        self.data_source_manager = DataSourceManager(spark, config, logger)
        self.sql_engine = SQLQueryEngine(spark, logger)

        self.data_source_manager.register_data_source_class("AUMDataSource", AUMDataSource)
        self.data_source_manager.register_data_source_class("BigQueryAppEventsDataSource", BigQueryAppEventsDataSource)
        self.data_source_manager.register_data_source_class("HeldAssetsDataSource", HeldAssetsDataSource)

        self.t_1 = config.get("t_1", "")

    def process_cohort(self, cohort_id: str, cohort_config: Dict[str, Any], cohort_params: Dict[str, Any] = None) -> DataFrame:
        """Process cohort."""
        start_time = time.time()
        try:
            self.logger.info(f"Processing cohort: {cohort_id}")

            parameters = cohort_config.get("parameters", [])
            if not parameters:
                return self.spark.createDataFrame([], StructType([]))

            parameter_dfs = {}
            total_param_rows = 0
            total_param_duration = 0.0

            for param_id, param_config in self.parameter_registry.get_parameter_configs(
                [p["parameter_id"] for p in parameters]
            ).items():
                param_start = time.time()

                param_join_config = next((p for p in parameters if p["parameter_id"] == param_id), {})
                param_df = self._process_parameter(param_config, param_join_config)

                parameter_dfs[param_id] = param_df
                param_rows = param_df.count()
                total_param_rows += param_rows
                total_param_duration += time.time() - param_start

            joined_df = self._join_parameter_dataframes(parameter_dfs, parameters)
            final_df = self._apply_cohort_logic(joined_df, cohort_config)

            total_duration = time.time() - start_time
            final_rows = final_df.count()
            joined_rows = joined_df.count()

            self.logger.info(
                f"Cohort {cohort_id}: {len(parameter_dfs)} params, "
                f"{total_param_rows} param_rows, {joined_rows} joined, "
                f"{final_rows} final in {total_duration:.2f}s"
            )

            return final_df

        except Exception as e:
            self.logger.error(f"Error processing cohort {cohort_id}: {e}")
            raise

    def _process_parameter(self, param_config: Dict[str, Any], join_config: Dict[str, Any]) -> DataFrame:
        """Process parameter."""
        try:
            self.logger.info(f"param_config = {param_config}")
            param_id = param_config["parameter_id"]
            data_source_class = param_config["data_source_class"]
            data_source_params = param_config.get("data_source_params") or {}            
            sql_condition = param_config["sql_condition"]
            required_columns = param_config["required_columns"]
            output_columns = param_config["output_columns"]
            group_by_columns = param_config["group_by_columns"]

            self.logger.info(f"Processing parameter {param_id} from data source class {data_source_class}")

            data_source_instance = self.data_source_manager.get_data_source_instance(data_source_class)
            df = data_source_instance.read(
                params=data_source_params,
                required_columns=required_columns
            )

            # Filter out rows where any required column is NULL
            if required_columns:
                for col_name in required_columns:
                    if col_name in df.columns:
                        df = df.filter(F.col(col_name).isNotNull())
                self.logger.info(f"After NULL filtering on required columns, row count: {df.count()}")

            if df.count() == 0:
                self.logger.warning(f"No data found for parameter {param_id}")
                schema = StructType([StructField(col, StringType(), True) for col in output_columns])
                return self.spark.createDataFrame([], schema)

            if sql_condition or group_by_columns:
                variable_substitutions = {"{t_1}": self.t_1}
                query_config = {}

                if sql_condition:
                    query_config["where_condition"] = sql_condition

                if group_by_columns:
                    query_config["group_by_columns"] = group_by_columns
                    if "view_count" in output_columns:
                        query_config["aggregations"] = {"view_count": "COUNT(*)"}

                df = self.sql_engine.execute_sql(df, query_config, variable_substitutions)

            # Select output columns
            self.logger.info(f"output_columns = {output_columns}")
            self.logger.info(f"df columns = {df.columns}")
            if output_columns:
                missing_columns = [col for col in output_columns if col not in df.columns]
                if missing_columns:
                    self.logger.error(f"Missing output columns for parameter {param_config.get('parameter_id', 'unknown')}: {missing_columns}")

                df = df.select(*output_columns)

            return df

        except Exception as e:
            self.logger.error(f"Error processing parameter {param_config.get('parameter_id', 'unknown')}: {e}")
            raise

    
    def _join_parameter_dataframes(self, parameter_dfs: Dict[str, DataFrame],
                                   parameters: List[Dict[str, Any]]) -> DataFrame:
        """Join parameter DataFrames."""
        try:
            enriched_join_configs = []
            param_configs = self.parameter_registry.get_parameter_configs(
                [p["parameter_id"] for p in parameters]
            )
            
            for param in parameters:
                param_id = param["parameter_id"]
                join_config = {
                    "parameter_id": param_id,
                    "join_type": param["join_type"]
                }
                
                if param_id in param_configs:
                    join_config["join_keys"] = param_configs[param_id]["join_keys"]
                else:
                    self.logger.warning(f"Parameter config not found for {param_id}")
                    join_config["join_keys"] = []
                
                enriched_join_configs.append(join_config)
            
            return self.sql_engine.join_parameter_dataframes(parameter_dfs, enriched_join_configs)

        except Exception as e:
            self.logger.error(f"Error joining parameter DataFrames: {e}")
            raise

    def _apply_cohort_logic(self, df: DataFrame, cohort_config: Dict[str, Any]) -> DataFrame:
        """Apply cohort SQL logic."""
        try:
            self.logger.info(f"cohort_config = {cohort_config}")
            cohort_logic = cohort_config.get("cohort_logic") or {}
            cohort_logic_dict = cohort_logic.asDict() if hasattr(cohort_logic, "asDict") else {}
            select_clause = cohort_logic_dict.get("select_clause", "")
            where_clause = cohort_logic_dict.get("where_clause", "")
            group_by_columns = cohort_logic_dict.get("group_by_columns", [])
            if where_clause:
                # Substitute config-driven placeholders in where clause (e.g., {page_visit_view_count_min})
                wc = where_clause
                cohorting_cfg = self.config.get("cohorting", {})
                try:
                    # Only replace known placeholders; default to 5 for page visit threshold
                    wc = wc.replace("{page_visit_view_count_min}", str(cohorting_cfg.get("page_visit_view_count_min", 5)))
                except Exception:
                    pass
                df = df.filter(wc)

            if group_by_columns:
                # Get columns that are not in group_by_columns
                non_group_columns = [col for col in df.columns if col not in group_by_columns]
                # Create aggregation expressions for each non-grouped column
                agg_exprs = [F.first(col).alias(col) for col in non_group_columns]
                df = df.groupBy(*group_by_columns).agg(*agg_exprs)

            if select_clause:
                select_columns = [col.strip() for col in select_clause.split(",")]
                available_columns = [col for col in select_columns if col in df.columns]
                df = df.select(*available_columns)

            return df

        except Exception as e:
            self.logger.error(f"Error applying cohort logic: {e}")
            raise
    
