"""Held assets positions (gss/crypto) at t_1 → account_id,user_id,asset_type,asset_symbol."""

from typing import List, Dict, Any, Optional
from src.jobs.cohorting.data_sources.base_data_source import *
from src.utils.asset_utils import AssetUtils
from src.utils.s3_paths import S3Paths


class HeldAssetsDataSource(BaseDataSource):
    """Reads returns CSVs (gss/crypto), filters qty>0, joins symbols, returns unified view."""

    name = "HeldAssetsDataSource"

    def __init__(self, spark, config, logger):
        super().__init__(spark, config, logger)
        self.asset_utils = AssetUtils(spark, config)

    def read(self, params: Dict[str, Any], required_columns: List[str]) -> DataFrame:
        """Return account_id,user_id,asset_type,asset_symbol for t_1; supports params["assets"]."""
        try:
            assets = params["assets"]
            partition_date = self.config.get("t_1")
            cols = ["account_id", "user_id", "asset_type", "asset_symbol"]
            dfs = []
            if "gss" in assets:
                d = self._read_global_stocks_returns(partition_date)
                if d is not None: dfs.append(d.select(*cols))
            if "crypto" in assets:
                d = self._read_crypto_returns(partition_date)
                if d is not None: dfs.append(d.select(*cols))
            if not dfs:
                return self._create_empty_dataframe(required_columns or cols)
            unified_df = dfs[0]
            for d in dfs[1:]: unified_df = unified_df.unionByName(d)
            return self._apply_column_selection(unified_df.dropna(subset=["account_id", "user_id"]).distinct(), required_columns)

        except Exception as e:
            self.logger.error(f"Error reading HeldAssetsDataSource: {repr(e)}")
            return self._create_empty_dataframe(required_columns)

    def _read_global_stocks_returns(self, dt: str) -> Optional[DataFrame]:
        """Global stocks returns → positions with symbols."""
        try:
            path = f"{self.bucket_path}/{S3Paths.global_stock_returns}/dt={dt}/"
            self.logger.info(f"Reading GSS returns: {path}")
            df = self.io_utils.read_csv_file(path)
            df = df.select("global_stock_id", "account_id", "user_id", "total_quantity").withColumn(
                "total_quantity", col("total_quantity").cast(DoubleType())
            ).filter(col("total_quantity") > lit(0))
            gss_assets = self.asset_utils.get_global_stock_assets().select(
                col("id").alias("global_stock_id"), col("pluang_company_code").alias("asset_symbol")
            )
            return df.join(gss_assets, on="global_stock_id", how="left").select(
                "account_id", "user_id", lit("global_stocks").alias("asset_type"), "asset_symbol"
            )
        except AnalysisException as ae:
            if "Path does not exist:" in str(ae):
                self.logger.warning(f"GSS returns missing: {repr(ae)}")
                return None
            raise
        except Exception as e:
            self.logger.error(f"GSS returns error: {repr(e)}")
            return None

    def _read_crypto_returns(self, dt: str) -> Optional[DataFrame]:
        """Crypto returns → positions with symbols."""
        try:
            path = f"{self.bucket_path}/{S3Paths.crypto_currency_returns_t_2_files}/dt={dt}/"
            self.logger.info(f"Reading Crypto returns: {path}")
            df = self.io_utils.read_csv_file(path)
            df = df.select("crypto_currency_id", "account_id", "user_id", "total_quantity").withColumn(
                "total_quantity", col("total_quantity").cast(DoubleType())
            ).filter(col("total_quantity") > lit(0))
            crypto_assets = self.asset_utils.get_crypto_assets().select(
                col("id").alias("crypto_currency_id"), col("symbol").alias("asset_symbol")
            )
            return df.join(crypto_assets, on="crypto_currency_id", how="left").select(
                "account_id", "user_id", lit("crypto_currency").alias("asset_type"), "asset_symbol"
            )
        except AnalysisException as ae:
            if "Path does not exist:" in str(ae):
                self.logger.warning(f"Crypto returns missing: {repr(ae)}")
                return None
            raise
        except Exception as e:
            self.logger.error(f"Crypto returns error: {repr(e)}")
            return None


