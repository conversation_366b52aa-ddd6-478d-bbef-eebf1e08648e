"""
Base data source class for the Cohorting Engine.

Provides common interface for all data source implementations.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any
from pyspark.sql import DataFrame, SparkSession
from src.utils.spark_utils import *
from src.utils.io_utils import IOUtils
from src.utils.date_utils import DateUtils
from src.utils.operations import Operations


class BaseDataSource(ABC):
    """
    Abstract base class for data source implementations.

    Each concrete data source must implement:
    - name: Class identifier
    - read(): Data reading logic specific to the source
    """

    name: str

    def __init__(self, spark: SparkSession, config: Dict[str, Any], logger):
        self.spark = spark
        self.config = config
        self.logger = logger
        self.bucket_path = config.get("bucket_path")
        self.t_1 = config.get("t_1", "")
        self.io_utils = IOUtils(spark, config)
        self.ops = Operations(spark)

    @abstractmethod
    def read(self, params: Dict[str, Any], required_columns: List[str]) -> DataFrame:
        """
        Read data from this data source.

        Args:
            params: Parameters for reading (e.g., partition_date, date_range)
            required_columns: List of columns that must be present

        Returns:
            DataFrame with the data
        """
        pass

    def _create_empty_dataframe(self, columns: List[str]) -> DataFrame:
        """Create empty DataFrame with specified columns."""
        if columns:
            schema = StructType([StructField(col, StringType(), True) for col in columns])
            return self.spark.createDataFrame([], schema)
        return self.spark.createDataFrame([], StructType([]))

    def _expand_date_range(self, start_date: str, end_date: str) -> List[str]:
        """Expand date range to list of dates."""
        return DateUtils.dates_between(start_date, end_date)

    def _get_partition_dates(self, params: Dict[str, Any]) -> List[str]:
        """Extract partition dates from params."""
        if "date_range" in params:
            start = params["date_range"]["start_date"]
            end = params["date_range"]["end_date"]
            return self._expand_date_range(start, end)
        elif "partition_date" in params:
            return [params["partition_date"]]
        else:
            # Fallback to t_1 if present in config
            t_1 = self.config.get("t_1")
            if t_1:
                return [t_1]
            return []

    def _validate_required_columns(self, df: DataFrame, required_columns: List[str]) -> None:
        """
        Validate that required columns are present in DataFrame.

        Args:
            df: DataFrame to validate
            required_columns: List of required column names

        Raises:
            ValueError: If any required columns are missing
        """
        if not required_columns:
            return

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            available_columns = list(df.columns)
            self.logger.error(f"Missing required columns: {missing_columns}. Available: {available_columns}")
            raise ValueError(f"Data source missing required columns: {missing_columns}")

    def _apply_column_selection(self, df: DataFrame, required_columns: List[str]) -> DataFrame:
        """
        Select only required columns from DataFrame, adding missing ones as nulls.

        Args:
            df: Input DataFrame
            required_columns: List of column names to select

        Returns:
            DataFrame with selected columns
        """
        if not required_columns:
            return df

        # Select available columns
        available_columns = [col for col in required_columns if col in df.columns]
        if available_columns:
            df = df.select(*available_columns)

        # Add missing columns as null
        for col in required_columns:
            if col not in df.columns:
                df = df.withColumn(col, lit(None))

        return df

    def _handle_date_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle date parameter processing and validation.

        Args:
            params: Parameters dictionary that may contain date information

        Returns:
            Processed parameters with date information
        """
        processed_params = params.copy()

        # Handle partition_date defaulting to t_1
        if "partition_date" not in processed_params and "date_range" not in processed_params:
            if hasattr(self.config, 'get') and self.config.get("t_1"):
                processed_params["partition_date"] = self.config["t_1"]
                self.logger.info(f"Using default partition_date: {processed_params['partition_date']}")
            else:
                self.logger.warning("No partition_date or date_range specified and no default t_1 available")

        # Validate date range if provided
        if "date_range" in processed_params:
            date_range = processed_params["date_range"]
            if not isinstance(date_range, dict) or "start_date" not in date_range or "end_date" not in date_range:
                raise ValueError("date_range must be a dict with 'start_date' and 'end_date' keys")
            self.logger.info(f"Processing date range: {date_range['start_date']} to {date_range['end_date']}")

        return processed_params

