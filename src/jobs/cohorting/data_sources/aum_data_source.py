"""
AUM (Assets Under Management) Data Source.

Handles reading transaction data from S3 partitions with support for single date or date range.
Reads from global stock transactions, options contract transactions, crypto currency transactions,
and crypto currency pocket transactions locations.
"""

from typing import List, Dict, Any, Optional
from src.jobs.cohorting.data_sources.base_data_source import *
from src.utils.asset_utils import AssetUtils


class AUMDataSource(BaseDataSource):
    """
    Data source for AUM (Assets Under Management) data.

    Reads from S3 partitions at:
    - /snapshots/global_stock_transactions/dt={date}/
    - /snapshots/options_contract_transactions/dt={date}/
    - /snapshots/crypto_currency_transactions/dt={date}/
    - /snapshots/crypto_currency_pocket_transactions/dt={date}/
    File type: Parquet
    """

    name = "AUMDataSource"

    def __init__(self, spark, config, logger):
        super().__init__(spark, config, logger)
        self.asset_utils = AssetUtils(spark, config)

    def read(self, params: Dict[str, Any], required_columns: List[str]) -> DataFrame:
        """
        Read AUM data from S3 partitions.

        Args:
            params: Parameters containing partition_date or date_range, and optionally 'assets' list
                   to filter which asset types to load (e.g., ['gss'], ['gss', 'crypto'])
                   If 'assets' not specified, loads all available asset types
            required_columns: Required columns to enforce

        Returns:
            DataFrame with AUM data
        """
        try:
            partition_dates = self._get_partition_dates(params)
            if not partition_dates:
                self.logger.warning("No partition_date or date_range specified; returning empty dataframe")
                return self._create_empty_dataframe(required_columns)

            self.logger.info(f"AUM params = {params}")
            self.logger.info(f"AUM partition_dates = {partition_dates}")
            assets = params["assets"]
            self.logger.info(f"AUM assets = {assets}")
            df_result = self._read_and_union_partitions(partition_dates, assets)

            if df_result is None or df_result.count() == 0:
                return self._create_empty_dataframe(required_columns)

            return self._enforce_required_columns(df_result, required_columns)

        except Exception as e:
            self.logger.error(f"Error reading AUM data: {e}")
            return self._create_empty_dataframe(required_columns)

    def _read_and_union_partitions(self, partition_dates: List[str], assets: List[str]) -> Optional[DataFrame]:
        """Read and union data from all specified partitions."""
        result = None
        for dt in partition_dates:
            partition_df = self._get_asset_data(dt, assets)
            result = self.ops.get_union(result, partition_df)
        return result

    def _get_crypto_assets(self) -> DataFrame:
        """Get crypto assets with id and symbol mapping."""
        self.logger.info("Fetching crypto assets")
        df_crypto = self.asset_utils.get_crypto_assets()
        df_crypto = df_crypto.select(col("id").alias("crypto_currency_id"), col("symbol").alias("asset_symbol"))
        return df_crypto

    def _get_global_stock_assets(self) -> DataFrame:
        """Get global stock assets with id and pluang_company_code mapping."""
        self.logger.info("Fetching global stock assets")
        df_gss = self.asset_utils.get_global_stock_assets()
        df_gss = df_gss.select(col("id").alias("global_stock_id"), col("pluang_company_code").alias("asset_symbol"))
        return df_gss

    def _get_crypto_future_instrument_assets(self):
        df_crypto_futures = self.asset_utils.get_crypto_future_instrument_assets()
        df_crypto_futures = df_crypto_futures.select(
            col("id").alias("crypto_future_instrument_id"), col("future_pair_symbol").alias("asset_symbol")
        )
        return df_crypto_futures

    def _get_asset_data(self, dt: str, assets: List[str]) -> DataFrame:
        """Get unioned DataFrame for requested assets with selected columns."""
        partition_df = None

        if "gss" in assets:
            # Get global stock asset symbols
            gss_assets = self._get_global_stock_assets()
            
            # Read and union GSS asset data sources
            gss_path = f"{self.bucket_path}/snapshots/global_stock_transactions/dt={dt}/"
            self.logger.info(f"fetched gss path - {gss_path}")
            gss_df = self.io_utils.read_parquet_data(gss_path).select(
                "account_id", "user_id", col("id").alias("transaction_id"), "global_stock_id", col("global_stock_id").alias("asset_id"), "created", "updated",
                "executed_quantity", "executed_unit_price", "executed_total_price", col("transaction_fee").alias("fees"),
                "transaction_type", "status", col("usd_to_idr").alias("currency_to_idr"), "transaction_time",
                lit(0).alias("leverage"), lit("global_stocks").alias("asset_type"), lit("global_stock_transactions").alias("asset_sub_type")
            ).filter(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"]))
            
            # Join with asset symbols
            gss_df = gss_df.join(gss_assets, on="global_stock_id", how="left").drop("global_stock_id")
            self.logger.info(f"fetched gss data count - {gss_df.count()}")

            partition_df = gss_df

        if "crypto" in assets:
            # Get crypto asset symbols
            crypto_assets = self._get_crypto_assets()
            
            # Read and union crypto asset data sources
            crypto_path = f"{self.bucket_path}/snapshots/crypto_currency_transactions/dt={dt}/"
            crypto_df = self.io_utils.read_parquet_data(crypto_path).select(
                "account_id", "user_id", col("id").alias("transaction_id"), 
                "crypto_currency_id", col("crypto_currency_id").alias("asset_id"), "created", "updated", 
                "executed_quantity", "executed_unit_price", "executed_total_price", col("fee").alias("fees"), 
                "transaction_type", "status", lit(1).alias("currency_to_idr"), "transaction_time", 
                lit(0).alias("leverage"), lit("crypto_currency").alias("asset_type"), lit("crypto_currency_transactions").alias("asset_sub_type")
            ).filter(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"]))
            
            # Join with asset symbols
            crypto_df = crypto_df.join(crypto_assets, on="crypto_currency_id", how="left").drop("crypto_currency_id")
            self.logger.info(f"fetched crypto_df data count - {crypto_df.count()}")

            crypto_future_path = f"{self.bucket_path}/snapshots/crypto_future_transactions/dt={dt}/"
            crypto_future_assets = self._get_crypto_future_instrument_assets()
            crypto_future_df = self.io_utils.read_parquet_data(crypto_future_path).select(
                "account_id", "user_id", col("id").alias("transaction_id"),
                "crypto_future_instrument_id", col("crypto_future_instrument_id").alias("asset_id"), "created", "updated",
                "executed_quantity", "executed_unit_price", (col("executed_quantity") * col("executed_unit_price")).cast("double").alias("executed_total_price"), lit(0).alias("fees"),
                 lit("BUY").alias("transaction_type"), lit("SUCCESS").alias("status"), lit("usdt_price").alias("currency_to_idr"), "transaction_time",
                "leverage", lit("crypto_future").alias("asset_type"), lit("crypto_future_transactions").alias("asset_sub_type")
            ).filter(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"]))
            
            # Join with asset symbols
            crypto_future_df = crypto_future_df.join(crypto_future_assets, on="crypto_future_instrument_id", how="left").drop("crypto_future_instrument_id")
            self.logger.info(f"fetched crypto_future_df data count - {crypto_future_df.count()}")


            crypto_union_df = crypto_df.unionByName(crypto_future_df, allowMissingColumns=True)

            if partition_df is None:
                partition_df = crypto_union_df
            else:
                partition_df = partition_df.unionByName(crypto_union_df, allowMissingColumns=True)

        # Future asset types can be added here
        return partition_df


    def _enforce_required_columns(self, df: DataFrame, required_columns: List[str]) -> DataFrame:
        """Enforce required columns by selecting available and adding missing as nulls."""
        if not required_columns:
            return df

        # Select available columns
        available = [c for c in required_columns if c in df.columns]
        if available:
            df = df.select(*available)

        # Add missing columns as null
        for col_name in required_columns:
            if col_name not in df.columns:
                df = df.withColumn(col_name, lit(None))

        return df
