"""
Data Source Manager for the Cohorting Engine.

Registry for data source classes. Each data source implements its own read logic.
"""

from typing import List, Dict, Any
from src.utils.spark_utils import *


class DataSourceManager:
    """
    Registry for data source classes.

    Provides factory pattern to instantiate data source classes.
    Each data source class implements its own read logic.
    """

    def __init__(self, spark: SparkSession, config: Dict[str, Any], logger):
        self.spark = spark
        self.config = config
        self.logger = logger
        self._class_registry: Dict[str, Any] = {}

    def register_data_source_class(self, class_name: str, cls: Any) -> None:
        """Register a data source class by name."""
        self._class_registry[class_name] = cls
        self.logger.info(f"Registered data source class: {class_name}")

    def get_data_source_instance(self, class_name: str) -> Any:
        """Get an instance of a registered data source class."""
        if class_name not in self._class_registry:
            available = list(self._class_registry.keys())
            raise ValueError(f"Data source class '{class_name}' is not registered. Available: {available}")

        cls = self._class_registry[class_name]
        return cls(self.spark, self.config, self.logger)

    def get_available_data_sources(self) -> List[str]:
        """Get list of registered data source class names."""
        return list(self._class_registry.keys())
