"""
Base Schema Factory for Customer Risk Rating cohort.

This is the base class for all schema factories that handle the transformation
of customer risk rating data for different sink types.
"""
from typing import Dict, Any, List
from abc import ABC, abstractmethod
from pyspark.sql import DataFrame
from src.utils.custom_logger import get_logger


class BaseSchemaFactory(ABC):
    """Base schema factory for Customer Risk Rating transformations."""

    def __init__(self, logger=None):
        """
        Initialize the schema factory.

        Args:
            logger: Logger instance
        """
        self.logger = logger or get_logger()

    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """
        Get the schema for this sink type.

        Returns:
            Dictionary containing the schema
        """
        pass

    @abstractmethod
    def transform_data(self, df: DataFrame, cohort_id: str, date: str, hour: str) -> DataFrame:
        """
        Transform DataFrame for the specific sink type.

        Args:
            df: Input DataFrame
            cohort_id: Cohort identifier
            date: Processing date
            hour: Processing hour

        Returns:
            Transformed DataFrame for the sink
        """
        pass

    def validate_schema(self, df: DataFrame) -> bool:
        """
        Validate DataFrame against the schema.

        Args:
            df: DataFrame to validate

        Returns:
            True if valid, False otherwise
        """
        try:
            schema = self.get_schema()
            required_columns = list(schema.keys())

            df_columns = set(df.columns)
            missing_columns = set(required_columns) - df_columns

            if missing_columns:
                self.logger.error(f"Missing required columns for schema: {missing_columns}")
                return False

            self.logger.info("Schema validation passed")
            return True

        except Exception as e:
            self.logger.error(f"Error validating schema: {e}")
            return False

