"""Cohort output writer for various destinations."""
from typing import Dict, Any, List
from enum import Enum
from src.utils.spark_utils import *
from src.utils.io_utils import IOUtils
from src.utils.operations import Operations


class SchemaFactoryType(Enum):
    """Schema factory types."""
    CUSTOMER_RISK_RATING_KAFKA = "CustomerRiskRatingKafkaSchema"
    CUSTOMER_RISK_RATING_S3 = "CustomerRiskRatingS3Schema"


SCHEMA_FACTORY_REGISTRY = {
    SchemaFactoryType.CUSTOMER_RISK_RATING_KAFKA: None,
    SchemaFactoryType.CUSTOMER_RISK_RATING_S3: None,
}


class CohortOutputWriter:
    """Writer for cohort outputs."""
    
    def __init__(self, spark: SparkSession, config: Dict[str, Any], logger):
        """Initialize output writer."""
        self.spark = spark
        self.config = config
        self.logger = logger
        self.ops = Operations(spark)
        self.io_utils = IOUtils(spark, config)
    
    def _transform_viewed_ticker_cohort(self, df: DataFrame) -> DataFrame:
        """Transform viewed_ticker_not_invested_24h cohort by selecting top-N symbols per asset_type.

        N is driven by config: config["cohorting"]["top_page_visit_assets_num"].
        The output contains comma-separated symbols per asset_type and does not include view_count.
        """
        # Read top-N from config (defaults to 1)
        top_n = int(self.config.get("cohorting", {}).get("top_page_visit_assets_num", 1))

        # Prepare base with required columns if present
        select_cols = [c for c in ["user_id", "asset_type", "asset_symbol", "view_count", "account_id"] if c in df.columns]
        base = df.select(*select_cols).distinct()

        # Ensure view_count exists for ranking
        if "view_count" not in base.columns:
            base = base.withColumn("view_count", lit(1))

        # Rank assets per user_id, asset_type by view_count desc, then asset_symbol asc
        w = Window.partitionBy("user_id", "asset_type").orderBy(F.desc("view_count"), F.asc("asset_symbol"))
        ranked = base.withColumn("rn", F.row_number().over(w)).filter(F.col("rn") <= F.lit(top_n))

        # Aggregate chosen symbols to comma-separated strings per user_id, asset_type
        symbols_df = (
            ranked.groupBy("user_id", "asset_type")
                  .agg(F.array_join(F.collect_set("asset_symbol"), ",").alias("symbols"))
        )

        # Pivot to columns for the known asset types
        sym_pivot = (
            symbols_df.groupBy("user_id")
                      .pivot("asset_type", ["global_stocks", "crypto_currency", "crypto_future"])
                      .agg(F.first("symbols"))
        )

        # Build base output with user_id and account_id (if present)
        out_base_cols = [c for c in ["user_id", "account_id"] if c in df.columns]
        out = df.select(*out_base_cols).distinct().join(sym_pivot, "user_id", "left")

        # Rename columns to legacy names and return
        return (
            out
            .withColumnRenamed("global_stocks", "Global Stocks Page Visits List")
            .withColumnRenamed("crypto_currency", "Crypto Currency Page Visits List")
            .withColumnRenamed("crypto_future", "Crypto Future Page Visits List")
        )
    
    def _transform_held_assets_cohort_data(self, df: DataFrame, max_segments: int = 4) -> DataFrame:
        """Segmented held symbols per asset_type using config-limited column length."""
        max_len = int(self.config.get("cohorting", {}).get("clevertap_max_char_limit"))
        base_cols = [c for c in ["account_id", "user_id", "asset_type", "asset_symbol"] if c in df.columns]
        base_df = df.select(*base_cols).dropna(subset=["account_id", "user_id"]).distinct()
        grouped = base_df.groupBy("account_id", "user_id", "asset_type").agg(
            F.sort_array(F.collect_set("asset_symbol")).alias("symbols_arr")
        )
        def _pack_segments(symbols: list) -> list:
            if symbols is None:
                return []
            segments = []
            current = ""
            for sym in symbols:
                if sym is None: continue
                s = str(sym)
                if not s: continue
                if current == "":
                    current = s if len(s) <= max_len else s[:max_len]
                    continue
                proposed_len = len(current) + 1 + len(s)
                if proposed_len <= max_len:
                    current = current + "," + s
                else:
                    segments.append(current)
                    current = s if len(s) <= max_len else s[:max_len]
            if current != "":
                segments.append(current)
            return segments[:max_segments]
        pack_udf = F.udf(_pack_segments, ArrayType(StringType()))
        segmented = grouped.withColumn("segments", pack_udf(F.col("symbols_arr")))
        def _extract_segment_columns(filtered: DataFrame, prefix: str) -> DataFrame:
            seg_cols = [
                F.when(F.size(F.col("segments")) > i, F.col("segments").getItem(i))
                .otherwise(F.lit(None).cast(StringType()))
                .alias(f"{prefix}_{i+1}")
                for i in range(max_segments)
            ]
            return filtered.select("account_id", "user_id", *seg_cols).distinct()
        gss = segmented.filter(F.col("asset_type") == F.lit("global_stocks"))
        gss = _extract_segment_columns(gss, "global_stocks_held_asset_symbols")
        crypto = segmented.filter(F.col("asset_type") == F.lit("crypto_currency"))
        crypto = _extract_segment_columns(crypto, "crypto_currency_held_asset_symbols")
        out = gss.join(crypto, on=["account_id", "user_id"], how="full_outer")
        final_cols = [
            "user_id", "account_id",
            *[f"global_stocks_held_asset_symbols_{i+1}" for i in range(max_segments)],
            *[f"crypto_currency_held_asset_symbols_{i+1}" for i in range(max_segments)],
        ]
        for c in final_cols:
            if c not in out.columns:
                out = out.withColumn(c, lit("0"))

        # Fill all null values in segment columns with "0"
        segment_cols = [c for c in final_cols if c not in ["user_id", "account_id"]]
        for seg_col in segment_cols:
            out = out.withColumn(seg_col, F.coalesce(F.col(seg_col), F.lit("0")))
        
        return out.select(*final_cols).distinct()
    
    def write_cohort_to_default_location(self, cohort_id: str, cohort_data: DataFrame, date: str) -> str:
        """
        Write cohort data to default individual cohort output path.

        Args:
            cohort_id: Unique identifier for the cohort
            cohort_data: DataFrame with cohort data
            date: Processing date

        Returns:
            Path where data was written
        """
        try:
            bucket_path = self.config.get("bucket_path")
            cohorting_cfg = self.config.get("cohorting", {})
            individual_output_template = cohorting_cfg.get("individual_cohort_output_path")
            individual_output_path = f"{bucket_path}/{individual_output_template.format(cohort_id=cohort_id, date=date)}"
            
            data_count = cohort_data.count()
            self.logger.info(f"Writing {cohort_id} cohort data ({data_count} records) to default location - {individual_output_path}")
            self.io_utils.write_csv_file(cohort_data, individual_output_path)
            self.logger.info(f"{cohort_id} cohort data written to: {individual_output_path}")
            
            # Additional write for viewed_ticker_not_invested_24h: clevertap_cohort_clear_path with columns set to 0
            if cohort_id == "viewed_ticker_not_invested_24h":
                clear_df = cohort_data.withColumnRenamed("user_id", "identity").drop("account_id")
                for col_name in [c for c in clear_df.columns if c != "identity"]:
                    clear_df = clear_df.withColumn(col_name, lit(0))

                clear_template = cohorting_cfg.get('clevertap_cohort_clear_path')
                clear_path = f"{self.config.get('bucket_path')}/{clear_template}"
                self.io_utils.write_csv_file(clear_df, clear_path)
                self.logger.info(f"{cohort_id} clear data written to: {clear_path}")
            
            return individual_output_path
            
        except Exception as e:
            self.logger.error(f"Error writing {cohort_id} to default location: {e}")
            raise

    def write_individual_cohort_output(self, cohort_id: str, cohort_data: DataFrame, date: str, hour: str, data_sink: str) -> str:
        """
        Write individual cohort output to the configured sink.

        Args:
            cohort_id: Unique identifier for the cohort
            cohort_data: DataFrame with cohort data
            date: Processing date
            hour: Processing hour
            data_sink: Data sink configuration (required, e.g., "S3.customer_risk_rating", "Kafka.cohort_topic_a")

        Returns:
            Path/identifier where data was written
        """
        try:
            data_count = cohort_data.count()
            self.logger.info(f"Writing cohort {cohort_id} data: {data_count} records to sink: {data_sink}")

            # Parse sink configuration (format: "SINK_TYPE.sink_name")
            sink_parts = data_sink.split(".", 1)
            if len(sink_parts) != 2:
                raise ValueError(f"Invalid data_sink format: {data_sink}. Expected format: 'SINK_TYPE.sink_name'")

            sink_type, sink_name = sink_parts

            # Route to appropriate sink writer
            if sink_type.upper() == "S3":
                return self._write_to_s3(cohort_id, cohort_data, date, hour, sink_name)
            else:
                # Unknown sink type - use default S3
                self.logger.warning(f"Sink type {sink_type} not yet implemented. Using default S3 sink.")
                return self._write_to_s3(cohort_id, cohort_data, date, hour)

        except Exception as e:
            self.logger.error(f"Error writing cohort {cohort_id} data: {e}")
            raise
    
    def _write_to_s3(self, cohort_id: str, cohort_data: DataFrame, date: str, hour: str, sink_name: str) -> str:
        """Write cohort data to S3."""
        bucket_path = self.config.get("bucket_path")

        # Use specific sink configuration (sink_name is always provided)
        sink_config = self.config.get("sink_configurations", {}).get("S3", {}).get("sinks", {}).get(sink_name, {})
        s3_relative_path = sink_config.get("s3_path", f"cohorting/individual_cohorts/{date}/{hour}/{cohort_id}/")
        s3_path = f"{bucket_path}/{s3_relative_path.format(date=date, hour=hour, cohort_id=cohort_id)}"

        # Use CSV format for all cohort data
        self.logger.info("Printing cohort data")
        cohort_data.limit(10).show(truncate=False)
        # self.io_utils.write_csv_file(cohort_data, s3_path)

        self.logger.info(f"Cohort {cohort_id} data written to S3: {s3_path}")
        return s3_path
    

    def _get_schema_factory(self, factory_name: str):
        """Get schema factory class by name using enum-based registry."""
        # Initialize registry if not already done
        self._initialize_schema_factory_registry()

        # Find the enum value for this factory name
        schema_factory_type = None
        for enum_value in SchemaFactoryType:
            if enum_value.value == factory_name:
                schema_factory_type = enum_value
                break

        if schema_factory_type is None:
            raise ValueError(f"Unknown schema factory: {factory_name}. Available: {[e.value for e in SchemaFactoryType]}")

        # Get the factory class from registry
        factory_class = SCHEMA_FACTORY_REGISTRY.get(schema_factory_type)
        if factory_class is None:
            raise ValueError(f"Schema factory not registered: {factory_name}")

        return factory_class

    def _initialize_schema_factory_registry(self):
        """Initialize the schema factory registry with actual classes."""
        # Only initialize if not already done
        if SCHEMA_FACTORY_REGISTRY[SchemaFactoryType.CUSTOMER_RISK_RATING_KAFKA] is not None:
            return


    @classmethod
    def register_schema_factory(cls, factory_type: SchemaFactoryType, factory_class):
        """Register new schema factory."""
        SCHEMA_FACTORY_REGISTRY[factory_type] = factory_class

    @classmethod
    def get_available_schema_factories(cls):
        """Get list of available schema factory names."""
        return [factory_type.value for factory_type in SchemaFactoryType]

    def write_clevertap_cohort_upload(self, cohort_results: list, processing_date: str, processing_hour: str):
        """
        Join all cohort data on user_id, transform for Clevertap, and write to S3.
        
        Args:
            cohort_results: List of tuples (cohort_id, cohort_data DataFrame)
            processing_date: Processing date
            processing_hour: Processing hour
        """
        self.logger.info("Generating Clevertap cohort upload file")

        try:
            # Join all cohort DataFrames (transformation already applied in cohorting_job)
            joined_df = self._join_cohort_dataframes(cohort_results)
            if joined_df is None:
                self.logger.warning("No valid cohort data found for Clevertap upload")
                return

            # Transform: remove account_id, rename user_id to identity, add metadata
            transformed_df = joined_df.distinct()
            if "account_id" in transformed_df.columns:
                transformed_df = transformed_df.drop("account_id")
            
            transformed_df = (transformed_df
                .withColumnRenamed("user_id", "identity")
                .withColumn("processing_date", lit(processing_date))
                .withColumn("processing_hour", lit(processing_hour))
            )

            # Write to S3
            cohorting_cfg = self.config.get("cohorting", {})
            upload_prefix = cohorting_cfg.get('clevertap_cohort_upload_path')
            if not upload_prefix:
                raise ValueError("Missing config['cohorting']['clevertap_cohort_upload_path']")
            partition_s3_path = f"{self.config.get('bucket_path')}/{upload_prefix}dt={processing_date}/"
            
            self.io_utils.write_csv_file(transformed_df, partition_s3_path)
            self.logger.info(f"Clevertap upload: {transformed_df.count()} users, {len(transformed_df.columns)} columns -> {partition_s3_path}")

        except Exception as e:
            self.logger.error(f"Error generating Clevertap cohort upload: {e}")
            raise

    def _join_cohort_dataframes(self, cohort_results: list) -> DataFrame:
        """Join all cohort DataFrames on user_id using full outer join."""
        if not cohort_results:
            return None

        joined_df = None
        for cohort_id, cohort_data in cohort_results:
            if cohort_data is None or cohort_data.count() == 0:
                self.logger.warning(f"Cohort {cohort_id} has no data, skipping")
                continue

            joined_df = cohort_data if joined_df is None else joined_df.join(cohort_data, on=["user_id"], how="full_outer")

        return joined_df
