from src.utils.spark_utils import *
from src.jobs.cohorting.config_readers.cohort_config_reader import CohortConfigReader
from src.jobs.cohorting.writers.cohort_output_writer import CohortOutputWriter
from src.jobs.cohorting.processors.base_cohort_processor import BaseCohortProcessor
from src.jobs.cohorting.engine.parameter_registry import ParameterRegistry
from src.jobs.cohorting.processors.corporate_action_dividends_processor import CorporateActionDividendsCohortProcessor
from enum import Enum
from typing import Dict, Type, Tuple, Any


class ProcessorType(Enum):
    """Cohort processor types."""
    BASE_COHORT_PROCESSOR = "BaseCohortProcessor"
    CORPORATE_ACTION_DIVIDENDS = "CorporateActionDividendsCohortProcessor"

    def is_custom_processor(self) -> bool:
        """Check if processor is custom."""
        return self != ProcessorType.BASE_COHORT_PROCESSOR


PROCESSOR_REGISTRY: Dict[ProcessorType, Type] = {
    ProcessorType.BASE_COHORT_PROCESSOR: BaseCohortProcessor,
    ProcessorType.CORPORATE_ACTION_DIVIDENDS: CorporateActionDividendsCohortProcessor
}


class CohortingJob:
    def __init__(self, config: dict, **kwargs):
        """Initialize cohorting job."""
        self.logger = get_logger()
        self.config = config
        
        self.spark_utils = SparkUtils("cohorting_job")
        self.spark = self.spark_utils.create_spark_session()
        self.io_utils = IOUtils(self.spark, self.config)
        
        self.bucket_path = self.config.get("bucket_path")
        self.offset = self.config.get("offset")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.logger.info("Offset initialised successfully with t_1: {}, t_2: {}".format(self.t_1, self.t_2))

        self.schema_factory = None
        
        self.parameter_registry = ParameterRegistry(self.spark, self.config, self.logger)
        self.cohort_config_reader = CohortConfigReader(self.spark, self.config, self.logger, self.parameter_registry)
    
    def get_cohort_configurations(self):
        """Fetch active cohorts and load parameters."""
        try:
            self.logger.info("Fetching cohort configurations from MongoDB")
            cohorts_df = self.cohort_config_reader.get_active_cohorts(self.t_1)

            if cohorts_df.count() == 0:
                self.logger.warning("No active cohorts found in MongoDB")
                return []

            self.logger.info("Pre-fetching parameter configurations for all cohorts")
            self.cohort_config_reader.get_all_parameters_for_cohorts(cohorts_df)
            self.logger.info(f"Loaded {len(self.parameter_registry._parameter_cache)} parameter configurations into cache")

            cohorts = []
            for row in cohorts_df.collect():
                cohort_processor = row.cohort_processor if hasattr(row, "cohort_processor") else "BaseCohortProcessor"

                cohort_config = {
                    "cohort_id": row["cohort_id"],
                    "cohort_name": row["name"],
                    "start_date": row["start_date"],
                    "end_date": row["end_date"],
                    "data_sinks": row["data_sinks"] if hasattr(row, "data_sinks") else [],
                    "assimilation_schedule": row["assimilation_schedule"],
                    "cohort_processor": cohort_processor,
                    "data_source_classes": row.data_source_classes if hasattr(row, "data_source_classes") else [],
                    "parameters": row["parameters"] if hasattr(row, "parameters") else [],
                    "cohort_logic": row["cohort_logic"] if hasattr(row, "cohort_logic") else {},
                    "created_at": row["created_at"],
                    "updated_at": row["updated_at"]
                }
                cohorts.append(cohort_config)

            self.logger.info(f"Retrieved {len(cohorts)} active cohort configurations")
            return cohorts

        except Exception as e:
            self.logger.error(f"Error fetching cohort configurations: {e}")
            return []
    
    
    def _process_cohort(self, cohort_config, processing_date: str, processing_hour: str):
        """Process cohort and write to data sinks."""
        cohort_id = cohort_config["cohort_id"]
        cohort_processor = cohort_config["cohort_processor"]

        self.logger.info(f"Processing cohort: {cohort_id} with processor: {cohort_processor}")

        try:
            processor_class, processor_type = self._get_processor_info(cohort_processor)
            processor = processor_class(self.spark, self.config, self.logger, self.parameter_registry)

            cohort_params = self._get_cached_cohort_parameters(cohort_config)
            cohort_data = processor.process_cohort(cohort_id, cohort_config, cohort_params)

            output_writer = CohortOutputWriter(self.spark, self.config, self.logger)
            
            # Apply transformation for viewed_ticker_not_invested_24h cohort once
            if cohort_id == "viewed_ticker_not_invested_24h":
                cohort_data = output_writer._transform_viewed_ticker_cohort(cohort_data)
            elif cohort_id == "gss_and_crypto_held_assets":
                cohort_data = output_writer._transform_held_assets_cohort_data(cohort_data)
            
            data_sinks = cohort_config.get("data_sinks", [])
            if data_sinks:
                output_paths = []
                for data_sink in data_sinks:
                    try:
                        output_path = output_writer.write_individual_cohort_output(cohort_id, cohort_data, processing_date, processing_hour, data_sink)
                        output_paths.append(output_path)
                    except Exception as e:
                        self.logger.error(f"Error writing to sink {data_sink}: {e}")
                self.logger.info(f"{cohort_id} cohort data written to configured sinks: {output_paths}")
            
            try:
                output_writer.write_cohort_to_default_location(cohort_id, cohort_data, processing_date)
            except Exception as e:
                self.logger.warning(f"Error writing to default location for {cohort_id}: {e}")
            
            return cohort_data

        except Exception as e:
            self.logger.error(f"Error processing cohort {cohort_id}: {e}")
            raise

    def _generate_cohort_matrix(self, cohort_results: list, processing_date: str, processing_hour: str):
        """Generate cohort membership matrix."""
        self.logger.info("Generating cohort membership matrix")

        try:
            if not cohort_results:
                self.logger.warning("No cohort results to process for matrix generation")
                return

            cohort_ids = []
            matrix_df = None

            for cohort_id, cohort_data in cohort_results:
                cohort_ids.append(cohort_id)

                if cohort_data is None or cohort_data.count() == 0:
                    self.logger.warning(f"Cohort {cohort_id} has no data, skipping")
                    continue

                cohort_members = cohort_data.withColumn(cohort_id, lit(True))

                if matrix_df is None:
                    matrix_df = cohort_members
                else:
                    matrix_df = matrix_df.join(
                        cohort_members.select("account_id", "user_id", cohort_id),
                        on=["account_id", "user_id"],
                        how="full_outer"
                    )

            if matrix_df is None:
                self.logger.warning("No valid cohort data found for matrix generation")
                return

            matrix_df = matrix_df.distinct()

            for cohort_id in cohort_ids:
                if cohort_id in matrix_df.columns:
                    matrix_df = matrix_df.withColumn(
                        cohort_id,
                        when(col(cohort_id).isNotNull(), col(cohort_id)).otherwise(False)
                    )

            matrix_df = matrix_df.withColumn("processing_date", lit(processing_date))
            matrix_df = matrix_df.withColumn("processing_hour", lit(processing_hour))

            base_columns = ["account_id", "user_id"]
            cohort_columns = [cid for cid in cohort_ids if cid in matrix_df.columns]
            metadata_columns = ["processing_date", "processing_hour"]

            final_columns = base_columns + cohort_columns + metadata_columns
            existing_columns = [col for col in final_columns if col in matrix_df.columns]
            matrix_df = matrix_df.select(*existing_columns)

            cohorting_cfg = self.config.get("cohorting", {})
            matrix_template = cohorting_cfg.get("cohort_matrix_output_path")
            s3_path = f"{self.bucket_path}/{matrix_template.format(date=processing_date, hour=processing_hour)}"
            self.io_utils.write_csv_file(matrix_df, s3_path)

            self.logger.info(f"Cohort matrix generated and written to: {s3_path}")
            self.logger.info(f"Matrix contains {len(cohort_ids)} cohorts with {matrix_df.count()} users")

        except Exception as e:
            self.logger.error(f"Error generating cohort matrix: {e}")
            raise

    def _get_processor_info(self, processor_name: str) -> Tuple[Type[Any], ProcessorType]:
        """Get processor class and type."""
        if not processor_name:
            processor_type = ProcessorType.BASE_COHORT_PROCESSOR
        else:
            try:
                processor_type = ProcessorType(processor_name)
            except ValueError:
                self.logger.warning(f"Unknown processor '{processor_name}', using default BaseCohortProcessor")
                processor_type = ProcessorType.BASE_COHORT_PROCESSOR

        processor_class = PROCESSOR_REGISTRY.get(processor_type, BaseCohortProcessor)
        return processor_class, processor_type

    def _get_cached_cohort_parameters(self, cohort_config: Dict[str, Any]) -> Dict[str, Any]:
        """Get cohort parameters from cache."""
        try:
            parameters_list = cohort_config.get("parameters", [])
            if not parameters_list:
                return {"parameters": []}

            parameters = []
            for param in parameters_list:
                if isinstance(param, dict) and "parameter_id" in param:
                    param_id = param["parameter_id"]
                    if param_id in self.parameter_registry._parameter_cache:
                        parameters.append(self.parameter_registry._parameter_cache[param_id].copy())

            return {"parameters": parameters}

        except Exception as e:
            self.logger.error(f"Error getting cached parameters: {e}")
            return {"parameters": []}

    def run(self):
        """Execute cohorting job."""
        self.logger.info("Starting cohorting job")

        try:
            processing_date = self.t_1
            processing_hour = DateUtils.get_jkt_timestamp(0).strftime("%H")

            self.logger.info(f"Processing date: {processing_date}, hour: {processing_hour}")

            cohort_configs = self.get_cohort_configurations()

            if not cohort_configs:
                self.logger.warning("No active cohorts found, exiting gracefully")
                return

            cohort_results = []
            for cohort_config in cohort_configs:
                cohort_id = cohort_config["cohort_id"]
                self.logger.info(f"Processing cohort: {cohort_id}")
                cohort_data = self._process_cohort(cohort_config, processing_date, processing_hour)
                cohort_results.append((cohort_id, cohort_data))

            self._generate_cohort_matrix(cohort_results, processing_date, processing_hour)

            output_writer = CohortOutputWriter(self.spark, self.config, self.logger)
            output_writer.write_clevertap_cohort_upload(cohort_results, processing_date, processing_hour)

            self.logger.info("Cohorting job completed successfully")

        except Exception as e:
            self.logger.error(f"Cohorting job failed: {e}")
            raise