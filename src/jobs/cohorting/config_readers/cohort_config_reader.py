"""Cohort configuration reader."""
from typing import Dict, Any, List
from src.utils.spark_utils import *
from src.jobs.cohorting.engine.parameter_registry import ParameterRegistry


class CohortConfigReader:
    """Reader for cohort configs from MongoDB."""
    
    def __init__(self, spark: SparkSession, config: Dict[str, Any], logger=None, parameter_registry=None):
        """Initialize config reader."""
        self.spark = spark
        self.config = config
        self.logger = logger or get_logger()
        self.io_utils = IOUtils(spark, config)

        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.logger.info("Cohort Config Reader successfully with t_1: {}, t_2: {}".format(self.t_1, self.t_2))

        self.mongo_config = self.config.get("data_store", {}).get("reporting_mongo", {})
        self.cohorts_collection = "pluang_data_eng.cohorting_engine_user_cohorts"
        self.parameters_collection = "pluang_data_eng.cohorting_engine_parameters"
        
        self.parameter_registry = parameter_registry or ParameterRegistry(spark, config, logger)

        self.logger.info("CohortConfigReader initialized")
    
    def get_active_cohorts(self, t_1: str) -> DataFrame:
        """Get all active cohorts from MongoDB that are valid for the given date."""
        try:
            self.logger.debug(f"Fetching active cohorts from MongoDB for date {t_1}")

            # MongoDB aggregation pipeline to get active cohorts with all necessary fields
            pipeline = '[{"$match": {"is_active": true, "start_date": {"$lte": "%s"}, "end_date": {"$gte": "%s"}}},{"$sort":{"start_date":1}}]' % (t_1, t_1)
            self.logger.debug(f"Using cohorts pipeline: {pipeline}")

            self.logger.debug("Calling io_utils.read_from_mongodb for cohorts")
            df = self.io_utils.read_from_mongodb(
                mongo_config=self.mongo_config,
                collection=self.cohorts_collection,
                pipeline=pipeline
            )

            self.logger.debug(f"MongoDB cohorts query returned DataFrame with {df.count()} rows")
            self.logger.info(f"Retrieved {df.count()} active cohorts with parameters")
            return df

        except Exception as e:
            self.logger.error(f"Error fetching active cohorts: {e}")
            # Return empty DataFrame with expected schema
            return self._create_empty_cohorts_dataframe()

    def get_all_parameters_for_cohorts(self, cohorts_df: DataFrame) -> Dict[str, Dict[str, Any]]:
        """Extract parameter IDs from cohorts and fetch configurations in single query."""
        try:
            parameter_ids = set()
            rows = cohorts_df.collect()

            for row in rows:
                # Handle case where parameters field might not exist or is None
                if not hasattr(row, "parameters"):
                    continue
                    
                parameters_list = row["parameters"]
                
                # Skip if parameters_list is None or empty
                if not parameters_list:
                    continue
                    
                for param in parameters_list:
                    param_id = None
                    
                    if hasattr(param, 'parameter_id'):
                        param_id = param.parameter_id
                    elif isinstance(param, dict) and "parameter_id" in param:
                        param_id = param["parameter_id"]
                    else:
                        continue
                        
                    if param_id:
                        parameter_ids.add(param_id)

            if not parameter_ids:
                self.logger.info("No parameters found in any cohorts (custom processors don't use parameters)")
                return {}

            self.logger.info(f"Extracted {len(parameter_ids)} unique parameter IDs")
            param_configs = self.get_parameter_configs_by_ids(list(parameter_ids))
            return param_configs

        except Exception as e:
            self.logger.error(f"Error getting all parameters for cohorts: {e}")
            return {}


    def get_parameter_configs_by_ids(self, parameter_ids: List[str]) -> Dict[str, Any]:
        """Get parameter configurations for given parameter IDs."""
        try:
            if not parameter_ids:
                return {}

            param_configs = self.parameter_registry.get_parameter_configs(parameter_ids)
            self.logger.info(f"Retrieved {len(param_configs)} parameter configurations")
            return param_configs

        except Exception as e:
            self.logger.error(f"Error fetching parameter configurations: {e}")
            return {}
    
    def _create_empty_cohorts_dataframe(self) -> DataFrame:
        """Create empty DataFrame with cohort schema."""
        # Create empty DataFrame with default values
        empty_data = {
            "cohort_id": "",
            "name": "",
            "description": "",
            "start_date": "",
            "end_date": "",
            "data_sinks": [],
            "assimilation_schedule": "",
            "cohort_processor": "BaseCohortProcessor",  # Default to BaseCohortProcessor
            "data_sources": [],
            "parameters": [],
            "cohort_logic": {"select_clause": "", "where_clause": "", "group_by_columns": []},
            "created_at": None,
            "updated_at": None
        }

        return self.spark.createDataFrame([empty_data], schema=StructType([
            StructField("cohort_id", StringType(), True),
            StructField("name", StringType(), True),
            StructField("description", StringType(), True),
            StructField("start_date", StringType(), True),
            StructField("end_date", StringType(), True),
            StructField("data_sinks", ArrayType(StringType()), True),
            StructField("assimilation_schedule", StringType(), True),
            StructField("cohort_processor", StringType(), True),
            StructField("data_sources", ArrayType(StringType()), True),
            StructField("parameters", ArrayType(
                StructType([
                    StructField("parameter_id", StringType(), True),
                    StructField("join_type", StringType(), True),
                    StructField("weight", DoubleType(), True)
                ])
            ), True),
            StructField("cohort_logic", StructType([
                StructField("select_clause", StringType(), True),
                StructField("where_clause", StringType(), True),
                StructField("group_by_columns", ArrayType(StringType()), True)
            ]), True),
            StructField("created_at", TimestampType(), True),
            StructField("updated_at", TimestampType(), True)
        ]))