from typing import Dict, List, Any, Optional
from src.utils.spark_utils import *


class SQLQueryEngine:
    """Spark SQL engine for DataFrame queries."""

    def __init__(self, spark: SparkSession, logger):
        """Initialize SQL engine."""
        self.spark = spark
        self.logger = logger

    def execute_sql(self, df: DataFrame, query: Dict[str, Any],
                    variable_substitutions: Optional[Dict[str, str]] = None) -> DataFrame:
        """Execute SQL query on DataFrame."""
        try:
            return self._execute_config_query(df, query, variable_substitutions)

        except Exception as e:
            self.logger.error(f"Error executing SQL: {e}")
            raise

    def _execute_config_query(self, df: DataFrame, query_config: Dict[str, Any],
                             variable_substitutions: Optional[Dict[str, str]] = None) -> DataFrame:
        """Execute config-based query."""
        # Apply variable substitutions to all conditions
        config = query_config.copy()
        if variable_substitutions:
            for key in ["where_condition", "having_condition"]:
                if config.get(key):
                    for var, value in variable_substitutions.items():
                        config[key] = config[key].replace(var, f"'{value}'")

        # Create temp view
        temp_view_name = f"temp_view_{hash(str(df.columns)) % 1000000}"
        df.createOrReplaceTempView(temp_view_name)

        # Build query components
        query_parts = []

        # SELECT clause
        if config.get("select_columns"):
            select_cols = config["select_columns"]
            # Add aggregations if present
            if config.get("aggregations"):
                for alias, expr in config["aggregations"].items():
                    select_cols.append(f"{expr} as {alias}")
            select_clause = ", ".join(select_cols)
        elif config.get("group_by_columns"):
            # For GROUP BY, include group columns and aggregations
            select_cols = config["group_by_columns"].copy()
            if config.get("aggregations"):
                for alias, expr in config["aggregations"].items():
                    select_cols.append(f"{expr} as {alias}")
            select_clause = ", ".join(select_cols)
        else:
            select_clause = "*"

        query_parts.append(f"SELECT {select_clause}")
        query_parts.append(f"FROM {temp_view_name}")

        # WHERE clause
        if config.get("where_condition"):
            query_parts.append(f"WHERE {config['where_condition']}")

        # GROUP BY clause
        if config.get("group_by_columns"):
            group_by_clause = ", ".join(config["group_by_columns"])
            query_parts.append(f"GROUP BY {group_by_clause}")

        # HAVING clause
        if config.get("having_condition"):
            query_parts.append(f"HAVING {config['having_condition']}")

        # Build final query
        sql_query = " ".join(query_parts)
        self.logger.info(f"Executing SQL: {sql_query}")

        result_df = self.spark.sql(sql_query)
        self.logger.info(f"Query executed. Rows before: {df.count()}, after: {result_df.count()}")

        return result_df

    def join_parameter_dataframes(self, parameter_dfs: Dict[str, DataFrame],
                                  join_configs: List[Dict[str, Any]]) -> DataFrame:
        try:
            if not parameter_dfs:
                raise ValueError("No parameter DataFrames provided for joining")

            if not join_configs:
                raise ValueError("No join configurations provided")

            self.logger.info(f"Joining {len(parameter_dfs)} parameter DataFrames")

            # Start with the first DataFrame
            base_param_id = join_configs[0]["parameter_id"]
            if base_param_id not in parameter_dfs:
                raise ValueError(f"Base parameter '{base_param_id}' not found in parameter DataFrames")

            result_df = parameter_dfs[base_param_id]
            self.logger.info(f"Starting join with base DataFrame: {base_param_id}")
            self.logger.info(f"result df = ")
            result_df.show()
            # Apply subsequent joins
            for join_config in join_configs[1:]:
                self.logger.info(f" join config = {join_config}")
                param_id = join_config["parameter_id"]
                join_type = join_config["join_type"]
                join_keys = join_config["join_keys"]

                if param_id not in parameter_dfs:
                    raise ValueError(f"Parameter '{param_id}' not found in parameter DataFrames")

                if not join_keys:
                    raise ValueError(f"No join keys specified for parameter '{param_id}'")

                right_df = parameter_dfs[param_id]

                # Validate join keys exist in both DataFrames
                for key in join_keys:
                    if key not in result_df.columns:
                        raise ValueError(f"Join key '{key}' not found in left DataFrame columns: {result_df.columns}")
                    if key not in right_df.columns:
                        raise ValueError(f"Join key '{key}' not found in right DataFrame columns: {right_df.columns}")

                # Perform the join using column names (this automatically handles duplicate join keys)
                self.logger.info(f"Joining with {param_id} using {join_type} join on keys: {join_keys}")
                result_df = result_df.join(right_df, join_keys, join_type)

                # Log join result
                self.logger.info(f"Join completed. Result has {result_df.count()} rows")

            return result_df

        except Exception as e:
            self.logger.error(f"Error joining parameter DataFrames: {e}")
            raise

    def validate_sql(self, sql: str) -> bool:
        """Validate Spark SQL string syntax."""
        try:
            if not sql or not sql.strip():
                return True

            sql_upper = sql.upper()

            # Basic syntax checks for Spark SQL
            if sql_upper.count('SELECT') > 1:
                self.logger.error("Multiple SELECT statements not supported")
                return False

            if 'UNION' in sql_upper and 'SELECT' not in sql_upper:
                self.logger.error("UNION without SELECT")
                return False

            # Check for balanced parentheses
            if sql.count('(') != sql.count(')'):
                self.logger.error("Unmatched parentheses")
                return False

            # Check for balanced quotes
            if sql.count("'") % 2 != 0:
                self.logger.error("Unmatched single quotes")
                return False

            return True

        except Exception as e:
            self.logger.error(f"SQL validation error: {e}")
            return False

    def get_supported_sql_features(self) -> str:
        """Get supported SQL features."""
        return "Supports standard SQL: functions, operators, joins, window functions, CTEs, variable substitutions"
