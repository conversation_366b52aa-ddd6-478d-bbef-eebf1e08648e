"""Parameter registry for cohorting engine."""

import json
from typing import List, Dict, Any, Optional
from src.utils.spark_utils import *
from src.utils.io_utils import IOUtils


class ParameterRegistry:
    """Parameter definitions registry."""

    def __init__(self, spark: SparkSession, config: Dict[str, Any], logger):
        """Initialize parameter registry."""
        self.spark = spark
        self.config = config
        self.logger = logger
        self.io_utils = IOUtils(spark, config)

        self.mongo_config = config.get("data_store", {}).get("reporting_mongo", {})
        self.parameters_collection = "pluang_data_eng.cohorting_engine_parameters"
        self._parameter_cache: Dict[str, Dict[str, Any]] = {}

    def load_parameter_definitions_from_mongo(self, parameter_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """Load parameters from MongoDB."""
        try:
            self.logger.info(f"Loading parameter definitions for IDs: {parameter_ids}")

            pipeline = '[{"$match": {"parameter_id": {"$in": %s}}}, {"$sort": {"created_at": 1}}]' % json.dumps(parameter_ids)

            df = self.io_utils.read_from_mongodb(
                mongo_config=self.mongo_config,
                collection=self.parameters_collection,
                pipeline=pipeline
            )

            parameters = {}
            for row in df.collect():
                param_id = row["parameter_id"]
                if param_id:
                    data_source_class = row.data_source_class
                    
                    param_config = {
                        "parameter_id": param_id,
                        "name": row.name if hasattr(row, "name") else "",
                        "description": row.description if hasattr(row, "description") else "",
                        "data_source_class": data_source_class,
                        "data_source_params": row.data_source_params if hasattr(row, "data_source_params") else {},
                        "sql_condition": row.sql_condition if hasattr(row, "sql_condition") else "",
                        "join_keys": row.join_keys if hasattr(row, "join_keys") else [],
                        "required_columns": row.required_columns if hasattr(row, "required_columns") else [],
                        "output_columns": row.output_columns if hasattr(row, "output_columns") else [],
                        "group_by_columns": row.group_by_columns if hasattr(row, "group_by_columns") else [],
                        "created_at": row.created_at if hasattr(row, "created_at") else None,
                        "updated_at": row.updated_at if hasattr(row, "updated_at") else None
                    }
                    parameters[param_id] = param_config
                    self._parameter_cache[param_id] = param_config

            self.logger.info(f"Loaded {len(parameters)} parameter definitions")
            self.logger.debug(f"Parameters: {parameters}")
            return parameters

        except Exception as e:
            self.logger.error(f"Error loading parameter definitions: {e}")
            return {}

    def get_parameter_configs(self, parameter_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get parameter configs."""
        try:
            cached_params = {}
            missing_ids = []

            for param_id in parameter_ids:
                if param_id in self._parameter_cache:
                    cached_params[param_id] = self._parameter_cache[param_id]
                else:
                    missing_ids.append(param_id)

            if missing_ids:
                self.logger.info(f"Loading {len(missing_ids)} missing parameters from MongoDB")
                loaded_params = self.load_parameter_definitions_from_mongo(missing_ids)
                cached_params.update(loaded_params)
            else:
                self.logger.info("All parameters found in cache")

            return cached_params

        except Exception as e:
            self.logger.error(f"Error getting parameter configs for {parameter_ids}: {e}")
            return {}

    def validate_parameter_config(self, parameter_config: Dict[str, Any]) -> bool:
        """Validate parameter configuration structure."""
        try:
            required_fields = [
                "parameter_id", "data_source_class",
                "join_keys", "required_columns", "output_columns"
            ]

            for field in required_fields:
                if field not in parameter_config or not parameter_config[field]:
                    if field != "join_keys" or not parameter_config.get("join_keys"):
                        self.logger.error(f"Missing or empty required field '{field}' in parameter config")
                        return False

            # Validate that join_keys contains valid column names
            join_keys = parameter_config.get("join_keys", [])
            required_columns = parameter_config.get("required_columns", [])
            output_columns = parameter_config.get("output_columns", [])

            for key in join_keys:
                if key not in required_columns and key not in output_columns:
                    self.logger.error(f"Join key '{key}' not found in required_columns or output_columns")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Error validating parameter config: {e}")
            return False

    def validate_parameter_dependencies(self, parameter_configs: Dict[str, Dict[str, Any]]) -> bool:
        """Validate parameter dependencies and configurations."""
        try:
            # Validate each parameter individually
            for param_id, config in parameter_configs.items():
                if not self.validate_parameter_config(config):
                    self.logger.error(f"Parameter '{param_id}' failed validation")
                    return False

            # Check for conflicting join keys or data sources if needed
            # (Additional validation logic can be added here)

            return True

        except Exception as e:
            self.logger.error(f"Error validating parameter dependencies: {e}")
            return False


    def clear_cache(self):
        """Clear the parameter cache."""
        self._parameter_cache.clear()
        self.logger.info("Parameter cache cleared")

    def get_cached_parameters(self) -> Dict[str, Dict[str, Any]]:
        """Get all cached parameter configurations."""
        return self._parameter_cache.copy()
