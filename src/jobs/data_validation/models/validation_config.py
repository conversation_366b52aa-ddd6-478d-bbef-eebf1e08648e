"""
Validation configuration data class.
"""

from dataclasses import dataclass, field
from typing import Dict, Any


@dataclass
class ValidationConfig:
    """
    Configuration class for validation operations.
    
    Attributes:
        include_data_quality: Whether to perform data quality checks
        include_join_key_validation: Whether to validate join keys
        include_missing_analysis: Whether to analyze missing data
        include_full_outer_join: Whether to use full outer join
        include_column_validation: Whether to validate column schemas
        custom_options: Additional custom configuration options
    """
    
    include_data_quality: bool = True
    include_join_key_validation: bool = True
    include_missing_analysis: bool = True
    include_full_outer_join: bool = False
    include_column_validation: bool = True
    custom_options: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'include_data_quality': self.include_data_quality,
            'include_join_key_validation': self.include_join_key_validation,
            'include_missing_analysis': self.include_missing_analysis,
            'include_full_outer_join': self.include_full_outer_join,
            'include_column_validation': self.include_column_validation,
            'custom_options': self.custom_options
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ValidationConfig':
        """Create configuration from dictionary."""
        return cls(**config_dict)
    
    def get_custom_option(self, key: str, default: Any = None) -> Any:
        """Get a custom configuration option."""
        return self.custom_options.get(key, default)
    
    def set_custom_option(self, key: str, value: Any) -> None:
        """Set a custom configuration option."""
        self.custom_options[key] = value