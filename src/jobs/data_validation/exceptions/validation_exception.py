"""
Base validation exception class.
"""


class ValidationException(Exception):
    """
    Base exception class for validation operations.
    
    This exception is raised when validation operations fail due to
    data issues, configuration problems, or other validation-related errors.
    """
    
    def __init__(self, message: str, details: dict = None):
        """
        Initialize validation exception.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self) -> str:
        """String representation of the exception."""
        if self.details:
            return f"{self.message} - Details: {self.details}"
        return self.message
    
    def get_details(self) -> dict:
        """Get error details."""
        return self.details