"""
DataValidator interface for validating data between different datasets.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Tuple
from pyspark.sql import DataFrame


class DataValidator(ABC):
    """
    Abstract base class for data validation operations.
    
    This interface defines the contract for validating data between two datasets,
    including data quality checks, join key validation, and missing data analysis.
    """
    
    @abstractmethod
    def validate_data(
        self, 
        df1: DataFrame, 
        df2: DataFrame, 
        join_keys: Tuple[str, str],
        validation_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Perform comprehensive validation between two DataFrames.
        
        Args:
            df1: First DataFrame for comparison
            df2: Second DataFrame for comparison
            join_keys: Tuple of (join_key1, join_key2) column names
            validation_config: Configuration dictionary for validation options
            
        Returns:
            Dictionary containing validation results:
            - matched_records: DataFrame of matching records
            - unmatched_records: DataFrame of non-matching records
            - missing_in_df1: DataFrame of records missing in first dataset
            - missing_in_df2: DataFrame of records missing in second dataset
            - summary: Validation summary statistics
            - quality_metrics: Data quality metrics
        """
        pass
    
    @abstractmethod
    def validate_join_keys(
        self, 
        df1: DataFrame, 
        df2: DataFrame, 
        join_keys: Tuple[str, str]
    ) -> Dict[str, Any]:
        """
        Validate the join keys for data integrity.
        
        Args:
            df1: First DataFrame
            df2: Second DataFrame
            join_keys: Tuple of (join_key1, join_key2) column names
            
        Returns:
            Dictionary containing join key validation results:
            - is_valid: Boolean indicating if join keys are valid
            - warnings: List of warnings about join keys
            - errors: List of errors found in join keys
        """
        pass
    
    @abstractmethod
    def analyze_data_quality(
        self, 
        df: DataFrame, 
        column_name: str
    ) -> Dict[str, Any]:
        """
        Analyze data quality for a specific column.
        
        Args:
            df: DataFrame to analyze
            column_name: Name of the column to analyze
            
        Returns:
            Dictionary containing quality metrics:
            - null_count: Number of null values
            - unique_count: Number of unique values
            - duplicate_count: Number of duplicate values
            - data_types: Data type information
        """
        pass
    
    @abstractmethod
    def detect_missing_data(
        self, 
        df1: DataFrame, 
        df2: DataFrame, 
        join_keys: Tuple[str, str]
    ) -> Dict[str, DataFrame]:
        """
        Detect missing data between two datasets.
        
        Args:
            df1: First DataFrame
            df2: Second DataFrame
            join_keys: Tuple of (join_key1, join_key2) column names
            
        Returns:
            Dictionary containing missing data DataFrames:
            - missing_in_df1: Records missing in first dataset
            - missing_in_df2: Records missing in second dataset
        """
        pass