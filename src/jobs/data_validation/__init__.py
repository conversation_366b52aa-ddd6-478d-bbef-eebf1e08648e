"""
Data Validation Module

Minimal, factory-based data validation utilities for batch jobs. The primary
entry point is `ForexValidation` for running an end-to-end reconciliation
workflow. Validator strategies are provided via the service submodule.

Example:
    from src.jobs.data_validation import ForexValidation

    job = ForexValidation({
        "env": "local",
        "bucket_path": "s3a://bucket/path",
        "t_1": "2025-09-12",
    })
    job.run()
"""

from .forex_validation import ForexValidation

__version__ = "2.1.0"

__all__ = [
    # Orchestration
    "ForexValidation",
]
