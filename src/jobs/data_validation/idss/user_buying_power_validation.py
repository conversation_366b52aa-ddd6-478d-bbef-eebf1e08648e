#!/usr/bin/env python3
"""
IDSS User Buying Power Validation Script

A data validation tool for Indonesian Stock System (IDSS) that validates
calculated buying power balances against indo_stock_wallet data.
"""

from dataclasses import dataclass
from datetime import datetime
from src.utils.custom_logger import get_logger
from src.utils.s3_paths import S3Paths
from src.utils.spark_utils import *
from src.jobs.data_validation.service.validation_service import DataValidatorService
from src.jobs.data_validation.idss.config.schema_config import load_schema_config


@dataclass
class IDSSBuyingPowerValidationConfig:
    """IDSS buying power validation configuration."""
    include_data_quality: bool = True
    include_missing_analysis: bool = True
    include_field_comparison: bool = True
    target_fields: list[str] = None

    def __post_init__(self):
        if self.target_fields is None:
            self.target_fields = ["balance"]


class IDSSUserBuyingPowerValidation:
    """
    IDSS User Buying Power Validation class that validates calculated buying power balances
    against indo_stock_wallet data.
    """

    def __init__(self, config: dict, **kwargs):
        """
        Initialize IDSS User Buying Power Validation with configuration parameters.

        Args:
            config: Configuration dictionary containing bucket_path, t_1, etc.
            **kwargs: Additional parameters for file paths and options
        """
        self.spark = None
        self.schema_mapper = None
        self.data_validator_service = None
        # Core configuration
        self.config = config
        self.bucket_path = config.get("bucket_path")
        self.t_1 = config["t_1"]

        # File paths with defaults
        self.client_cash_path = kwargs.get("client_cash_path", f"{self.bucket_path}/{S3Paths.s21_rt_client_cash}/dt={self.t_1}")
        self.rt_trade_path = kwargs.get("rt_trade_path", f"{self.bucket_path}/{S3Paths.s21_rt_rt_trade}/dt={self.t_1}")
        self.view_order_path = kwargs.get("view_order_path", f"{self.bucket_path}/{S3Paths.s21_rt_view_order}/dt={self.t_1}")
        self.indo_stock_wallet_path = kwargs.get("indo_stock_wallet_path", f"{self.bucket_path}/{S3Paths.indo_stock_wallet_v2}/dt={self.t_1}")
        self.output_path = kwargs.get("output_path", f"{self.bucket_path}/{S3Paths.data_validation_idss_user_buying_power_validation}/dt={self.t_1}")
        self.output_format = kwargs.get("output_format", "csv")
        self.logger = get_logger(env=self.config.get("env"), tag="IDSS User Buying Power Validation")

        # Spark setup
        self.app_name = "IDSSUserBuyingPowerValidation"
        self.spark_utils = SparkUtils(self.app_name)
        self.spark = self._setup_spark_session()

        self.io_utils = IOUtils(self.spark, self.config)

        # Validation configuration
        self.validation_config = IDSSBuyingPowerValidationConfig()

    def _validate_config(self) -> None:
        """Validate configuration parameters."""

        required_paths = [
            self.client_cash_path,
            self.rt_trade_path,
            self.view_order_path,
            self.indo_stock_wallet_path,
            self.output_path
        ]

        print(required_paths)
        if not all(required_paths):
            raise ValueError("Missing required file paths")

        if self.output_format not in ["csv", "parquet", "json"]:
            raise ValueError(f"Invalid output format: {self.output_format}")

    def _setup_spark_session(self):
        """Create and configure Spark session."""
        self.spark = self.spark_utils.create_spark_session()
        self.schema_mapper=load_schema_config("user_buying_power_validation")
        # Initialize data validator service after spark session is created
        self.data_validator_service = DataValidatorService(self.logger, self.config, self.spark, self.schema_mapper)
        return self.spark

    def _stop_spark_session(self) -> None:
        """Stop Spark session."""
        if self.spark is not None:
            self.spark_utils.stop_spark(self.spark)
            self.spark = None

    def _load_data(self, file_path: str) -> DataFrame:
        """Load data from file path with automatic format detection."""
        try:
            # Handle local files
            if file_path.endswith('.csv'):
                df = self.io_utils.read_csv_file(file_path)
            elif file_path.endswith('.parquet'):
                df = self.io_utils.read_parquet_data(file_path)
            elif file_path.endswith('.json'):
                df = self.io_utils.read_json_data(file_path)
            else:
                df = self.io_utils.read_csv_file(file_path)

            # Log the schema
            self.logger.info("Schema for %s:", file_path)
            self.logger.info("Columns: %s", df.columns)
            return df
        except Exception as e:
            self.logger.error("Failed to load data from %s: %s", file_path, str(e))
            return None

    def _calculate_buying_power_balance(self) -> DataFrame:
        """
        Calculate buying power balance using the SQL logic converted to Spark operations.

        SQL Logic:
        SELECT deposit + ac + sell - buy - ISNULL(buyamount, 0) + ISNULL(sellamount, 0) - ISNULL(bidamount, 0) 
        FROM client_cash a 
        LEFT JOIN (
            SELECT clientid, 
                   SUM(CASE WHEN buysell = 'b' THEN (tradevolume * tradeprice) ELSE 0 END) AS buyamount,
                   SUM(CASE WHEN buysell = 's' THEN (tradevolume * tradeprice) ELSE 0 END) AS sellamount
            FROM rt_Trade 
            WHERE tradedate = CAST(GETDATE() AS date) 
            GROUP BY clientid
        ) b ON a.clientid = b.clientid 
        LEFT JOIN (
            SELECT clientid, 
                   SUM(CASE WHEN buysell = 'b' THEN ((ordervolume - donevolume) * orderprice) ELSE 0 END) AS bidamount
            FROM vieworder 
            WHERE orderdate = CAST(GETDATE() AS date) AND orderstatus IN ('B','O','P') 
            GROUP BY clientid
        ) c ON a.clientid = c.clientid;

        Returns:
            DataFrame with calculated buying power balance for each ClientID
        """
        try:
            self.logger.info("Starting buying power balance calculation...")

            # Load all required datasets
            self.logger.info("Loading input datasets...")
            client_cash_df = self._load_data(self.client_cash_path)  # This is client_cash now
            rt_trade_df = self._load_data(self.rt_trade_path)
            view_order_df = self._load_data(self.view_order_path)

            # Check if any of the dataframes failed to load
            if client_cash_df is None or rt_trade_df is None or view_order_df is None:
                error_msg = "Failed to load one or more required datasets. Job cannot proceed."
                if client_cash_df is None:
                    self.logger.error("Client Cash data failed to load from: %s", self.client_cash_path)
                if rt_trade_df is None:
                    self.logger.error("RT Trade data failed to load from: %s", self.rt_trade_path)
                if view_order_df is None:
                    self.logger.error("View Order data failed to load from: %s", self.view_order_path)
                self.logger.error(error_msg)
                self.logger.error("Gracefully exiting the job due to missing required data.")
                return None

            # Log data counts
            self.logger.info("Loaded %d records from Client_Cash", client_cash_df.count())
            self.logger.info("Loaded %d records from RT_Trade", rt_trade_df.count())
            self.logger.info("Loaded %d records from ViewOrder", view_order_df.count())


            rt_trade_filtered = rt_trade_df.filter(
                col("TradeDate") == self.t_1
            )
            self.logger.info("Loaded %d records from RT_Trade filtered on date %s", rt_trade_filtered.count(), self.t_1)

            # Group by ClientID and calculate buy/sell amounts (volume * price)
            trade_amounts = rt_trade_filtered.groupBy("ClientID").agg(
                sum(when(F.upper(col("BuySell")).startswith("B"), 
                        col("TradeVolume") * col("TradePrice")).otherwise(0)).alias("buyamount"),
                sum(when(F.upper(col("BuySell")).startswith("S"), 
                        col("TradeVolume") * col("TradePrice")).otherwise(0)).alias("sellamount")
            )

            self.logger.info("Calculated trade amounts for %d clients", trade_amounts.count())

            # Step 2: Calculate bid amounts from ViewOrder
            # Filter for current date and order statuses that start with B, O, or P
            view_order_filtered = view_order_df.filter(
                (col("OrderDate") == self.t_1) &
                (col("OrderStatus").startswith("B") |
                 col("OrderStatus").startswith("O") |
                 col("OrderStatus").startswith("P"))
            )

            # Calculate bid amount (only for buy orders) - (ordervolume - donevolume) * orderprice
            bid_amounts = view_order_filtered.filter(
                F.upper(col("BuySell")).startswith("B")
            ).groupBy("ClientID").agg(
                sum((col("OrderVolume") - col("DoneVolume")) * col("OrderPrice")).alias("bidamount")
            )

            self.logger.info("Calculated bid amounts for %d clients", bid_amounts.count())

            # Step 3: Join Client_Cash with trade amounts (LEFT JOIN)
            balance_df = client_cash_df.alias("a").join(
                trade_amounts.alias("b"),
                col("a.ClientID") == col("b.ClientID"),
                "left"
            )

            # Step 4: Join with bid amounts (LEFT JOIN)
            balance_df = balance_df.join(
                bid_amounts.alias("c"),
                col("a.ClientID") == col("c.ClientID"),
                "left"
            )

            # Step 5: Calculate final buying power balance using the SQL formula
            # deposit + ac + sell - buy - ISNULL(buyamount, 0) + ISNULL(sellamount, 0) - ISNULL(bidamount, 0)
            # Note: Assuming client_cash has columns: deposit, ac, sell, buy
            final_balance_df = balance_df.select(
                col("a.ClientID").alias("ClientID"),
                (col("a.deposit") + 
                 col("a.ac") + 
                 col("a.sell") - 
                 col("a.buy") - 
                 F.coalesce(col("b.buyamount"), lit(0)) +
                 F.coalesce(col("b.sellamount"), lit(0)) -
                 F.coalesce(col("c.bidamount"), lit(0))
                 ).alias("calculated_balance"),
                col("a.deposit").alias("deposit"),
                col("a.ac").alias("ac"),
                col("a.sell").alias("sell"),
                col("a.buy").alias("buy"),
                F.coalesce(col("b.buyamount"), lit(0)).alias("buyamount"),
                F.coalesce(col("b.sellamount"), lit(0)).alias("sellamount"),
                F.coalesce(col("c.bidamount"), lit(0)).alias("bidamount")
            )

            self.logger.info("Calculated final buying power balance for %d records", final_balance_df.count())

            # Show sample results for debugging
            self.logger.info("Sample calculated buying power balances:")
            sample_results = final_balance_df.limit(5).collect()
            for i, row in enumerate(sample_results, 1):
                self.logger.info(
                    "  %d. ClientID: %s, Calculated Balance: %s, Deposit: %s, AC: %s, Sell: %s, Buy: %s, Buy Amount: %s, Sell Amount: %s, Bid Amount: %s",
                    i, row.ClientID, row.calculated_balance, row.deposit, row.ac, row.sell, row.buy,
                    row.buyamount, row.sellamount, row.bidamount)

            # Cache the DataFrame to avoid recomputation and return it for further processing
            final_balance_df.cache()
            self.logger.info("Buying power balance calculation completed, DataFrame cached for further processing")

            return final_balance_df

        except Exception as e:
            self.logger.error("Failed to calculate buying power balance: %s", str(e))
            raise

    def _preprocess_data(self) -> DataFrame:
        """
        Preprocessing step: Calculate buying power balance using SQL logic.

        Returns:
            DataFrame with calculated buying power balance for each ClientID, or None if data loading failed
        """
        self.logger.info("Starting data preprocessing - calculating buying power balance from SQL logic...")
        calculated_balance_df = self._calculate_buying_power_balance()
        if calculated_balance_df is None:
            self.logger.error("Data preprocessing failed - unable to calculate buying power balance")
            return None
        self.logger.info("Data preprocessing completed successfully")
        return calculated_balance_df

    def _perform_validation_with_service(self, calculated_balance_df: DataFrame):
        """Perform validation using the data validator service."""
        try:
            self.logger.info("Starting IDSS User Buying Power validation workflow...")
            self.logger.info("Client Cash: %s", self.client_cash_path)
            self.logger.info("RT Trade: %s", self.rt_trade_path)
            self.logger.info("View Order: %s", self.view_order_path)
            self.logger.info("Indo Stock Wallet: %s", self.indo_stock_wallet_path)
            self.logger.info("Output: %s", self.output_path)

            # Step 1: Load indo_stock_wallet for comparison
            self.logger.info("Loading indo_stock_wallet for validation...")
            indo_stock_wallet_df = self._load_data(self.indo_stock_wallet_path)

            self.logger.info("Loaded %d records from indo_stock_wallet", indo_stock_wallet_df.count())

            # Step 2: Process indo_stock_wallet to calculate total balance (balance + blocked_balance)
            self.logger.info("Processing indo_stock_wallet to calculate total balance...")
            wallet_balance_df = indo_stock_wallet_df.select(
                col("client_id"),
                (col("balance") + col("blocked_balance")).alias("balance")
            )

            self.logger.info("Sample wallet balances after adding blocked_balance:")
            sample_wallet = wallet_balance_df.limit(5).collect()
            for i, row in enumerate(sample_wallet, 1):
                self.logger.info("  %d. ClientID: %s, Total Balance: %s", i, row.client_id, row.balance)

            # Step 3: Prepare DataFrames for validation
            # Select relevant columns from calculated balance
            df1 = calculated_balance_df.select(
                col("ClientID"),
                col("calculated_balance").alias("balance")
            )

            # Use processed wallet balance
            df2 = wallet_balance_df

            reconciled_df = self.data_validator_service.reconcile(df1, df2)
            self.logger.info("Writing the reconciled data to path: %s", self.output_path)
            self._write_dataframe(reconciled_df, self.output_path)
            self.logger.info("Reconciled Dataframe has been successfully written to path: %s", self.output_path)
            return reconciled_df

        except Exception as e:
            error_msg = "IDSS User Buying Power validation workflow failed: %s"
            self.logger.error(error_msg, str(e))

    def _write_dataframe(self, df: DataFrame, output_path: str) -> None:
        """Write DataFrame to file with proper formatting (supports S3 and HDFS)."""
        try:
            # For S3/HDFS, we need to accept that Spark creates directories
            # The final path will be a directory containing the part files
            if self.output_format == "csv":
                # Write as CSV - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").option("header", "true").csv(output_path)
            elif self.output_format == "parquet":
                # Write as Parquet - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").parquet(output_path)
            elif self.output_format == "json":
                # Write as JSON - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").json(output_path)

            self.logger.info("Written results to directory: %s", output_path)
            self.logger.info("Note: Spark creates a directory with part files - this is normal behavior")

        except Exception as e:
            self.logger.error("Failed to write DataFrame to %s: %s", output_path, str(e))
            raise

    def _check_and_notify_unmatched_records(self, reconciled_df: DataFrame) -> None:
        """Check for unmatched records and send warning notification similar to forex_validation.py."""
        try:
            self.logger.info("Checking for unmatched records in reconciliation results...")
            
            # Count total unmatched records
            total_unmatched = reconciled_df.count()
            
            if total_unmatched == 0:
                self.logger.info("No unmatched records found - all records reconciled successfully")
                return
            
            self.logger.info("Found %d unmatched records", total_unmatched)
            
            # Get sample unmatched client IDs (limit to 10 for notification)
            sample_unmatched = reconciled_df.select("key_ClientID").limit(10).collect()
            
            # Create client ID list for notification
            client_id_list = []
            for row in sample_unmatched:
                client_id = row.key_ClientID if row.key_ClientID else "N/A"
                client_id_list.append(str(client_id))
            
            # Create warning message similar to forex_validation.py
            client_ids_str = ", ".join(client_id_list[:5]) if client_id_list else "None"
            warning_message = (f"IDSS User Buying Power Validation Alert - Unmatched Records Detected | "
                             f"Total Unmatched: {total_unmatched:,} | "
                             f"Sample Client IDs: {client_ids_str} | "
                             f"Output Location: {self.output_path} | "
                             f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
            
            # Log the warning message with IDSS tag
            self.logger.warning("IDSS - User Buying Power Validation - Unmatched Records Warning:")
            self.logger.warning(warning_message)
            
            # Log detailed sample for debugging
            self.logger.info("Sample unmatched records details:")
            sample_details = reconciled_df.limit(5).collect()
            for i, row in enumerate(sample_details, 1):
                row_dict = row.asDict()
                client_id = row_dict.get('key_ClientID', 'N/A')
                left_balance = row_dict.get('s21_balance', 'N/A')
                right_balance = row_dict.get('pluang_balance', 'N/A')
                self.logger.info("  %d. Client ID: %s, Calculated Balance: %s, Wallet Balance: %s", 
                               i, client_id, left_balance, right_balance)
                
        except Exception as e:
            self.logger.error("Failed to check and notify unmatched records: %s", str(e))

    def run(self) -> None:
        """Run the complete IDSS user buying power validation workflow."""
        try:
            self._validate_config()
            calculated_balance_df = self._preprocess_data()
            
            # Check if preprocessing was successful
            if calculated_balance_df is None:
                self.logger.error("IDSS User Buying Power Validation stopped - required data could not be loaded")
                return
            
            reconciled_df = self._perform_validation_with_service(calculated_balance_df)
            self._check_and_notify_unmatched_records(reconciled_df)
            self.logger.info("IDSS User Buying Power Validation reconciliation completed successfully")

        except Exception as e:
            self.logger.error("IDSS User Buying Power Validation failed with error: %s", str(e))
            raise
        finally:
            self._stop_spark_session()