#!/usr/bin/env python3
"""
IDSS User Trades Validation Script

A data validation tool for Indonesian Stock System (IDSS) that validates
RT_Trade data against reference data with preprocessing for quantity calculation.
"""

from dataclasses import dataclass
from datetime import datetime
from src.utils.custom_logger import get_logger
from src.utils.s3_paths import S3Paths
from src.utils.spark_utils import *
from src.jobs.data_validation.service.validation_service import DataValidatorService
from src.jobs.data_validation.idss.config.schema_config import load_schema_config

@dataclass
class IDSSUserTradesValidationConfig:
    """IDSS user trades validation configuration."""
    include_data_quality: bool = True
    include_missing_analysis: bool = True
    include_field_comparison: bool = True
    target_fields: list[str] = None

    def __post_init__(self):
        if self.target_fields is None:
            self.target_fields = ["market_order_id", "quantity", "unit_price"]



class IDSSUserTradesValidation:
    """
    IDSS User Trades Validation class that validates RT_Trade data
    with preprocessing for quantity calculation.
    """

    def __init__(self, config: dict, **kwargs):
        """
        Initialize IDSS User Trades Validation with configuration parameters.

        Args:
            config: Configuration dictionary containing bucket_path, t_1, etc.
            **kwargs: Additional parameters for file paths and options
        """
        self.spark = None
        self.schema_mapper = None
        self.data_validator_service = None
        # Core configuration
        self.config = config
        self.bucket_path = config.get("bucket_path")
        self.t_1 = config["t_1"]

        # File paths with defaults
        self.rt_trade_path = kwargs.get("rt_trade_path", f"{self.bucket_path}/{S3Paths.s21_rt_rt_trade}/dt={self.t_1}")
        self.indo_stock_trades_path = kwargs.get("indo_stock_trades_path", f"{self.bucket_path}/{S3Paths.indo_stock_trades_v2_snapshots}/dt={self.t_1}")
        self.output_path = kwargs.get("output_path", f"{self.bucket_path}/{S3Paths.data_validation_idss_user_trades_validation}/dt={self.t_1}")
        self.output_format = kwargs.get("output_format", "csv")
        self.logger = get_logger(env=self.config.get("env"), tag="IDSS User Trades Validation")

        # Spark setup
        self.app_name = "IDSSUserTradesValidation"
        self.spark_utils = SparkUtils(self.app_name)
        self.spark = self._setup_spark_session()

        self.io_utils = IOUtils(self.spark, self.config)

        # Validation configuration
        self.validation_config = IDSSUserTradesValidationConfig()

    def _validate_config(self) -> None:
        """Validate configuration parameters."""

        required_paths = [
            self.rt_trade_path,
            self.indo_stock_trades_path,
            self.output_path
        ]

        print(required_paths)
        if not all(required_paths):
            raise ValueError("Missing required file paths")

        if self.output_format not in ["csv", "parquet", "json"]:
            raise ValueError(f"Invalid output format: {self.output_format}")

    def _setup_spark_session(self):
        """Create and configure Spark session."""
        self.spark = self.spark_utils.create_spark_session()
        self.schema_mapper = load_schema_config("user_trades_validation")
        # Initialize data validator service after spark session is created
        self.data_validator_service = DataValidatorService(self.logger, self.config, self.spark, self.schema_mapper)
        return self.spark

    def _stop_spark_session(self) -> None:
        """Stop Spark session."""
        if self.spark is not None:
            self.spark_utils.stop_spark(self.spark)
            self.spark = None

    def _load_data(self, file_path: str) -> DataFrame:
        """Load data from file path with automatic format detection."""
        try:
            # Handle local files
            if file_path.endswith('.csv'):
                df = self.io_utils.read_csv_file(file_path)
            elif file_path.endswith('.parquet'):
                df = self.io_utils.read_parquet_data(file_path)
            elif file_path.endswith('.json'):
                df = self.io_utils.read_json_data(file_path)
            else:
                df = self.io_utils.read_csv_file(file_path)

            # Log the schema
            self.logger.info("Schema for %s:", file_path)
            self.logger.info("Columns: %s", df.columns)
            return df
        except Exception as e:
            self.logger.error("Failed to load data from %s: %s", file_path, str(e))
            return None

    def _preprocess_rt_trades(self, rt_trade_df: DataFrame) -> DataFrame:
        """
        Preprocess RT_Trade data with quantity calculation.
        
        Preprocessing logic:
        - quantity = trade_lot * trade_volume / (trade_lot * 100)
        - Simplifies to: quantity = trade_volume / 100
        - Join key: market_trade_id = transaction_id
        - Fields to match: market_order_id = batch_id, quantity = quantity, unit_price = unit_price
        
        Args:
            rt_trade_df: Raw RT_Trade DataFrame
            
        Returns:
            DataFrame with preprocessed RT_Trade data
        """
        try:
            self.logger.info("Starting RT_Trade data preprocessing...")
            
            # Filter for the target date
            rt_trade_filtered = rt_trade_df.filter(
                col("TradeDate") == self.t_1
            )
            
            self.logger.info("Filtered %d RT_Trade records for date %s", rt_trade_filtered.count(), self.t_1)
            
            # Preprocess RT_Trade data with quantity calculation
            # quantity = (TradeLot*TradeVolume) / (TradeLot*100)
            preprocessed_df = rt_trade_filtered.select(
                col("MarketTradeID").alias("market_trade_id"),  # Join key = transaction_id
                col("MarketOrderID").alias("market_order_id"),  # Field to match = batch_id
                ((col("TradeLot") * col("TradeVolume")) / (col("TradeLot") * lit(100))).alias("quantity"),
                col("TradePrice").alias("unit_price"),  # Field to match = unit_price

            )
            
            self.logger.info("Preprocessed %d RT_Trade records with quantity calculation", preprocessed_df.count())
            
            # Show sample results for debugging
            self.logger.info("Sample preprocessed RT_Trade data:")
            sample_results = preprocessed_df.limit(5).collect()
            for i, row in enumerate(sample_results, 1):
                self.logger.info(
                    "  %d. MarketTradeID: %s, MarketOrderID: %s, Quantity: %s, UnitPrice: %s ",
                    i, row.market_trade_id, row.market_order_id, row.quantity, row.unit_price
                )
            
            # Cache the DataFrame to avoid recomputation
            preprocessed_df.cache()
            self.logger.info("RT_Trade preprocessing completed, DataFrame cached for further processing")
            
            return preprocessed_df
            
        except Exception as e:
            self.logger.error("Failed to preprocess RT_Trade data: %s", str(e))
            raise

    def _preprocess_data(self) -> DataFrame:
        """
        Preprocessing step: Load and preprocess RT_Trade data.

        Returns:
            DataFrame with preprocessed RT_Trade data, or None if data loading failed
        """
        self.logger.info("Starting data preprocessing - loading and processing RT_Trade data...")
        
        # Load RT_Trade data
        rt_trade_df = self._load_data(self.rt_trade_path)
        
        # Check if RT_Trade data failed to load
        if rt_trade_df is None:
            self.logger.error("RT Trade data failed to load from: %s", self.rt_trade_path)
            self.logger.error("Data preprocessing failed - unable to load required RT_Trade data")
            self.logger.error("Gracefully exiting the job due to missing required data.")
            return None
        
        self.logger.info("Loaded %d records from RT_Trade", rt_trade_df.count())
        
        # Preprocess RT_Trade data
        preprocessed_df = self._preprocess_rt_trades(rt_trade_df)
        
        self.logger.info("Data preprocessing completed successfully")
        return preprocessed_df

    def _perform_validation_with_service(self, preprocessed_df: DataFrame):
        """Perform validation using the data validator service."""
        try:
            self.logger.info("Starting IDSS User Trades validation workflow...")
            self.logger.info("RT Trade: %s", self.rt_trade_path)
            self.logger.info("Indo Stock Trades: %s", self.indo_stock_trades_path)
            self.logger.info("Output: %s", self.output_path)

            # Step 1: Load indo stock trades data for comparison
            self.logger.info("Loading indo stock trades data for validation...")
            indo_stock_trades_df = self._load_data(self.indo_stock_trades_path)

            # Check if indo stock trades data failed to load
            if indo_stock_trades_df is None:
                self.logger.error("Indo Stock Trades data failed to load from: %s", self.indo_stock_trades_path)
                self.logger.error("Validation workflow failed - unable to load required Indo Stock Trades data")
                self.logger.error("Gracefully exiting the job due to missing required data.")
                return None

            self.logger.info("Loaded %d records from indo stock trades data", indo_stock_trades_df.count())

            # Step 2: Prepare DataFrames for validation
            # Use preprocessed RT_Trade data as df1
            df1 = preprocessed_df.select(
                col("market_trade_id").alias("transaction_id"),  # Join key
                col("market_order_id").alias("batch_id"),        # Field to match
                col("quantity"),                                 # Field to match
                col("unit_price")          # Field to match
            )

            df1.show(5, truncate=False)

            # Use indo stock trades data as df2
            df2 = indo_stock_trades_df
            df2 = df2.select(
                col("transaction_id"),
                col("batch_id"),
                col("quantity"),
                col("unit_price")
            )

            self.logger.info("Sample preprocessed data for validation:")
            sample_df1 = df1.limit(3).collect()
            for i, row in enumerate(sample_df1, 1):
                self.logger.info("  %d. TransactionID: %s, BatchID: %s, Quantity: %s, UnitPrice: %s", 
                               i, row.transaction_id, row.batch_id, row.quantity, row.unit_price)

            reconciled_df = self.data_validator_service.reconcile(df1, df2)
            self.logger.info("Writing the reconciled data to path: %s", self.output_path)
            self._write_dataframe(reconciled_df, self.output_path)
            self.logger.info("Reconciled Dataframe has been successfully written to path: %s", self.output_path)
            return reconciled_df

        except Exception as e:
            error_msg = "IDSS User Trades validation workflow failed: %s"
            self.logger.error(error_msg, str(e))
            raise

    def _write_dataframe(self, df: DataFrame, output_path: str) -> None:
        """Write DataFrame to file with proper formatting (supports S3 and HDFS)."""
        try:
            # For S3/HDFS, we need to accept that Spark creates directories
            # The final path will be a directory containing the part files
            if self.output_format == "csv":
                # Write as CSV - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").option("header", "true").csv(output_path)
            elif self.output_format == "parquet":
                # Write as Parquet - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").parquet(output_path)
            elif self.output_format == "json":
                # Write as JSON - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").json(output_path)

            self.logger.info("Written results to directory: %s", output_path)
            self.logger.info("Note: Spark creates a directory with part files - this is normal behavior")

        except Exception as e:
            self.logger.error("Failed to write DataFrame to %s: %s", output_path, str(e))
            raise

    def _check_and_notify_unmatched_records(self, reconciled_df: DataFrame) -> None:
        """Check for unmatched records and send warning notification."""
        try:
            self.logger.info("Checking for unmatched records in reconciliation results...")
            
            # Count total unmatched records
            total_unmatched = reconciled_df.count()
            
            if total_unmatched == 0:
                self.logger.info("No unmatched records found - all records reconciled successfully")
                return
            
            self.logger.info("Found %d unmatched records", total_unmatched)
            
            # Get sample unmatched transaction IDs (limit to 10 for notification)
            sample_unmatched = reconciled_df.select("key_transaction_id").limit(10).collect()
            
            # Create transaction ID list for notification
            transaction_id_list = []
            for row in sample_unmatched:
                transaction_id = row.key_transaction_id if row.key_transaction_id else "N/A"
                transaction_id_list.append(str(transaction_id))
            
            # Create warning message
            transaction_ids_str = ", ".join(transaction_id_list[:5]) if transaction_id_list else "None"
            warning_message = (f"IDSS User Trades Validation Alert - Unmatched Records Detected | "
                             f"Total Unmatched: {total_unmatched:,} | "
                             f"Sample Transaction IDs: {transaction_ids_str} | "
                             f"Output Location: {self.output_path} | "
                             f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
            
            # Log the warning message with IDSS tag
            self.logger.warning("IDSS - User Trades Validation - Unmatched Records Warning:")
            self.logger.warning(warning_message)
            
            # Log detailed sample for debugging
            self.logger.info("Sample unmatched records details:")
            sample_details = reconciled_df.limit(5).collect()
            for i, row in enumerate(sample_details, 1):
                row_dict = row.asDict()
                transaction_id = row_dict.get('key_transaction_id', 'N/A')
                left_batch_id = row_dict.get('s21_batch_id', 'N/A')
                right_batch_id = row_dict.get('pluang_batch_id', 'N/A')
                left_quantity = row_dict.get('s21_quantity', 'N/A')
                right_quantity = row_dict.get('pluang_quantity', 'N/A')
                left_price = row_dict.get('s21_unit_price', 'N/A')
                right_price = row_dict.get('pluang_unit_price', 'N/A')
                self.logger.info("  %d. Transaction ID: %s, BatchID (S21/Pluang): %s/%s, Quantity (S21/Pluang): %s/%s, Price (S21/Pluang): %s/%s", 
                               i, transaction_id, left_batch_id, right_batch_id, left_quantity, right_quantity, left_price, right_price)
                
        except Exception as e:
            self.logger.error("Failed to check and notify unmatched records: %s", str(e))
            # Don't re-raise here as this is just notification

    def run(self) -> None:
        """Run the complete IDSS user trades validation workflow."""
        try:
            self._validate_config()
            preprocessed_df = self._preprocess_data()
            
            # Check if preprocessing was successful
            if preprocessed_df is None:
                self.logger.error("IDSS User Trades Validation stopped - required data could not be loaded")
                return
            
            preprocessed_df.show(5, truncate=False)
            reconciled_df = self._perform_validation_with_service(preprocessed_df)
            
            # Check if validation service was successful
            if reconciled_df is None:
                self.logger.error("IDSS User Trades Validation stopped - validation workflow could not complete")
                return
            
            self._check_and_notify_unmatched_records(reconciled_df)
            self.logger.info("IDSS User Trades Validation reconciliation completed successfully")

        except Exception as e:
            self.logger.error("IDSS User Trades Validation failed with error: %s", str(e))
            raise
        finally:
            self._stop_spark_session()
