"""
Configuration module for IDSS data validation.

This module provides functionality to read and manage schema configuration
from the schema_config.json file.
"""

import os
import json
from typing import Dict, Any
from src.utils.custom_logger import get_logger
logger = get_logger()

class IDSSConfig:
    """Configuration class for IDSS data validation."""
    schema_config = {
                "user_position_validation": {   
                    "source1": "s21",
                    "source2": "pluang",
                    "join_keys": ["ClientID", "StockID"],
                    "schema_mapper": {
                        "StockID":"stock_id",
                        "ClientID":"client_id",
                        "balance":"balance"
                    }            
                },
                "user_buying_power_validation": {
                    "source1": "s21",
                    "source2": "pluang",
                    "join_keys": ["ClientID"],
                    "schema_mapper": {
                        "ClientID":"client_id",
                        "balance":"balance"
                    }
                },
                "user_trades_validation": {
                    "source1": "s21",
                    "source2": "pluang",
                    "join_keys": ["transaction_id"],
                    "schema_mapper": {
                        "transaction_id":"transaction_id",
                        "batch_id":"batch_id",
                        "quantity":"quantity",
                        "unit_price":"unit_price"
                    }
            }
        }

    
    def load_schema_config(self, schema_key: str = None) -> Dict[str, Any]:
        """
        Load schema configuration from JSON file.
        
        Args:
            schema_key: Optional key to get specific schema section. 
                       If None, returns complete configuration.
        
        Returns:
            Dict containing the schema configuration (complete or specific section)
            
        Raises:
            FileNotFoundError: If schema_config.json is not found
            json.JSONDecodeError: If the JSON file is invalid
            KeyError: If schema_key is provided but not found in configuration
        """
        self.schema_config = self.schema_config[schema_key]
        return self.schema_config
    
def load_schema_config(schema_key: str = None) -> Dict[str, Any]:
    """
    Load schema configuration from JSON file.
    
    Args:
        schema_key: Optional key to get specific schema section. 
                   If None, returns complete configuration.
    
    Returns:
        Dict containing the schema configuration (complete or specific section)
    """
    config = IDSSConfig()
    return config.load_schema_config(schema_key)