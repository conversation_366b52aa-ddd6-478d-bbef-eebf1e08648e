#!/usr/bin/env python3
"""
IDSS User Position Validation Script

A data validation tool for Indonesian Stock System (IDSS) that validates
calculated stock balances against indo_stock_accounts data.
"""

from dataclasses import dataclass
from datetime import datetime
from src.utils.spark_utils import *
from src.utils.custom_logger import get_logger
from src.utils.s3_paths import S3Paths
from src.jobs.data_validation.service.validation_service import DataValidatorService
from src.jobs.data_validation.idss.config.schema_config import load_schema_config



@dataclass
class IDSSValidationConfig:
    """IDSS validation configuration."""
    include_data_quality: bool = True
    include_missing_analysis: bool = True
    include_field_comparison: bool = True
    target_fields: list[str] = None

    def __post_init__(self):
        if self.target_fields is None:
            self.target_fields = ["balance"]

 
class IDSSUserPositionValidation:
    """
    IDSS User Position Validation class that validates calculated stock balances
    against indo_stock_accounts data.
    """

    def __init__(self, config: dict, **kwargs):
        """
        Initialize IDSS User Position Validation with configuration parameters.

        Args:
            config: Configuration dictionary containing bucket_path, t_1, etc.
            **kwargs: Additional parameters for file paths and options
        """
        self.spark = None
        self.schema_mapper=None
        self.data_validator_service = None
        # Core configuration
        self.config = config
        self.bucket_path = config.get("bucket_path")
        self.t_1 = config["t_1"]

        # File paths with defaults
        self.client_stock_path = kwargs.get("client_stock_path", f"{self.bucket_path}/{S3Paths.s21_rt_client_stock}/dt={self.t_1}")
        self.rt_trade_path = kwargs.get("rt_trade_path", f"{self.bucket_path}/{S3Paths.s21_rt_rt_trade}/dt={self.t_1}")
        self.view_order_path = kwargs.get("view_order_path", f"{self.bucket_path}/{S3Paths.s21_rt_view_order}/dt={self.t_1}")
        self.indo_stock_accounts_path = kwargs.get("indo_stock_accounts_path",
                                                   f"{self.bucket_path}/{S3Paths.indo_stock_accounts_v2_t_2_files}/dt={self.t_1}")
        self.output_path = kwargs.get("output_path",
                                      f"{self.bucket_path}/{S3Paths.data_validation_idss_user_position_validation}/dt={self.t_1}")


        # Validation parameters
        self.join_keys = kwargs.get("join_keys", "ClientID client_id")
        self.output_format = kwargs.get("output_format", "csv")
        self.estimate_time = kwargs.get("estimate_time", True)

        self.logger = get_logger(env=self.config.get("env"), tag="IDSS User Position Validation")

        # Spark setup
        self.app_name = "IDSSUserPositionValidation"
        self.spark_utils = SparkUtils(self.app_name)
        self.spark = self._setup_spark_session()

        self.io_utils = IOUtils(self.spark, self.config)

        # Validation configuration
        self.validation_config = IDSSValidationConfig()



    def _validate_config(self) -> None:
        """Validate configuration parameters."""

        required_paths = [
            self.client_stock_path,
            self.rt_trade_path,
            self.view_order_path,
            self.indo_stock_accounts_path,
            self.output_path
        ]

        print(required_paths)
        if not all(required_paths):
            raise ValueError("Missing required file paths")

        if self.output_format not in ["csv", "parquet", "json"]:
            raise ValueError(f"Invalid output format: {self.output_format}")

    def _setup_spark_session(self):
        """Create and configure Spark session."""
        self.spark = self.spark_utils.create_spark_session()
        self.schema_mapper = load_schema_config("user_position_validation")
        # Initialize data validator service after spark session is created
        self.data_validator_service = DataValidatorService(self.logger, self.config, self.spark,self.schema_mapper)
        return self.spark

    def _stop_spark_session(self) -> None:
        """Stop Spark session."""
        if self.spark is not None:
            self.spark_utils.stop_spark(self.spark)
            self.spark = None

    def _load_data(self, file_path: str) -> DataFrame:
        """Load data from file path with automatic format detection."""
        try:
            # Handle local files
            if file_path.endswith('.csv'):
                df = self.io_utils.read_csv_file(file_path)
            elif file_path.endswith('.parquet'):
                df = self.io_utils.read_parquet_data(file_path)
            elif file_path.endswith('.json'):
                df = self.io_utils.read_json_data(file_path)
            else:
                df = self.io_utils.read_csv_file(file_path)

            # Log the schema
            self.logger.info("Schema for %s:", file_path)
            self.logger.info("Columns: %s", df.columns)
            return df
        except Exception as e:
            self.logger.error("Failed to load data from %s: %s", file_path, str(e))
            return None

    def _calculate_balance(self) -> DataFrame:
        """
        Calculate balance using the SQL logic converted to Spark operations.

        SQL Logic:
        SELECT balance - ISNULL(buyvolume, 0) + ISNULL(sellvolume, 0) - ISNULL(offervolume, 0) as Balance
        FROM client_stock a
        LEFT JOIN (
            SELECT clientid, stockid,
                   SUM(CASE WHEN buysell = 'b' THEN tradevolume ELSE 0 END) AS buyvolume,
                   SUM(CASE WHEN buysell = 's' THEN tradevolume ELSE 0 END) AS sellvolume
            FROM rt_Trade
            WHERE tradedate = CAST(GETDATE() AS date)
            GROUP BY clientid, stockid
        ) b ON a.clientid = b.clientid AND a.stockid = b.stockid
        LEFT JOIN (
            SELECT clientid, stockid,
                   SUM(CASE WHEN buysell = 's' THEN (ordervolume - donevolume) ELSE 0 END) AS offervolume
            FROM vieworder
            WHERE orderdate = CAST(GETDATE() AS date) AND orderstatus IN ('B','O','P')
            GROUP BY clientid, stockid
        ) c ON a.clientid = c.clientid AND a.stockid = c.stockid;

        Returns:
            DataFrame with calculated balance for each ClientID and StockID
        """
        try:
            self.logger.info("Starting balance calculation...")

            # Load all required datasets
            self.logger.info("Loading input datasets...")
            client_stock_df = self._load_data(self.client_stock_path)
            rt_trade_df = self._load_data(self.rt_trade_path)
            view_order_df = self._load_data(self.view_order_path)

            # Check if any of the dataframes failed to load
            if client_stock_df is None or rt_trade_df is None or view_order_df is None:
                error_msg = "Failed to load one or more required datasets. Job cannot proceed."
                if client_stock_df is None:
                    self.logger.error("Client Stock data failed to load from: %s", self.client_stock_path)
                if rt_trade_df is None:
                    self.logger.error("RT Trade data failed to load from: %s", self.rt_trade_path)
                if view_order_df is None:
                    self.logger.error("View Order data failed to load from: %s", self.view_order_path)
                self.logger.error(error_msg)
                self.logger.error("Gracefully exiting the job due to missing required data.")
                return None

            # Log data counts
            self.logger.info("Loaded %d records from Client_Stock", client_stock_df.count())
            self.logger.info("Loaded %d records from RT_Trade", rt_trade_df.count())
            self.logger.info("Loaded %d records from ViewOrder", view_order_df.count())

            # Debug: Show sample data from each dataset
            # self.logger.info("DEBUG - Sample Client_Stock data:")
            # client_stock_df.show(5, truncate=False)

            # self.logger.info("DEBUG - Sample RT_Trade data:")
            # rt_trade_df.show(5, truncate=False)

            # self.logger.info("DEBUG - Sample ViewOrder data:")
            # view_order_df.show(5, truncate=False)

            # Step 1: Calculate buy/sell volumes from RT_Trade
            # Filter for current date (using TradeDate column)
            rt_trade_filtered = rt_trade_df.filter(
                col("TradeDate") == self.t_1
            )

            # self.logger.info("DEBUG - RT_Trade filtered for date %s:", self.t_1)
            # rt_trade_filtered.show(10, truncate=False)

            # Group by ClientID and StockID and calculate buy/sell volumes
            trade_volumes = rt_trade_filtered.groupBy("ClientID", "StockID").agg(
                sum(when(F.upper(col("BuySell")).startswith("B"), col("TradeVolume")).otherwise(0)).alias("buyvolume"),
                sum(when(F.upper(col("BuySell")).startswith("S"), col("TradeVolume")).otherwise(0)).alias("sellvolume")
            )

            self.logger.info("Calculated trade volumes for %d client-stock combinations", trade_volumes.count())
            # self.logger.info("DEBUG - Trade volumes calculated:")
            # trade_volumes.show(10, truncate=False)

            # Step 2: Calculate offer volumes from ViewOrder
            # Filter for current date and order statuses that start with B, O, or P
            view_order_filtered = view_order_df.filter(
                (col("OrderDate") == self.t_1) &
                (col("OrderStatus").startswith("B") |
                 col("OrderStatus").startswith("O") |
                 col("OrderStatus").startswith("P"))
            )

            # self.logger.info("DEBUG - ViewOrder filtered for date %s and statuses starting with [B,O,P]:", self.t_1)
            # view_order_filtered.show(10, truncate=False)

            # Calculate offer volume (only for sell orders)
            offer_volumes = view_order_filtered.filter(
                F.upper(col("BuySell")).startswith("S")
            ).groupBy("ClientID", "StockID").agg(
                sum(col("OrderVolume") - col("DoneVolume")).alias("offervolume")
            )

            self.logger.info("Calculated offer volumes for %d client-stock combinations", offer_volumes.count())
            # self.logger.info("DEBUG - Offer volumes calculated:")
            # offer_volumes.show(10, truncate=False)

            # Step 3: Join Client_Stock with trade volumes (LEFT JOIN)
            balance_df = client_stock_df.alias("a").join(
                trade_volumes.alias("b"),
                (col("a.ClientID") == col("b.ClientID")) & (col("a.StockID") == col("b.StockID")),
                "left"
            )

            # self.logger.info("DEBUG - After joining Client_Stock with trade volumes:")
            # balance_df.show(10, truncate=False)

            # Step 4: Join with offer volumes (LEFT JOIN)
            balance_df = balance_df.join(
                offer_volumes.alias("c"),
                (col("a.ClientID") == col("c.ClientID")) & (col("a.StockID") == col("c.StockID")),
                "left"
            )

            # self.logger.info("DEBUG - After joining with offer volumes:")
            # balance_df.show(10, truncate=False)

            # Step 5: Calculate final balance using the SQL formula
            # balance - ISNULL(buyvolume, 0) + ISNULL(sellvolume, 0) - ISNULL(offervolume, 0)
            final_balance_df = balance_df.select(
                col("a.ClientID").alias("ClientID"),
                col("a.StockID").alias("StockID"),
                (col("a.Balance") -
                 F.coalesce(col("b.buyvolume"), lit(0)) +
                 F.coalesce(col("b.sellvolume"), lit(0)) -
                 F.coalesce(col("c.offervolume"), lit(0))
                 ).alias("calculated_balance"),
                col("a.Balance").alias("original_balance"),
                F.coalesce(col("b.buyvolume"), lit(0)).alias("buyvolume"),
                F.coalesce(col("b.sellvolume"), lit(0)).alias("sellvolume"),
                F.coalesce(col("c.offervolume"), lit(0)).alias("offervolume")
            )

            self.logger.info("Calculated final balance for %d records", final_balance_df.count())

            # Show sample results for debugging
            self.logger.info("Sample calculated balances:")
            sample_results = final_balance_df.limit(5).collect()
            for i, row in enumerate(sample_results, 1):
                self.logger.info(
                    "  %d. ClientID: %s, StockID: %s, Original: %s, Calculated: %s, Buy: %s, Sell: %s, Offer: %s",
                    i, row.ClientID, row.StockID, row.original_balance, row.calculated_balance,
                    row.buyvolume, row.sellvolume, row.offervolume)

            # Cache the DataFrame to avoid recomputation and return it for further processing
            final_balance_df.cache()
            self.logger.info("DEBUG - Balance calculation completed, DataFrame cached for further processing")

            return final_balance_df

        except Exception as e:
            self.logger.error("Failed to calculate balance: %s", str(e))
            raise

    def _preprocess_data(self) -> DataFrame:
        """
        Preprocessing step: Calculate balance using SQL logic.

        Returns:
            DataFrame with calculated balance for each ClientID and StockID, or None if data loading failed
        """
        self.logger.info("Starting data preprocessing - calculating balance from SQL logic...")
        calculated_balance_df = self._calculate_balance()
        if calculated_balance_df is None:
            self.logger.error("Data preprocessing failed - unable to calculate balance")
            return None
        self.logger.info("Data preprocessing completed successfully")
        return calculated_balance_df

    def _perform_validation_with_service(self, calculated_balance_df: DataFrame) -> DataFrame:
        """Perform validation using the data validator service."""
        try:
            self.logger.info("Starting IDSS User Position validation workflow...")
            self.logger.info("Client Stock: %s", self.client_stock_path)
            self.logger.info("RT Trade: %s", self.rt_trade_path)
            self.logger.info("View Order: %s", self.view_order_path)
            self.logger.info("Indo Stock Accounts: %s", self.indo_stock_accounts_path)
            self.logger.info("Output: %s", self.output_path)


            # Step 1: Load indo_stock_accounts for comparison
            self.logger.info("Loading indo_stock_accounts for validation...")
            indo_stock_accounts_df = self._load_data(self.indo_stock_accounts_path)

            self.logger.info("Loaded %d records from indo_stock_accounts", indo_stock_accounts_df.count())

            # Step 2: Prepare DataFrames for validation
            # Select relevant columns from calculated balance
            df1 = calculated_balance_df.select(
                col("ClientID"),
                col("StockID"),
                col("calculated_balance").alias("balance")
            )

            # Select relevant columns from indo_stock_accounts
            df2 = indo_stock_accounts_df.select(
                col("client_id"),
                col("stock_id"),
                col("balance")
            )

            reconciled_df = self.data_validator_service.reconcile(df1,df2)
            self.logger.info(f"Writing the reconciled data to path:{self.output_path}")
            self._write_dataframe(reconciled_df, self.output_path)
            self.logger.info(f"Reconciled Dataframe has been successfully written to path:{self.output_path}")
            return reconciled_df
        except Exception as e:
            error_msg = f"IDSS User Position validation workflow failed: {str(e)}"
            self.logger.error(error_msg)


    def _write_dataframe(self, df: DataFrame, output_path: str) -> None:
        """Write DataFrame to file with proper formatting (supports S3 and HDFS)."""
        try:
            # For S3/HDFS, we need to accept that Spark creates directories
            # The final path will be a directory containing the part files
            if self.output_format == "csv":
                # Write as CSV - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").option("header", "true").csv(output_path)
            elif self.output_format == "parquet":
                # Write as Parquet - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").parquet(output_path)
            elif self.output_format == "json":
                # Write as JSON - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").json(output_path)

            self.logger.info("Written results to directory: %s", output_path)
            self.logger.info("Note: Spark creates a directory with part files - this is normal behavior")

        except Exception as e:
            self.logger.error("Failed to write DataFrame to %s: %s", output_path, str(e))
            raise


    def _send_balance_mismatch_notification(self, mismatch_df: DataFrame, mismatch_path: str,
                                            mismatch_count: int) -> None:
        """Send notification about balance mismatches."""
        try:
            # Get sample mismatched client-stock pairs
            sample_mismatches = mismatch_df.select("df1_ClientID", "df1_StockID").limit(5).collect()

            # Create client-stock list
            client_stock_list = []
            for row in sample_mismatches:
                client_id = row.df1_ClientID
                stock_id = row.df1_StockID
                client_stock_list.append(f"{client_id}:{stock_id}")

            # Create notification message
            client_stock_str = ", ".join(client_stock_list) if client_stock_list else "None"
            notification_message = (f"IDSS User Position Validation Alert - Balance Mismatches Detected | "
                                    f"Total Mismatches: {mismatch_count:,} | "
                                    f"Sample Client:Stock pairs: {client_stock_str} | "
                                    f"Output Location: {mismatch_path} | "
                                    f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")

            # Log the notification message (in production, you would send this to actual notification system)
            self.logger.warning("NOTIFICATION - Balance Mismatches:")
            self.logger.warning(notification_message)

        except Exception as e:
            self.logger.error("Failed to send balance mismatch notification: %s", str(e))

    def _check_and_notify_unmatched_records(self, reconciled_df: DataFrame) -> None:
        """Check for unmatched records and send warning notification similar to forex_validation.py."""
        try:
            self.logger.info("Checking for unmatched records in reconciliation results...")

            # Count total unmatched records
            total_unmatched = reconciled_df.count()

            if total_unmatched == 0:
                self.logger.info("No unmatched records found - all records reconciled successfully")
                return

            self.logger.info("Found %d unmatched records", total_unmatched)

            # Get sample unmatched client IDs (limit to 10 for notification)
            sample_unmatched = reconciled_df.select("key_ClientID","key_StockID").limit(10).collect()

            # Create client ID list for notification
            validation_key_list = []
            for row in sample_unmatched:
                client_id = row.key_ClientID if row.key_ClientID else "N/A"
                stock_id = row.key_StockID if row.key_StockID else "N/A"
                validation_key_list.append((client_id,stock_id))


            
            validation_key_str = [f"({client_id},{stock_id})" for client_id,stock_id in validation_key_list]
            warning_message = (f"IDSS User Position Validation Alert - Unmatched Records Detected | "
                               f"Total Unmatched: {total_unmatched} | "
                               f"Validation Keys: {validation_key_str} | "
                               f"Output Location: {self.output_path} | "
                               f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")

            # Log the warning message with IDSS tag
            self.logger.warning("IDSS - User Position Validation - Unmatched Records Warning:")
            self.logger.warning(warning_message)

            # Log detailed sample for debugging
            self.logger.info("Sample unmatched records details:")
            sample_details = reconciled_df.limit(5).collect()
            for i, row in enumerate(sample_details, 1):
                row_dict = row.asDict()
                client_id = row_dict.get('key_ClientID', 'N/A')
                stock_id = row_dict.get('key_StockID', 'N/A')
                left_balance = row_dict.get('s21_balance', 'N/A')
                right_balance = row_dict.get('pluang_balance', 'N/A')
                self.logger.info("  %d. Client ID: %s, Stock ID: %s S21 Balance: %s, Indo Stock Account Balance: %s",
                                 i, client_id,stock_id, left_balance, right_balance)

        except Exception as e:
            self.logger.error("Failed to check and notify unmatched records: %s", str(e))

    def run(self) -> None:
        """Run the complete IDSS user position validation workflow."""
        try:
            # Validate configuration
            self._validate_config()

            # Estimate processing time if requested
            if self.estimate_time:
                time_estimate = self.data_validator_service.estimate_processing_time(
                    self.client_stock_path, self.indo_stock_accounts_path)
                self.logger.info("Estimated processing time: %.2f minutes", time_estimate['estimated_time_minutes'])
                self.logger.info("Total file size: %.2f MB", time_estimate['total_size_mb'])
                if time_estimate['recommendations']:
                    self.logger.info("Recommendations:")
                    for rec in time_estimate['recommendations']:
                        self.logger.info("  - %s", rec)

            # Preprocessing: Calculate balance from SQL logic
            calculated_balance_df = self._preprocess_data()
            
            # Check if preprocessing was successful
            if calculated_balance_df is None:
                self.logger.error("IDSS User Position Validation stopped - required data could not be loaded")
                return
            
            reconciled_df=self._perform_validation_with_service(calculated_balance_df)
            self._check_and_notify_unmatched_records(reconciled_df)
            self.logger.info("IDSS User Position Validation reconciliation completed successfully")


        except Exception as e:
            self.logger.error("IDSS User Position Validation failed with error: %s", str(e))
            raise
        finally:
            self._stop_spark_session()

