# Data Validation Module

This module provides data validation utilities for Pluang batch jobs. It currently focuses on reconciling Forex transactions and uses a factory-pattern based validation service.

## Structure

- `forex_validation.py`: Orchestrates the end-to-end workflow via `ForexValidation`.
- `service/validation_service.py`: Factory-pattern service and strategies:
  - `DataValidationService` (facade)
  - `DataValidatorFactory` (creates strategies)
  - `DataValidator` (abstract base)
  - `SimpleDataValidator` (default strategy)

## Requirements

- Python 3.10+
- PySpark and dependencies from `requirements.txt`

## Usage

```python
from src.jobs.data_validation.forex_validation import ForexValidation

config = {
    "env": "local",
    "bucket_path": "s3a://your-bucket/path",
    "t_1": "2025-09-12",
}

job = ForexValidation(
    config,
    file1_path=f"{config['bucket_path']}/raw/forex_transactions/dt={config['t_1']}",
    file2_path=f"{config['bucket_path']}/curated/forex_unhedged_transactions/dt={config['t_1']}",
    output_path=f"{config['bucket_path']}/reports/forex_validation/dt={config['t_1']}",
    download_path="hdfs:///tmp/forex_validation/results",
    join_keys="id transaction_id",
    output_format="csv",  # csv | parquet | json
    estimate_time=True,
    validator_type="simple",
)

job.run()
```

Notes:
- Set `validator_type` to choose a strategy; default is `simple`.
- Inputs can be local files or S3/HDFS paths; CSV/Parquet/JSON supported.
- Spark writes output directories under `download_path`/`output_path`.

## Extending

1. Create a new class extending `DataValidator` in `service/validation_service.py`.
2. Register it in `DataValidatorFactory.create()`.
3. Use it via `validator_type="your_type"` in `ForexValidation`.

## Testing

- Unit-test strategies by calling `perform_validation` with small DataFrames.
- For E2E, construct `ForexValidation` and call `run()` with sample data.
