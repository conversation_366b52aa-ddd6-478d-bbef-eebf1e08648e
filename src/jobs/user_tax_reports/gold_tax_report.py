from src.utils.spark_utils import *


class GoldTaxReport:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("gold_tax_report")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.tax_year, self.tax_period_end_date, self.tax_period_start_date = DateUtils.get_tax_year_params(self.t_1)
        self.pluang_partner_id = self.config["pluang_partner_id"]
        self.logger.info("Gold Tax Report initialised successfully with t_1: {}, t_2: {}, tax_year: {}"
                         "tax_period_end_date: {}, tax_period_start_date: {}".format(
            self.t_1, self.t_2, self.tax_year, self.tax_period_end_date, self.tax_period_start_date))

    def get_gold_realised_gain(self):
        # read gold return file for tax year end
        accounts = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.accounts, self.tax_period_end_date)
        )
        accounts = accounts.select("id", "user_id").withColumnRenamed("id", "account_id")

        df_period_end = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_returns, self.tax_period_end_date)
        ).select("account_id", "realised_gain").withColumnRenamed("realised_gain", "end_realised_gain")

        df_period_start = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_returns, self.tax_period_start_date)
        ).select("account_id", "realised_gain").withColumnRenamed("realised_gain", "start_realised_gain")

        df_period_end = df_period_end.join(accounts, on=["account_id"], how="left")
        df_period_start = df_period_start.join(accounts, on=["account_id"], how="left")
        df_period_realised_gain = df_period_end.join(df_period_start, on=["user_id", "account_id"], how="left")\
            .fillna(0)

        df_period_realised_gain = df_period_realised_gain.filter(col("user_id") != 0)
        df_period_realised_gain = df_period_realised_gain.withColumn("realised_gain",
                                                col("end_realised_gain") - col("start_realised_gain"))
        return df_period_realised_gain

    def get_gold_tax_data(self):
        gold_realised_gain = self.get_gold_realised_gain()
        total_gold_realised_gain = gold_realised_gain.groupBy(["user_id", "account_id"])\
            .agg(sum("realised_gain").alias("total_realised_gain"))

        df_gold_tax = total_gold_realised_gain.withColumn("financial_year", lit(self.tax_year))

        df_gold_tax = df_gold_tax.withColumn("category", lit("gold"))

        df_gold_tax.coalesce(1).write.mode("overwrite").json("{}/{}/dt={}/".format(self.bucket_path,
        S3Paths.gold_tax_report, self.tax_period_end_date))
        return df_gold_tax

    def write_gold_tax_data_in_mongo(self, gold_tax_data):
        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_config["collection"] = self.config["data_store"]["tax_report"]["gold_tax_report_collection"]
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)

        mongo_write_config = {
            "uri": mongo_uri,
            "collection": mongo_config["collection"],
            "batch_size": "500",
            "mode": "append"
        }

        shard_key = "{'accountId':1, 'userId':1, 'financialYear':1}"

        self.io_utils.write_dataset_to_mongo(
            gold_tax_data,
            mongo_config=mongo_write_config,
            asset_name="gold tax report",
            write_format="update",
            shardkey=shard_key,
            add_created_at=True
        )

    def run(self):
        gold_tax_data = self.get_gold_tax_data()
        self.write_gold_tax_data_in_mongo(gold_tax_data)
        self.spark_utils.stop_spark(self.spark)
