from src.utils.spark_utils import *


class CryptoCurrencyTaxReport:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("crypto_currency_tax_report")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)
        self.asset_utils = AssetUtils(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.tax_year, self.tax_period_end_date, self.tax_period_start_date = DateUtils.get_tax_year_params(self.t_1)
        self.pluang_partner_id = self.config["pluang_partner_id"]
        self.logger.info("Crypto Currency Tax Report initialised successfully with t_1: {}, t_2: {}, tax_year: {}"
                         "tax_period_end_date: {}, tax_period_start_date: {}".format(
            self.t_1, self.t_2, self.tax_year, self.tax_period_end_date, self.tax_period_start_date))

    def get_one_year_crytpo_trans(self):
        crypto_transaction = self.io_utils.read_parquet_data(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_transactions, self.tax_period_end_date)
        ).select(
            "account_id", "user_id", "client_id", "crypto_currency_id", "partner_id", "executed_unit_price",
            "created", "transaction_type", "executed_quantity", "status", "taxation_fee"
        )
        crypto_transaction_pocket = self.io_utils.read_parquet_data(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_pocket_transactions, self.tax_period_end_date)
        ).select(
            "account_id", "user_id", "client_id", "crypto_currency_id", "partner_id", "executed_unit_price",
            "created", "transaction_type", "executed_quantity", "status", "taxation_fee"
        )
        crypto_transaction = crypto_transaction.union(crypto_transaction_pocket)

        crypto_transaction = crypto_transaction.filter(col("status").isin(Constants.SUCCESS, Constants.PARTIALLY_FILLED)) \
            .filter(col("transaction_type").isin(Constants.BUY, Constants.SELL)) \
            .withColumn("dt", F.from_utc_timestamp(F.to_timestamp("created"), Constants.JKT_TIMEZONE).cast("date")) \
            .withColumn("financial_year", F.year(col("dt"))) \
            .withColumn("month", F.month(col("dt"))) \
            .filter(col("financial_year") == self.tax_period_end_date.year).fillna(0) \
            .withColumn("total_value", (col("executed_quantity")*col("executed_unit_price")))

        return crypto_transaction

    def calculate_vat_and_wht(self, crypto_transaction):
        self.logger.info("calculate vat on buy transaction")
        crypto_transaction_vat = crypto_transaction.withColumn("vat",
                                (when(col("transaction_type").isin(Constants.BUY), col("taxation_fee")).otherwise(0)))

        self.logger.info("calculate wht on sell transaction")
        crypto_transaction_vat_wht = crypto_transaction_vat.withColumn("wht",
                                (when(col("transaction_type").isin(Constants.SELL), col("taxation_fee")).otherwise(0)))
        crypto_transaction_vat_wht = crypto_transaction_vat_wht.withColumn("buy",
                    F.round(when((col("transaction_type").isin("BUY")), col("total_value")).otherwise(0)).cast("long"))
        crypto_transaction_vat_wht = crypto_transaction_vat_wht.withColumn("sell",
                    F.round(when((col("transaction_type").isin("SELL")), col("total_value")).otherwise(0)).cast("long"))
        return crypto_transaction_vat_wht

    def monthly_tax_report(self, monthly_vat_wht):
        month_text = self.ops.create_jkt_month_text_df(self.tax_year)
        monthly_vat_wht = monthly_vat_wht.select(
            "user_id", "account_id", "month", "vat", "wht", "buy_value", "sell_value"
        )
        monthly_vat_wht = monthly_vat_wht.join(month_text, on=["month"], how="left")

        monthly_vat_wht_columns = monthly_vat_wht.select("month", "monthText", "vat", "wht").columns

        monthly_vat_wht = monthly_vat_wht.withColumn("monthly_tax", F.struct(monthly_vat_wht_columns))

        monthly_vat_wht = monthly_vat_wht.withColumn("tax_report", lit("monthly_tax"))

        monthly_vat_wht = monthly_vat_wht.groupBy("tax_report","user_id","account_id")\
            .agg(F.collect_list("monthly_tax").alias("monthly_tax")).drop("tax_report")

        monthly_vat_wht = monthly_vat_wht.withColumn("monthly_tax", F.expr("array_sort(monthly_tax)"))

        return monthly_vat_wht

    def get_crypto_codes(self):
        self.logger.info("reading crypto coin codes from kafka topic")
        df_crypto_code = self.asset_utils.get_crypto_assets()
        df_crypto_code = df_crypto_code.select(col("id"), col("symbol"))
        self.logger.info("successfully read crypto codes from kafka")
        return df_crypto_code

    def coin_tax_report(self, coin_base_vat_wht):
        coin_code = self.get_crypto_codes()
        coin_base_vat_wht = coin_base_vat_wht.select(
            "user_id", "account_id", "crypto_currency_id", "vat", "wht", "buy_value", "sell_value"
        )
        coin_base_vat_wht = coin_base_vat_wht.withColumnRenamed("crypto_currency_id", "id")

        coin_base_vat_wht = coin_base_vat_wht.join(coin_code, on=["id"], how="left")

        coin_base_vat_wht_column = coin_base_vat_wht.select("id", "vat", "wht", "symbol").columns

        coin_base_vat_wht = coin_base_vat_wht.withColumn("crypto_currency_tax", F.struct(coin_base_vat_wht_column))

        coin_base_vat_wht = coin_base_vat_wht.withColumn("tax_report", lit("crypto_currency_tax"))

        coin_base_vat_wht = coin_base_vat_wht.groupBy("tax_report", "user_id", "account_id")\
            .agg(F.collect_list("crypto_currency_tax").alias("crypto_currency_tax")).drop("tax_report")

        return coin_base_vat_wht

    def create_report(self):
        crypto_transaction = self.get_one_year_crytpo_trans()
        transaction = self.calculate_vat_and_wht(crypto_transaction)
        monthly_vat_wht = transaction.groupBy("user_id", "account_id", "month")\
            .agg(
            sum("vat").alias("vat"), sum("wht").alias("wht"),
            sum("buy").alias("buy_value"), sum("sell").alias("sell_value")
        )
        monthly_report = self.monthly_tax_report(monthly_vat_wht)

        coin_base_vat_wht = transaction.groupBy("user_id", "account_id", "crypto_currency_id")\
            .agg(
            sum("vat").alias("vat"), sum("wht").alias("wht"),
            sum("buy").alias("buy_value"), sum("sell").alias("sell_value")
        )
        coin_report = self.coin_tax_report(coin_base_vat_wht)

        yearly_vat_wht = transaction.groupBy("user_id", "account_id")\
            .agg(
            sum("vat").alias("total_vat"), sum("wht").alias("total_wht"),
            sum("buy").alias("total_buy_value"), sum("sell").alias("total_sell_value")
        ).fillna(0)

        yearly_vat_wht = yearly_vat_wht.withColumn("total_transaction",
                                                   (col("total_buy_value") + col("total_sell_value"))
                        )
        report = yearly_vat_wht.join(monthly_report, on=["user_id", "account_id"], how="full").fillna(0)

        report = report.join(coin_report, on=["user_id", "account_id"], how="full").fillna(0)

        report = report.withColumn("financial_year", lit(self.tax_period_end_date.year))

        report = report.withColumn("category", lit("cryptocurrency"))

        report.coalesce(1).write.mode("overwrite").json(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_tax_report, self.tax_period_end_date)
        )
        return report

    def write_crypto_data_in_mongo(self, crypto_tax_data):
        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_config["collection"] = self.config["data_store"]["tax_report"]["crypto_tax_report_collection"]
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)

        mongo_write_config = {
            "uri": mongo_uri,
            "collection": mongo_config["collection"],
            "batch_size": "500",
            "mode": "append"
        }

        shard_key = "{'accountId':1, 'userId':1, 'financialYear':1}"

        self.io_utils.write_dataset_to_mongo(
            crypto_tax_data,
            mongo_config=mongo_write_config,
            asset_name="crypto tax report",
            write_format="update",
            shardkey=shard_key,
            add_created_at=True
        )

    def run(self):
        crypto_tax_report = self.create_report()
        self.write_crypto_data_in_mongo(crypto_tax_report)
        self.spark_utils.stop_spark(self.spark)
