"""IDSS annual tax report job.

This module builds the annual IDSS tax report by:
- Reading start/end portfolio snapshots
- Calculating realised gains for the financial year
- Enriching with latest market prices and stock metadata
- Aggregating and writing results to JSON and MongoDB

All heavy lifting is delegated to helper methods to keep the main flow
clear and maintainable.
"""

from src.utils.spark_utils import *
from src.utils.io_utils import IOUtils
from src.utils.custom_logger import get_logger
from src.utils.s3_paths import S3Paths
from src.utils.operations import Operations

class IDSSTaxReport:
    """Builds the IDSS annual tax report.

    Parameters:
    - config: runtime configuration including Spark, S3/Mongo, and dates
    """

    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config
        self.spark_utils = SparkUtils("idss_tax_report")
        self.spark = self.spark_utils.create_spark_session()
        self.io_utils = IOUtils(self.spark, self.config)
        self.asset_utils = AssetUtils(self.spark, self.config)
        self.operations = Operations(self.spark)
        self.bucket = config["bucket"]
        self.offset = config.get("offset", 0)
        self.t_1 = self.config["t_1"]
        self.tax_year, self.tax_period_end_date, self.tax_period_start_date = DateUtils.get_tax_year_params(self.t_1)
        self.logger.info("Indo Stock Tax Report v2 initialised successfully with t_1: %s, tax_year: %s, tax_period_end_date: %s, tax_period_start_date: %s",
            self.t_1, self.tax_year, self.tax_period_end_date, self.tax_period_start_date)

    def run(self):
        """Entry point to execute the job and ensure Spark shutdown."""
        try:
            self.logger.info("Starting IDSS Tax Report generation...")
            self.generate_annual_tax_report()
            self.logger.info("IDSS Tax Report generation completed successfully")
        except Exception as e:
            self.logger.error("Error during IDSS Tax Report generation: %s", str(e))
            self.logger.error("Exception type: %s", type(e).__name__)
            import traceback
            self.logger.error("Traceback: %s", traceback.format_exc())
            raise
        finally:
            try:
                self.spark_utils.stop_spark(self.spark)
                self.logger.info("Spark session stopped successfully")
            except Exception as e:
                self.logger.error("Error stopping Spark session: %s", str(e))

    def get_current_prices(self):
        """Read latest close prices for IDSS stocks from storage.

        Returns a DataFrame with columns: stock_code, current_price
        """
        s3_path = f"s3a://{self.bucket}/{S3Paths.indo_stock_price_v2}/dt={self.tax_period_end_date}"
        self.logger.info("Reading current prices from %s", s3_path)

        data = self.io_utils.read_csv_file(path=s3_path)
        self.logger.info("Applying deduplication on candle_end_time and stock_code")
        data = self.operations.de_dupe_dataframe(data, ["stock_code"], "candle_end_time")
        self.logger.info(f"After deduplication: {data.count()} records remaining")
        current_prices_df = data.select(
            col("stock_code"),
            col("close_price").alias("current_price")
        )

        self.logger.info("Current prices successfully loaded")
        return current_prices_df

    def _resolve_snapshot_paths(self):
        """Resolve start/end snapshot paths from overrides or defaults."""
        base_path = f"s3a://{self.bucket}/{S3Paths.indo_stock_returns_v2_t_2_files}/dt="
        
        end_path = f"{base_path}{self.tax_period_end_date}/"
        start_path = f"{base_path}{self.tax_period_start_date}/"
        wallet_snapshot = f"s3a://{self.bucket}/{S3Paths.indo_stock_wallet_v2}/dt={self.tax_period_end_date}/"
        dividend_snapshot =f"s3://{self.bucket}/{S3Paths.indo_stock_dividend_snapshot}/dt={self.tax_period_end_date}/"
        indo_stock_transactions_path = f"s3a://{self.bucket}/{S3Paths.indo_stock_transactions}/dt={self.tax_period_end_date}/"
        return start_path, end_path, wallet_snapshot, dividend_snapshot, indo_stock_transactions_path

    def _read_snapshots(self):
        """Read start and end snapshots; return tuple (start_df, end_df)."""
        start_path, end_path, wallet_snapshot, dividend_snapshot, indo_stock_transactions_path = self._resolve_snapshot_paths()
        self.logger.info("Start Path: %s, End Path: %s, Wallet Snapshot: %s, Dividend Snapshot: %s, Indo Stock Transactions Path: %s", start_path, end_path, wallet_snapshot, dividend_snapshot, indo_stock_transactions_path)

        df_end = self.io_utils.read_csv_file(end_path, schema=None, return_empty=True)
        df_start = self.io_utils.read_csv_file(start_path, schema=None, return_empty=True)
        df_wallet = self.io_utils.read_csv_file(wallet_snapshot, schema=None, return_empty=True).select(
            col("account_id"),
            (col("balance") + col("blocked_balance")).alias("rdn_balance")
        )
        df_dividend_snapshot = self.io_utils.read_parquet_data(dividend_snapshot, schema=None)
        df_indo_stock_transactions = self.io_utils.read_parquet_data(indo_stock_transactions_path, schema=None)
        for name, df in (("end", df_end), ("start", df_start), ("wallet", df_wallet), ("dividend", df_dividend_snapshot), ("indo_stock_transactions", df_indo_stock_transactions)):
            if df is None:
                self.logger.warning("%s snapshot not found or empty; will default to zeros if needed", name.capitalize())
            else:
                # self.logger.info("%s snapshot columns: %s", name.capitalize(), df.columns)
                self.logger.info("%s snapshot count: %s", name.capitalize(), df.count())

        return df_start, df_end, df_wallet, df_dividend_snapshot, df_indo_stock_transactions

    def _prepare_snapshots(self, df_start, df_end, df_indo_stock_transactions, df_dividend_snapshot):
        """Select/rename required columns and backfill missing frames with zeros."""
        select_cols = ["user_id", "account_id", "stock_id", "realised_gain"]
        end_select_cols = list(select_cols) + ["total_quantity", "weighted_cost"]
        preprocessed_df_dividend_snapshot = None

        if df_end is not None:
            df_end = df_end.select([c for c in end_select_cols if c in df_end.columns]) \
                .withColumnRenamed("realised_gain", "end_realised_gain") \
                .withColumnRenamed("total_quantity", "quantity")
        if df_start is not None:
            df_start = df_start.select([c for c in select_cols if c in df_start.columns]) \
                .withColumnRenamed("realised_gain", "start_realised_gain")

        if df_end is None and df_start is None:
            return None, None, None

        if df_end is None:
            df_end = df_start.select("user_id", "account_id", "stock_id") \
                .withColumn("end_realised_gain", lit(0.0))
            self.logger.info("Constructed end snapshot from start snapshot distinct keys with zero gains")
        if df_start is None:
            df_start = df_end.select("user_id", "account_id", "stock_id") \
                .withColumn("start_realised_gain", lit(0.0))
            self.logger.info("Constructed start snapshot from end snapshot distinct keys with zero gains")
        
        if df_indo_stock_transactions is not None:
            df_indo_stock_transactions = df_indo_stock_transactions \
                .filter(
                    (col("side") == Constants.SELL) & 
                    (col("order_status") == Constants.SUCCESS)
                ) \
                .select("user_id", "account_id", "stock_id", "response_price", "trade_qty") \
                .withColumn(
                    "stock_sales", 
                    (F.coalesce(col("response_price"), lit(0)) * F.coalesce(col("trade_qty"), lit(0))).cast(LongType())
                ) \
                .groupBy("user_id", "account_id", "stock_id") \
                .agg(F.sum("stock_sales").alias("stock_sales"))

        if df_dividend_snapshot is not None:
            df_dividend_snapshot = df_dividend_snapshot \
                .filter(
                    (F.to_date(col("payment_date")) >= lit(self.tax_period_start_date)) &
                    (F.to_date(col("payment_date")) <= lit(self.tax_period_end_date))
                )
            preprocessed_df_dividend_snapshot = df_dividend_snapshot.select("user_id", "account_id", "stock_id", "total_dividend") \
                .groupBy("user_id", "account_id", "stock_id") \
                .agg(F.sum("total_dividend").alias("total_dividend"))
            

        return df_start, df_end, df_indo_stock_transactions, df_dividend_snapshot, preprocessed_df_dividend_snapshot

    def _compute_period_gains(self, df_start, df_end):
        """Join start/end and compute realisedGain for the period."""
        df_period = df_end.join(df_start, on=["user_id", "account_id", "stock_id"], how="full")
        df_period = df_period.fillna({"end_realised_gain": 0.0, "start_realised_gain": 0.0})
        df_period = df_period.withColumn("realisedGain", F.round(col("end_realised_gain") - col("start_realised_gain"), 2)) \
            .filter(col("quantity") != 0)
        self.logger.info("Joined period rows (non-zero quantity): %s", df_period.count())
        return df_period

    def _enrich_with_market_data(self, df_period):
        """Join current prices and mapping; compute symbol and stock_value."""
        current_prices = self.get_current_prices()
        mapping_df = self.asset_utils.get_indo_stock_mapping().select(
            col("id").alias("stock_id"),
            col("code")
        )
        current_prices = current_prices.join(
            broadcast(mapping_df), current_prices.stock_code == mapping_df.code, "inner"
        ).drop("code")

        df_period = df_period.join(current_prices, on=["stock_id"], how="left")
        df_period = df_period.withColumn("symbol", col("stock_code"))
        df_period = df_period.withColumn("quantity", F.coalesce(col("quantity"), lit(0.0)))
        df_period = df_period.withColumn("weighted_cost", F.coalesce(col("weighted_cost"), lit(0.0)))

        
        # Log records with null symbols or current_price before filtering
        null_symbols = df_period.filter(col("symbol").isNull()).select("stock_id", "user_id", "account_id").limit(10)
        null_prices = df_period.filter(col("current_price").isNull()).select("stock_id", "user_id", "account_id").limit(10)
        
        null_symbols_list = null_symbols.collect()
        if null_symbols_list:
            self.logger.warning("Top 10 records with null symbols:")
            for row in null_symbols_list:
                self.logger.warning("  stock_id: %s, user_id: %s, account_id: %s", row.stock_id, row.user_id, row.account_id)
        else:
            self.logger.info("No records found with null symbols")
            
        null_prices_list = null_prices.collect()
        if null_prices_list:
            self.logger.warning("Top 10 records with null current_price:")
            for row in null_prices_list:
                self.logger.warning("  stock_id: %s, user_id: %s, account_id: %s", row.stock_id, row.user_id, row.account_id)
        else:
            self.logger.info("No records found with null current_price")
        
        # Filter out records with null symbols or current_price
        df_period = df_period.filter(col("symbol").isNotNull() & col("current_price").isNotNull())
        self.logger.info("Filtered out records with null symbols or current_price. Remaining records: %s", df_period.count())
        
        df_period = df_period.withColumn(
            "stock_value",
            F.round(col("current_price") * col("quantity"), 2)
        )
        return df_period

    def get_monthly_dividend(self, df_dividend_snapshot):
        """Returns a DataFrame with user_id, account_id, and a list of dicts [{month_name: dividend}] where dividend > 0."""

        # Use 'payment_date' for month and 'total_dividend' for aggregation
        if "payment_date" not in df_dividend_snapshot.columns or "total_dividend" not in df_dividend_snapshot.columns:
            raise ValueError("Required columns 'payment_date' or 'total_dividend' not found in df_dividend_snapshot")

        df_monthly = df_dividend_snapshot.withColumn("month_num", F.month(F.from_utc_timestamp(F.to_timestamp(col("payment_date")), "Asia/Jakarta")))
        df_monthly = df_monthly.withColumn("year", F.year(F.from_utc_timestamp(F.to_timestamp(col("payment_date")), "Asia/Jakarta")))

        # Create month text DataFrames using utility functions
        jkt_month_df = self.operations.create_jkt_month_text_df(self.tax_year)
        english_month_df = self.operations.create_english_month_text_df(self.tax_year)
        
        # Join with month text DataFrames
        df_monthly = df_monthly.join(jkt_month_df, df_monthly.month_num == jkt_month_df.month, "left") \
            .select(df_monthly["*"], jkt_month_df.monthText.alias("jkt_month_text"))
        
        df_monthly = df_monthly.join(english_month_df, df_monthly.month_num == english_month_df.month, "left") \
            .select(df_monthly["*"], english_month_df.monthText.alias("english_month_text"))
        
        # Aggregate and create list of structs in the required format, sorted by month
        df_monthly_list = df_monthly.groupBy("user_id", "account_id", "month_num", "jkt_month_text", "english_month_text") \
            .agg(F.sum("total_dividend").alias("dividend")) \
            .orderBy("month_num") \
            .groupBy("user_id", "account_id") \
            .agg(F.collect_list(
                F.struct(
                    col("month_num").alias("month"),
                    col("jkt_month_text").alias("jktMonthText"),
                    col("english_month_text").alias("englishMonthText"),
                    col("dividend")
                )
            ).alias("monthlyDividends"))

        return df_monthly_list

    def _aggregate_results(self, df_period, df_wallet, df_monthly, df_indo_stock_transactions, preprocessed_df_dividend_snapshot):
        """Aggregate per stock then per account and add static columns."""
        tax_year = self.tax_year if self.tax_year is not None else self.tax_period_end_date.year
        df_period = df_period.groupBy(["user_id", "account_id", "stock_id", "symbol"]) \
            .agg(
            F.round(F.sum("realisedGain"), 2).cast(DecimalType(38, 2)).alias("realisedGain"),
            F.sum("quantity").alias("quantity"),
            F.sum("weighted_cost").cast(DecimalType(38, 2)).alias("weighted_cost"),
            F.first("current_price", ignorenulls=True).alias("current_price"),
            F.sum("stock_value").cast(DecimalType(38, 2)).alias("stock_value"),
            )

        if df_indo_stock_transactions is not None:
            df_period = df_period.join(df_indo_stock_transactions, on=["user_id", "account_id", "stock_id"], how="left")
            df_period = df_period.withColumn("stock_sales", F.coalesce(col("stock_sales"), lit(0.0)))
        else:
            df_period = df_period.withColumn("stock_sales", lit(0.0))

        if preprocessed_df_dividend_snapshot is not None:
            df_period = df_period.join(preprocessed_df_dividend_snapshot, on=["user_id", "account_id", "stock_id"], how="left")
            df_period = df_period.withColumn("dividend", F.coalesce(col("total_dividend"), lit(0.0)))
            df_period = df_period.drop("total_dividend")
            
        else:
            df_period = df_period.withColumn("dividend", lit(0.0))

        df_period = df_period.withColumn("quantity", F.coalesce(col("quantity"), lit(0.0)))
        df_period = df_period.withColumn("weighted_cost", F.coalesce(col("weighted_cost"), lit(0.0)))
        df_period = df_period.withColumn("dividend", F.coalesce(col("dividend"), lit(0.0)))
        df_period = df_period.withColumn("stock_sales", F.coalesce(col("stock_sales"), lit(0.0)))
        df_period = df_period.withColumn("current_price", F.coalesce(col("current_price"), lit(0.0)))
        df_period = df_period.withColumn("stock_value", F.coalesce(col("stock_value"), lit(0.0)))
        df_period = df_period.withColumn("avg_buy_price", col("weighted_cost"))
        df_period = df_period.withColumn(
            "idssEarnings",
            F.struct(
                col("symbol"),
                col("quantity"),
                col("avg_buy_price").alias("avgBuyPrice"),
                col("stock_value").alias("stockValue"),
                col("dividend")
            )
        )

        result = df_period.groupBy(["user_id", "account_id"]) \
            .agg(
            F.collect_list("idssEarnings").alias("idssEarnings"),
            F.sum("stock_value").cast(DecimalType(38, 2)).alias("total_stock_value"),
            F.sum("stock_sales").cast(DecimalType(38, 2)).alias("total_stock_sales"),
            F.sum("dividend").cast(DecimalType(38, 2)).alias("total_dividend")
            )
        self.logger.info("Aggregated per-account rows: %s", result.count())

        result = result.join(df_monthly, on=["user_id", "account_id"], how="left")
        result = result.withColumn("monthlyDividends", F.coalesce(col("monthlyDividends"), F.array()))

        # Join with df_wallet and handle missing values
        if df_wallet is not None:
            result = result.join(df_wallet, on=["account_id"], how="left")
            result = result.withColumn("total_rdn_balance", F.coalesce(col("rdn_balance"), lit(0.0)))
            result = result.drop("rdn_balance")
        else:
            result = result.withColumn("total_rdn_balance", lit(0.0))
        
        result = result.withColumn("financial_year", lit(tax_year)) \
            .withColumn("category", lit("indoStock")) \
            .withColumn("stock_sales_transaction_tax", F.round(col("total_stock_sales") * 0.001, 2))
        return result


    def _write_outputs(self, result_df):
        """Write JSON output and append to MongoDB."""
        try:
            # Write JSON output to S3
            out_path = f"s3a://{self.bucket}/{S3Paths.indo_stock_v2_tax_report}/dt={self.tax_period_end_date}/"
            self.logger.info("Writing annual report to: %s", out_path)
            self.io_utils.write_json_file(result_df, out_path, partition=1)
            self.logger.info("Annual report JSON written successfully")

            # Write to MongoDB
            self.logger.info("Writing to MongoDB...")
            mongo_config = self.config["data_store"]["reporting_mongo"]
            mongo_config["collection"] = self.config["data_store"]["tax_report"]["indo_stock_v2_tax_report_collection"]
            mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)
            mongo_write_config = {
                "uri": mongo_uri,
                "collection": self.config["data_store"]["tax_report"]["indo_stock_v2_tax_report_collection"],
                "batch_size": "500",
                "mode": "append",
            }
            write_format = mongo_write_config.get("write_format", "update")
            shardkey = "{'accountId':1, 'userId':1, 'financialYear':1}"

            self.io_utils.write_dataset_to_mongo(result_df, mongo_write_config, "IDSS Tax Report", write_format, shardkey)
            self.logger.info("MongoDB write completed successfully")
            
        except Exception as e:
            self.logger.error("Error in _write_outputs: %s", str(e))
            self.logger.error("Exception type: %s", type(e).__name__)
            import traceback
            self.logger.error("Traceback: %s", traceback.format_exc())
            raise

    def generate_annual_tax_report(self):
        """Main orchestration for the IDSS annual tax report generation."""
        try:
            self.logger.info("Generating IDSS annual tax report")
            tax_year = self.tax_year if self.tax_year is not None else self.tax_period_end_date.year

            # Read snapshots
            self.logger.info("Reading snapshots...")
            df_start, df_end, df_wallet, df_dividend_snapshot, df_indo_stock_transactions = self._read_snapshots()
            if df_end is None and df_start is None and df_wallet is None and df_dividend_snapshot is None and df_indo_stock_transactions is None:
                self.logger.info("No IDSS returns snapshots found for annual period %s", tax_year)
                return None

            # Prepare snapshots
            self.logger.info("Preparing snapshots...")
            df_start, df_end, df_indo_stock_transactions, df_dividend_snapshot, preprocessed_df_dividend_snapshot = self._prepare_snapshots(df_start, df_end, df_indo_stock_transactions, df_dividend_snapshot)
            if df_start is None and df_end is None:
                self.logger.info("No usable data after snapshot preparation for %s", tax_year)
                return None
            
            # Get monthly dividend data
            self.logger.info("Processing monthly dividend data...")
            df_monthly = self.get_monthly_dividend(df_dividend_snapshot)

            # Compute period gains
            self.logger.info("Computing period gains...")
            df_period = self._compute_period_gains(df_start, df_end)
            
            # Enrich with market data
            self.logger.info("Enriching with market data...")
            df_period = self._enrich_with_market_data(df_period)
            
            # Aggregate results
            self.logger.info("Aggregating results...")
            result = self._aggregate_results(df_period, df_wallet, df_monthly, df_indo_stock_transactions, preprocessed_df_dividend_snapshot)
            
            # Write outputs
            self.logger.info("Writing outputs...")
            self._write_outputs(result)
            
            self.logger.info("IDSS annual tax report generation completed successfully")
            
        except Exception as e:
            self.logger.error("Error in generate_annual_tax_report: %s", str(e))
            self.logger.error("Exception type: %s", type(e).__name__)
            import traceback
            self.logger.error("Traceback: %s", traceback.format_exc())
            raise