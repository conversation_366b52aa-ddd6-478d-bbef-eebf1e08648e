from src.utils.spark_utils import *


class UsStocksTaxReport:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("us_stocks_tax_report")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)
        self.asset_utils = AssetUtils(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.tax_year, self.tax_period_end_date, self.tax_period_start_date = DateUtils.get_tax_year_params(self.t_1)
        self.pluang_partner_id = self.config["pluang_partner_id"]
        self.logger.info("US Stocks Tax Report initialised successfully with t_1: {}, t_2: {}, tax_year: {}"
                         "tax_period_end_date: {}, tax_period_start_date: {}".format(
            self.t_1, self.t_2, self.tax_year, self.tax_period_end_date, self.tax_period_start_date))

    def get_global_stock_codes(self):
        df_gss_code = self.asset_utils.get_global_stock_assets()
        df_gss_code = df_gss_code.withColumnRenamed("id", "stock_id")\
            .withColumnRenamed("pluang_company_code", "symbol").select("stock_id", "symbol")
        return df_gss_code

    def get_global_stock_realised_gain(self):
        df_gss_period_end = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_returns, self.tax_period_end_date))\
            .select("user_id", "account_id", "global_stock_id", "realised_gain")\
            .withColumnRenamed("realised_gain", "end_realised_gain")

        df_gss_period_start = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_returns, self.tax_period_start_date)
        ).select("user_id", "account_id", "global_stock_id", "realised_gain")\
            .withColumnRenamed("realised_gain", "start_realised_gain")

        df_gss_period_realised_gain = df_gss_period_end.join(
            df_gss_period_start, on=["user_id", "account_id", "global_stock_id"], how="full").fillna(0)

        df_gss_period_realised_gain = df_gss_period_realised_gain.withColumn("realisedGain",
                        col("end_realised_gain") - col("start_realised_gain")).filter(col("realisedGain") != 0)

        df_gss_period_realised_gain = df_gss_period_realised_gain.withColumnRenamed("global_stock_id", "stock_id")

        df_gss_pocket_period_end = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_pocket_returns, self.tax_period_end_date)
        ).select("user_id", "account_id", "global_stock_id", "realised_gain")\
            .withColumnRenamed("realised_gain", "end_realised_gain")\
            .groupBy(["user_id", "account_id", "global_stock_id"])\
            .agg(F.sum("end_realised_gain").alias("end_realised_gain"))

        df_gss_pocket_period_start = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_pocket_returns, self.tax_period_start_date)
        ).select("user_id", "account_id", "global_stock_id", "realised_gain")\
            .withColumnRenamed("realised_gain", "start_realised_gain")\
            .groupBy(["user_id", "account_id", "global_stock_id"])\
            .agg(F.sum("start_realised_gain").alias("start_realised_gain"))

        df_gss_pocket_period_realised_gain = df_gss_pocket_period_end.join(
            df_gss_pocket_period_start, on=["user_id", "account_id", "global_stock_id"], how="full").fillna(0)

        df_gss_pocket_period_realised_gain = df_gss_pocket_period_realised_gain.withColumn("realisedGain",
                F.round(col("end_realised_gain") - col("start_realised_gain"), 2)).filter(col("realisedGain") != 0)

        df_gss_pocket_period_realised_gain = df_gss_pocket_period_realised_gain\
            .withColumnRenamed("global_stock_id", "stock_id")

        df_intraday_period_end = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_intraday_t_2_files, self.tax_period_end_date)
        ).select("user_id", "account_id", "global_stock_id", "realised_gain")\
            .withColumnRenamed("realised_gain", "end_realised_gain")

        df_intraday_period_start = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_intraday_t_2_files, self.tax_period_start_date)
        ).select("user_id", "account_id", "global_stock_id", "realised_gain")\
            .withColumnRenamed("realised_gain", "start_realised_gain")

        df_intraday_period_realised_gain = df_intraday_period_end.join(
            df_intraday_period_start, on=["user_id", "account_id", "global_stock_id"], how="full").fillna(0)

        df_intraday_period_realised_gain = df_intraday_period_realised_gain.withColumn(
            "realisedGain", col("end_realised_gain") - col("start_realised_gain")).filter(col("realisedGain") != 0)
        df_intraday_period_realised_gain = df_intraday_period_realised_gain\
            .withColumnRenamed("global_stock_id", "stock_id")

        cols_to_select = ["user_id", "account_id", "stock_id", "realisedGain"]

        df_gss_period_realised_gain = df_gss_period_realised_gain.select(cols_to_select)

        df_intraday_period_realised_gain = df_intraday_period_realised_gain.select(cols_to_select)

        df_gss_pocket_period_realised_gain = df_gss_pocket_period_realised_gain.select(cols_to_select)

        df_period_realised_gain = df_gss_period_realised_gain.union(df_intraday_period_realised_gain)\
            .union(df_gss_pocket_period_realised_gain) \
            .withColumn("user_id", col("user_id").cast(LongType())) \
            .withColumn("account_id", col("account_id").cast(LongType())) \
            .withColumn("stock_id", col("stock_id").cast(LongType()))

        df_period_realised_gain = df_period_realised_gain.groupBy(["user_id", "account_id", "stock_id"])\
            .agg(round(sum("realisedGain"), 2).alias("realisedGain"))

        df_gss_codes = self.get_global_stock_codes()

        df_period_realised_gain = df_period_realised_gain.join(df_gss_codes, on=["stock_id"], how="left")\
            .withColumnRenamed("stock_id", "id")

        return df_period_realised_gain

    def get_gss_dividends(self):
        df_gss_dividend = self.io_utils.read_parquet_data(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_dividend_transactions,
                                  self.tax_period_end_date))\
            .filter(col("dividend_transaction_status") == Constants.SUCCESS)\
            .select("user_id", "account_id", "global_stock_id", "net_amount", "transaction_time")\
            .withColumnRenamed("global_stock_id", "stock_id")

        df_gss_dividend = df_gss_dividend.withColumn("dt",
                                        F.from_utc_timestamp(F.to_timestamp("transaction_time"), "Asia/Jakarta").cast("date")) \
            .withColumn("financial_year", F.year(col("dt"))) \
            .filter(col("financial_year") == self.tax_period_end_date.year)\
            .select("user_id", "account_id", "stock_id", "net_amount") \
            .groupBy("account_id", "user_id", "stock_id").agg(round(sum("net_amount"), 2).alias("dividend"))

        df_gss_codes = self.get_global_stock_codes()

        df_gss_dividend = df_gss_dividend.join(df_gss_codes, on=["stock_id"], how="left")\
            .withColumnRenamed("stock_id", "id")

        return df_gss_dividend

    def get_forex_yield(self):
        df_forex_yield = self.io_utils.read_parquet_data(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.forex_yield_transactions, self.tax_period_end_date)
        ).select("user_id", "account_id", "yield_qty_received", "transaction_time")

        df_forex_yield = df_forex_yield.withColumn("dt",
                                        F.from_utc_timestamp(F.to_timestamp("transaction_time"), "Asia/Jakarta").cast("date"))

        df_forex_yield = df_forex_yield.withColumn("financial_year", F.year(col("dt")))

        df_forex_yield = df_forex_yield.withColumn("month", F.month(col("dt")))

        df_forex_yield = df_forex_yield.filter(col("financial_year") == self.tax_period_end_date.year)\
            .select("user_id", "account_id", col("yield_qty_received").alias("yield"), "month")

        df_month_text = self.ops.create_jkt_month_text_df(self.tax_year)

        df_account = df_forex_yield.select("account_id", "user_id").distinct()

        df_month_account = df_account.join(df_month_text)

        df_forex_yield = df_forex_yield.join(df_month_account, on=["month", "account_id", "user_id"], how="full")\
            .fillna(0)

        df_forex_yield = df_forex_yield.groupBy(["account_id", "user_id", "month", "monthText"])\
            .agg(round(sum("yield"), 2).alias("yield"))

        return df_forex_yield

    def get_global_stock_options_realised_gain(self):
        snapshot_start_df = self.io_utils.read_csv_file("{}/{}/dt={}/".format(
            self.bucket_path, S3Paths.options_contract_accounts, self.tax_period_start_date)
        ).withColumn("account_id", col("account_id").cast(IntegerType())) \
            .withColumn("user_id", col("user_id").cast(IntegerType())) \
            .withColumn("options_contract_id", col("options_contract_id").cast(IntegerType())) \
            .withColumn("global_stock_id", col("global_stock_id").cast(IntegerType())) \
            .withColumn("start_realised_gain", col("realised_gain").cast(DoubleType())) \
            .select("user_id", "account_id", "options_contract_id", "global_stock_id", "start_realised_gain")

        snapshot_end_df = self.io_utils.read_csv_file("{}/{}/dt={}/".format(
            self.bucket_path, S3Paths.options_contract_accounts, self.tax_period_end_date)
        ).withColumn("account_id", col("account_id").cast(IntegerType())) \
            .withColumn("user_id", col("user_id").cast(IntegerType())) \
            .withColumn("options_contract_id", col("options_contract_id").cast(IntegerType())) \
            .withColumn("global_stock_id", col("global_stock_id").cast(IntegerType())) \
            .withColumn("end_realised_gain", col("realised_gain").cast(DoubleType())) \
            .select("user_id", "account_id", "options_contract_id", "global_stock_id", "end_realised_gain")

        gain_with_user_id_df = snapshot_start_df.join(
            snapshot_end_df, on=["user_id", "account_id", "options_contract_id", "global_stock_id"], how="full"
        ).fillna(0).withColumn("realised_gain", col("end_realised_gain") - col("start_realised_gain")) \
            .filter(col("realised_gain") != 0) \
            .select(
            "user_id", "account_id", "options_contract_id", "global_stock_id", "start_realised_gain",
            "end_realised_gain", "realised_gain")

        contracts_df = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.options_contracts, self.tax_period_end_date)
        ).select("id", "global_stock_id", "contract_symbol", "contract_type", "strike_price", "expiration_date")

        contracts_df = contracts_df.withColumn("year", F.year("expiration_date"))\
            .withColumn("month", F.month("expiration_date")) \
            .withColumn("day", F.dayofmonth("expiration_date"))

        jtk_months = self.ops.create_jkt_month_df()
        contracts_df = contracts_df.join(jtk_months, on=["month"], how="left")

        contracts_df = contracts_df.withColumn("expiration_date",
                                        F.concat(col("day"), F.lit(" "), col("month_name"), F.lit(" "), col("year"))) \
            .select("id", "global_stock_id", "contract_symbol", "contract_type", "strike_price", "expiration_date")

        contracts_with_symbols_df = contracts_df.join(
            self.get_global_stock_codes(), contracts_df["global_stock_id"] == col("stock_id"), how="left") \
            .select("id", "stock_id", "symbol", "contract_symbol", "contract_type", "strike_price", "expiration_date")

        gain_with_contracts_df = gain_with_user_id_df.join(
            contracts_with_symbols_df,
            gain_with_user_id_df["options_contract_id"] == contracts_with_symbols_df["id"],
            how="left")
        null_contract_ids_count = gain_with_contracts_df.filter(col("id").isNull()).count()

        if null_contract_ids_count > 0:
            self.logger.warning("Total No of Options Contract Accounts "
                                "for which contract not found is: {}".format(null_contract_ids_count))

        gain_with_contracts_df = gain_with_contracts_df.filter(col("id").isNotNull()) \
            .withColumn("symbol",
                        F.concat(col("symbol"), lit(" $"), col("strike_price").cast("int"),
                        lit(" "),col("contract_type"),lit(" "),col("expiration_date"))
            ).select(
                "user_id", "account_id", col("options_contract_id").alias("id"),
                "symbol", col("realised_gain").alias("realisedGain")
        )

        gain_with_contracts_df = gain_with_contracts_df.groupBy("user_id","account_id", "id", "symbol")\
            .agg(round(F.sum("realisedGain"), 2).alias("realisedGain"))

        us_stocks_options_contract_realised_gain = gain_with_contracts_df.groupBy("user_id", "account_id") \
            .agg(F.collect_list(F.struct("id", "symbol", "realisedGain")).alias("us_stocks_options_realised_gain"))

        total_us_stocks_options_realised_gain_df = gain_with_contracts_df.groupBy("user_id", "account_id") \
                                    .agg(sum("realisedGain").alias("global_stock_options_total_realised_gain"))

        return (
            us_stocks_options_contract_realised_gain,
            total_us_stocks_options_realised_gain_df,
        )

    def get_crypto_currency_futures_realised_gain(self):
        start_df = self.io_utils.read_parquet_data("{}/{}/dt={}/"
                .format(self.bucket_path, S3Paths.crypto_futures_positions_t_2_files, self.tax_period_start_date)
        ) \
            .withColumn("user_id", col("user_id").cast(IntegerType())) \
            .withColumn("account_id", col("account_id").cast(IntegerType())) \
            .withColumn("crypto_future_instrument_id", col("crypto_future_instrument_id").cast(IntegerType())) \
            .withColumn("start_realised_pnl", col("realised_pnl").cast(DoubleType())) \
            .select("user_id", "account_id", "crypto_future_instrument_id", "start_realised_pnl")

        end_df = self.io_utils.read_parquet_data("{}/{}/dt={}/"
                .format(self.bucket_path, S3Paths.crypto_futures_positions_t_2_files, self.tax_period_end_date)
        ) \
            .withColumn("user_id", col("user_id").cast(IntegerType())) \
            .withColumn("account_id", col("account_id").cast(IntegerType())) \
            .withColumn("crypto_future_instrument_id", col("crypto_future_instrument_id").cast(IntegerType())) \
            .withColumn("end_realised_pnl", col("realised_pnl").cast(DoubleType())) \
            .select("user_id", "account_id", "crypto_future_instrument_id", "end_realised_pnl")

        crypto_future_realised_gain = start_df.join(
            end_df, on=["user_id", "account_id", "crypto_future_instrument_id"], how="full"
        ) \
            .withColumn("end_realised_pnl", col("end_realised_pnl").cast("double")) \
            .withColumn("start_realised_pnl", col("start_realised_pnl").cast("double")).fillna(0) \
            .withColumn("realised_gain", col("end_realised_pnl") - col("start_realised_pnl")) \
            .select("user_id", "account_id", "crypto_future_instrument_id", "realised_gain")

        total_crypto_future_realised_gain = crypto_future_realised_gain.groupBy("user_id", "account_id") \
            .agg(sum("realised_gain").alias("total_crypto_futures_sales"))

        total_crypto_future_realised_gain = total_crypto_future_realised_gain\
            .filter(col("total_crypto_futures_sales") != 0)\
            .select("user_id", "account_id", "total_crypto_futures_sales")

        return crypto_future_realised_gain, total_crypto_future_realised_gain

    def get_funding_rate_fee(self):
        funding_rate_realised_gain = self.io_utils.read_parquet_data(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_future_funding_transactions, self.tax_period_end_date)
        ) \
            .withColumn("user_id", col("user_id").cast(IntegerType())) \
            .withColumn("account_id", col("account_id").cast(IntegerType())) \
            .withColumn("crypto_future_instrument_id", col("crypto_future_instrument_id").cast(IntegerType())) \
            .withColumn("realised_gain", col("fee").cast(DoubleType())) \
            .filter(
                (F.to_date(F.from_utc_timestamp(F.to_timestamp(col("transaction_time")), "Asia/Jakarta")) >= lit(self.tax_period_start_date)) &
                (F.to_date(F.from_utc_timestamp(F.to_timestamp(col("transaction_time")), "Asia/Jakarta")) <= lit(self.tax_period_end_date))
            ) \
            .select("user_id", "account_id", "crypto_future_instrument_id", "realised_gain")

        total_funding_rate_transactions = funding_rate_realised_gain.groupBy("user_id", "account_id")\
            .agg(sum("realised_gain").alias("total_funding_rate_fee")) \
            .filter(col("total_funding_rate_fee") != 0).select("user_id", "account_id", "total_funding_rate_fee")

        return funding_rate_realised_gain, total_funding_rate_transactions

    def create_us_stocks_tax_report_data(self):
        gss_realised_gain = self.get_global_stock_realised_gain()
        total_gss_realised_gain = gss_realised_gain.groupBy(["user_id", "account_id"])\
            .agg(round(sum("realisedGain"), 2).alias("global_stock_total_realised_gain"))

        # Fetching GSS Options Reliased Gain
        us_stocks_options_contract_realised_gain, total_us_stocks_options_realised_gain_df = \
            self.get_global_stock_options_realised_gain()

        total_us_stocks_options_realised_gain_df = total_us_stocks_options_realised_gain_df \
            .withColumn("global_stock_options_total_realised_gain",
                        round("global_stock_options_total_realised_gain", 2))

        # Get Crypto Futures Realised Gain
        crypto_currency_future_realised_gain, total_crypto_currency_futures_realised_gain = \
            self.get_crypto_currency_futures_realised_gain()

        total_crypto_currency_futures_realised_gain = total_crypto_currency_futures_realised_gain \
            .withColumn("total_crypto_futures_sales", round("total_crypto_futures_sales", 2))

        # Get Funding Rate
        funding_rate_realised_gain, total_funding_rate_fee = \
            self.get_funding_rate_fee()

        total_funding_rate_fee = total_funding_rate_fee\
            .withColumn("total_funding_rate_fee", round("total_funding_rate_fee",2))

        crypto_future_instruments = self.asset_utils.get_crypto_future_instrument_assets()\
            .select("id", "future_pair_symbol")

        combined_crypto_futures_and_funding_rate_realised_gain = crypto_currency_future_realised_gain\
            .union(funding_rate_realised_gain)

        combined_crypto_futures_and_funding_rate_realised_gain = combined_crypto_futures_and_funding_rate_realised_gain\
            .join(
                crypto_future_instruments,
                combined_crypto_futures_and_funding_rate_realised_gain["crypto_future_instrument_id"] ==
                crypto_future_instruments["id"], how="full"
        ).fillna(0).select("user_id", "account_id", "id", col("future_pair_symbol").alias("symbol"),
                    col("realised_gain").alias("realisedGain"))

        combined_crypto_futures_and_funding_rate_realised_gain = combined_crypto_futures_and_funding_rate_realised_gain \
            .groupBy("user_id", "account_id", "id", "symbol")\
            .agg(round(F.sum("realisedGain"), 2).alias("realisedGain"))\
            .filter(col("realisedGain") != 0)

        combined_crypto_futures_and_funding_rate_realised_gain = combined_crypto_futures_and_funding_rate_realised_gain\
            .groupBy("user_id", "account_id") \
            .agg(F.collect_list(F.struct("id", "symbol", "realisedGain"))
                 .alias("crypto_futures_and_funding_rate_realised_gain"))\
            .select("user_id", "account_id", "crypto_futures_and_funding_rate_realised_gain")

        df_us_stock_tax = gss_realised_gain\
            .withColumn("us_stocks_realised_gain", F.struct(["id", "symbol", "realisedGain"])) \
            .groupBy(["user_id", "account_id"]).agg(
            round(sum("realisedGain"), 2).alias("total_asset_realised_gain"),
            F.collect_list("us_stocks_realised_gain").alias("us_stocks_realised_gain"))

        df_us_stock_tax = df_us_stock_tax.join(total_gss_realised_gain, on=["user_id", "account_id"], how="full")\
            .fillna(0)

        df_us_stock_tax = df_us_stock_tax.join(
            total_us_stocks_options_realised_gain_df, on=["user_id", "account_id"], how="full").fillna(0)

        df_us_stock_tax = df_us_stock_tax.join(
            total_crypto_currency_futures_realised_gain, on=["user_id", "account_id"], how="full").fillna(0)

        df_us_stock_tax = df_us_stock_tax.join(
            total_funding_rate_fee, on=["user_id", "account_id"], how="full").fillna(0)

        gss_dividend = self.get_gss_dividends()

        gss_dividend = gss_dividend.withColumn("us_stocks_dividend", F.struct(["id", "symbol", "dividend"]))

        total_gss_dividend = gss_dividend.groupBy(["user_id", "account_id"])\
            .agg(
            round(sum("dividend"), 2).alias("total_dividend"),
            F.collect_list("us_stocks_dividend").alias("us_stocks_dividend")
        )

        df_us_stock_tax = df_us_stock_tax.join(total_gss_dividend, on=["user_id", "account_id"], how="full") \
            .withColumn("us_stocks_dividend",
                        when(col("us_stocks_dividend").isNull(), F.array()).otherwise(col("us_stocks_dividend"))) \
            .withColumn("us_stocks_realised_gain",
                when(col("us_stocks_realised_gain").isNull(), F.array()).otherwise(col("us_stocks_realised_gain")))\
            .fillna(0)

        forex_yield = self.get_forex_yield()

        forex_yield = forex_yield.withColumn("monthly_yield", F.struct(["month", "monthText", "yield"])) \
            .groupBy(["user_id", "account_id"])\
            .agg(round(sum("yield"), 2).alias("total_yield"), F.collect_list("monthly_yield").alias("monthly_yield")) \
            .withColumn("monthly_yield", F.expr("array_sort(monthly_yield)"))

        df_us_stock_tax = df_us_stock_tax.join(forex_yield, on=["user_id", "account_id"], how="full") \
            .withColumn("monthly_yield",
                        when(col("monthly_yield").isNull(), F.array()).otherwise(col("monthly_yield"))) \
            .withColumn("us_stocks_realised_gain",
                        when(col("us_stocks_realised_gain").isNull(), F.array())
                        .otherwise(col("us_stocks_realised_gain"))).fillna(0) \
            .join(us_stocks_options_contract_realised_gain, on=["user_id", "account_id"], how="full") \
            .join(combined_crypto_futures_and_funding_rate_realised_gain, on=["user_id", "account_id"], how="full") \
            .withColumn("us_stocks_options_realised_gain",
                        when(col("us_stocks_options_realised_gain").isNull(), F.array())
                        .otherwise(col("us_stocks_options_realised_gain"))) \
            .withColumn("crypto_futures_and_funding_rate_realised_gain",
                        when(col("crypto_futures_and_funding_rate_realised_gain").isNull(), F.array())
                        .otherwise(col("crypto_futures_and_funding_rate_realised_gain"))) \
            .withColumn("category", lit("globalStock")) \
            .withColumn("financial_year", lit(self.tax_period_end_date.year)) \
            .withColumn("total_asset_realised_gain",
                        round(col("total_asset_realised_gain") + col("global_stock_options_total_realised_gain"), 2)) \
            .withColumn("total_realised_gain",
                        round(col("total_asset_realised_gain") + col("total_dividend") + col("total_yield"), 2)) \
            .withColumn("total_crypto_futures_realised_profit_and_loss",
                        round(col("total_crypto_futures_sales") + col("total_funding_rate_fee"), 2)) \
            .withColumn("account_id", col("account_id").cast(LongType())) \
            .withColumn("user_id", col("user_id").cast(LongType()))

        self.io_utils.write_json_file(
            df_us_stock_tax,
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.us_stocks_tax_report, self.tax_period_end_date),
            partition=3
        )

        return df_us_stock_tax

    def write_us_stocks_tax_data_in_mongo(self, us_stocks_tax_data):
        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_config["collection"] = self.config["data_store"]["tax_report"]["us_stocks_tax_report_collection"]
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)

        mongo_write_config = {
            "uri": mongo_uri,
            "collection": mongo_config["collection"],
            "batch_size": "500",
            "mode": "append"
        }

        shard_key = "{'accountId':1, 'userId':1, 'financialYear':1}"

        self.io_utils.write_dataset_to_mongo(
            us_stocks_tax_data,
            mongo_config=mongo_write_config,
            asset_name="us stocks tax report",
            write_format="update",
            shardkey=shard_key,
            add_created_at=True
        )

    def run(self):
        df_us_stock_tax = self.create_us_stocks_tax_report_data()
        self.write_us_stocks_tax_data_in_mongo(df_us_stock_tax)
        self.spark_utils.stop_spark(self.spark)
