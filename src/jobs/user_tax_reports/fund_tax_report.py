from src.utils.spark_utils import *


class FundTaxReport:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("fund_tax_report")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)
        self.asset_utils = AssetUtils(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.tax_year, self.tax_period_end_date, self.tax_period_start_date = DateUtils.get_tax_year_params(self.t_1)
        self.pluang_partner_id = self.config["pluang_partner_id"]
        self.logger.info("Fund Tax Report initialised successfully with t_1: {}, t_2: {}, tax_year: {}"
                         "tax_period_end_date: {}, tax_period_start_date: {}".format(
            self.t_1, self.t_2, self.tax_year, self.tax_period_end_date, self.tax_period_start_date))

    def get_fund_tax_data(self):
        df_period_end = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.fund_returns, self.tax_period_end_date)
        ).select("user_id", "account_id", "fund_id", "realised_gain_idr")\
        .withColumnRenamed("realised_gain_idr", "end_realised_gain")

        df_period_start = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.fund_returns, self.tax_period_start_date)
        ).select("user_id", "account_id", "fund_id", "realised_gain_idr")\
        .withColumnRenamed("realised_gain_idr", "start_realised_gain")

        fund_realised_gain = df_period_end.join(
            df_period_start, on=["user_id", "account_id", "fund_id"], how="full"
        ).fillna(0)

        fund_realised_gain = fund_realised_gain.withColumn("realised_gain",
                                        F.floor(col("end_realised_gain") - col("start_realised_gain")))

        df_fund_codes = self.asset_utils.get_fund_assets()
        df_fund_codes = df_fund_codes.select(col("id").alias("fund_id"), col("name").alias("symbol"))

        fund_realised_gain = fund_realised_gain.join(df_fund_codes, on=["fund_id"], how="left")

        fund_realised_gain = fund_realised_gain.withColumnRenamed("fund_id", "id")

        total_fund_realised_gain = fund_realised_gain.groupBy(["user_id", "account_id", "id", "symbol"]) \
            .agg((F.floor(sum("realised_gain"))).alias("realisedGain"))

        total_fund_realised_gain = total_fund_realised_gain.withColumn("fund_realised_gain",
                                                        F.struct(["id", "symbol", "realisedGain"]))

        df_fund_tax = total_fund_realised_gain.groupBy(["user_id", "account_id"])\
        .agg(
            (F.floor(sum("realisedGain"))).alias("total_realised_gain"),
            F.collect_list("fund_realised_gain").alias("fund_realised_gain")
        )

        df_fund_tax = df_fund_tax.withColumn("financial_year", lit(self.tax_year))

        df_fund_tax = df_fund_tax.withColumn("category", lit("fund"))

        df_fund_tax.coalesce(1).write.mode("overwrite").json(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.fund_tax_report, self.tax_period_end_date)
        )

        return df_fund_tax

    def write_fund_tax_data_in_mongo(self, fund_tax_data):
        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_config["collection"] = self.config["data_store"]["tax_report"]["fund_tax_report_collection"]
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)

        mongo_write_config = {
            "uri": mongo_uri,
            "collection": mongo_config["collection"],
            "batch_size": "500",
            "mode": "append"
        }

        shard_key = "{'accountId':1, 'userId':1, 'financialYear':1}"

        self.io_utils.write_dataset_to_mongo(
            fund_tax_data,
            mongo_config=mongo_write_config,
            asset_name="fund tax report",
            write_format="update",
            shardkey=shard_key,
            add_created_at=True
        )

    def run(self):
        fund_tax_data = self.get_fund_tax_data()
        self.write_fund_tax_data_in_mongo(fund_tax_data)
        self.spark_utils.stop_spark(self.spark)
