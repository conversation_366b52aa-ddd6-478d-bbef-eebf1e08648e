from src.utils.spark_utils import *
from datetime import date, timedelta, datetime, timezone


class PNLPeriod:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("pnl_daily")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1: date = config["t_1"]
        self.t_2: date = config["t_2"]
        self.pnl_type = kwargs.get("pnl_type", None)
        self.allowed_pnl_types = ["yearly", "weekly", "quarterly", "monthly", "daily"]
        all_pnl_paths = {
            "yearly": "yearly_profit_and_loss", "weekly": "weekly_profit_and_loss",
            "quarterly": "quarterly_profit_and_loss", "monthly": "monthly_profit_and_loss",
            "daily": "daily_profit_and_loss"
        }
        self.pnl_path = all_pnl_paths.get(self.pnl_type) if (self.pnl_type is not None) else None
        self.execution_date = kwargs.get("execution_date", None)
        self.logger.info("PNL Daily initialised successfully with t_1: {}, t_2: {}".format(self.t_1, self.t_2))

    def update_t_2_dates(self):
        if self.pnl_type == "daily":
            self.t_2 = (self.t_1 - timedelta(days=1))
        if self.pnl_type == "weekly":
            self.t_2 = (self.t_1 - timedelta(days=self.t_1.weekday()) - timedelta(days=1))
        if self.pnl_type == "monthly":
            self.t_2 = (self.t_2.replace(day=1) - timedelta(days=1))
        if self.pnl_type == "quarterly":
            curr_quarter = int((self.t_1.month - 1) / 3 + 1)
            self.t_2 = (datetime(self.t_1.year, 3 * curr_quarter - 2, 1) - timedelta(days=1)).date()
        if self.pnl_type == "yearly":
            self.t_2 = (self.t_1.replace(month=1, day=1) - timedelta(days=1))
        self.logger.info("Updated t_1 and t_2 are: {}, {} and pnl path is: {}".format(self.t_1, self.t_2, self.pnl_path))

    def create_period_pnl(self):
        pnl_agg_t1 = self.io_utils.read_parquet_data(
            path="{}/{}/dt={}/".format(self.bucket_path, self.config["daily_agg_pnl_path"], self.t_1)
        )
        pnl_agg_t2 = self.io_utils.read_parquet_data(
            path="{}/{}/dt={}/".format(self.bucket_path, self.config["daily_agg_pnl_path"], self.t_2)
        )
        pnl_joined = pnl_agg_t1.join(pnl_agg_t2, on=["account_id"], how="full")
        pnl = pnl_joined.select(
            pnl_agg_t1.account_id.alias("account_id"), pnl_agg_t1.dt.alias("dt"),
            pnl_agg_t2.pl_value.alias("startTimePNLCash"), pnl_agg_t2.pl_value_percentage.alias("startTimePNL"),
            pnl_agg_t1.pl_value.alias("endTimePNLCash"), pnl_agg_t1.pl_value_percentage.alias("endTimePNL"),
            pnl_agg_t1.cashin, pnl_agg_t1.forex_cashin, pnl_agg_t1.total_crypto_receive, pnl_agg_t1.into_stock_topups
        )\
        .fillna({'endTimePNLCash': 0, 'endTimePNL': 0, 'startTimePNL': 0, 'startTimePNLCash': 0,
                     'cashin': 0, 'forex_cashin': 0, 'total_crypto_receive': 0, 'into_stock_topups': 0})\
        .withColumn("pnlDiffCash", col("endTimePNLCash") - col("startTimePNLCash"))\
        .withColumn("pnlDiff", 100*((col("endTimePNLCash") - col("startTimePNLCash")) /
                    (col("cashin") + col("forex_cashin") + col("into_stock_topups"))))\
        .fillna({'pnlDiff': 0})
        return pnl

    def generate_pnl(self):
        if self.execution_date is not None:
            self.t_1 = datetime.strptime(self.execution_date, '%Y-%m-%d').date()
        self.update_t_2_dates()
        pnl = self.create_period_pnl()
        start_time = (datetime.strptime(str(self.t_2), '%Y-%m-%d') + timedelta(days=1))
        end_time = datetime.strptime(str(self.t_1), '%Y-%m-%d').replace(hour=23, minute=59, second=59, microsecond=999999)
        current_ts = datetime.now(timezone.utc)
        pnl = pnl.withColumn("granularity", lit(self.pnl_type)) \
            .withColumn("endTime", lit(end_time)) \
            .withColumn("startTime", lit(start_time)) \
            .withColumn("updatedAt", lit(current_ts)) \
            .drop("dt", "into_stock_topups")
        self.io_utils.write_csv_file(pnl, "{}/{}/{}/dt={}/".format(
            self.bucket_path, S3Paths.profit_and_loss, self.pnl_path, self.t_1
        ))

    def run(self):
        if (self.pnl_type is None) or (self.pnl_type not in self.allowed_pnl_types):
            self.logger.warning("No PNL Job found for corresponding pnl type provided")
        else:
            self.generate_pnl()
        self.spark_utils.stop_spark(self.spark)
