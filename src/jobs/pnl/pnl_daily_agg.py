from src.utils.spark_utils import *
from src.schema.crypto_currency_schema import crypto_currency_returns_schema, crypto_currency_wallet_transfers_schema


class PNLDailyAgg:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("pnl_daily_agg")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.pluang_partner_id = self.config["pluang_partner_id"]
        self.logger.info("PNL Daily Agg initialised successfully with t_1: {}, t_2: {}".format(self.t_1, self.t_2))

    def get_crypto_withdrawals_t_1(self, crypto_withdrawals):
        crypto_withdrawals = crypto_withdrawals.filter(
            (col("status") == 'PENDING') & (col("transaction_type") == "WITHDRAWAL")
        )
        crypto_withdrawals = crypto_withdrawals.select("id", "account_id", "user_id", "client_id", "partner_id",
                "crypto_currency_id", "quantity", "total_quantity", "unit_price", "status", "created_at", "updated_at")
        crypto_withdrawals = crypto_withdrawals.withColumn("non_zero_weighted_cost", lit(0))
        crypto_withdrawals = self.ops.de_dupe_dataframe(crypto_withdrawals, ["id"], "updated_at", type="asc")
        return crypto_withdrawals

    def get_crypto_returns(self):
        crypto_returns_t_2 = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_returns_t_2_files, self.t_2)
        )
        cols_to_select = ["account_id", "user_id", "crypto_currency_id", "weighted_cost", "created", "updated"]
        crypto_returns_t_2 = crypto_returns_t_2.select(cols_to_select)
        crypto_returns_t_1 = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_returns_raw_data, self.t_1),
            is_raw=True,
            schema=crypto_currency_returns_schema,
            return_empty_if_file_not_present=True,
            alert_file_not_present=False
        )
        crypto_returns_t_1 = crypto_returns_t_1.select(cols_to_select).filter(col("weighted_cost") != 0)
        df_crypto_returns = crypto_returns_t_1.union(crypto_returns_t_2)
        return df_crypto_returns

    def get_crypto_withdrawal_snapshot(self, crypto_withdrawals, crypto_returns):
        self.logger.info("assigning weighted cost to crypto withdrawals")
        crypto_withdrawal_snapshot_t_1 = crypto_withdrawals.join(
            crypto_returns, on=["account_id", "user_id", "crypto_currency_id"], how="full"
        ).filter(col("non_zero_weighted_cost").isNotNull())
        crypto_withdrawal_snapshot_t_1 = crypto_withdrawal_snapshot_t_1.filter(col("created_at") > col("updated"))
        crypto_withdrawal_snapshot_t_1 = self.ops.de_dupe_dataframe(crypto_withdrawal_snapshot_t_1, ["id"], "updated")
        crypto_withdrawal_snapshot_t_1 = crypto_withdrawal_snapshot_t_1.withColumn("non_zero_weighted_cost",
                                                                                   col("weighted_cost"))
        crypto_withdrawal_snapshot_t_1 = crypto_withdrawal_snapshot_t_1.withColumn("total_withdrawal_amount",
                                            (col("non_zero_weighted_cost")*col("total_quantity")).cast(LongType()))
        crypto_withdrawal_snapshot_t_1 = crypto_withdrawal_snapshot_t_1.drop("created", "updated", "weighted_cost")
        self.logger.info("reading crypto withdrawals snapshot fot T-2 for the date {}".format(self.t_2))

        crypto_withdrawal_snapshot_t_2 = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_withdrawal_snapshot, self.t_2)
        )
        crypto_withdrawal_snapshot_t_1 = crypto_withdrawal_snapshot_t_1.select(crypto_withdrawal_snapshot_t_2.columns)
        crypto_withdrawal_snapshot = crypto_withdrawal_snapshot_t_1.union(crypto_withdrawal_snapshot_t_2)
        crypto_withdrawal_snapshot = self.ops.de_dupe_dataframe(
            crypto_withdrawal_snapshot, ["id"], "updated_at", type="asc"
        )
        self.logger.info("crypto withdrawal snapshot is created for the date {}".format(self.t_1))
        return crypto_withdrawal_snapshot

    def mark_crypto_withdrawals_success(self, crypto_withdrawal_snapshot, crypto_transfers):
        self.logger.info("marking withdrawals success which got success at {}".format(self.t_1))
        crypto_currency_wallets_success_withdrawals = crypto_transfers.filter(
            (col("status") == 'SUCCESS') & (col("transaction_type") == "WITHDRAWAL")
        )
        crypto_currency_wallets_success_withdrawals = self.ops.de_dupe_dataframe(
            crypto_currency_wallets_success_withdrawals, ["id"], "updated_at"
        ).select("id", "status").withColumnRenamed("status", "updated_status")
        crypto_withdrawal_snapshot = crypto_withdrawal_snapshot.join(
            crypto_currency_wallets_success_withdrawals, on=["id"], how="full"
        )
        crypto_withdrawal_snapshot = crypto_withdrawal_snapshot.filter(col("status").isNotNull())
        crypto_withdrawal_snapshot = crypto_withdrawal_snapshot.withColumn("status",
            when(col("updated_status").isNotNull(), col("updated_status")).otherwise(col("status")))\
            .drop("updated_status")
        return crypto_withdrawal_snapshot

    def calculate_crypto_withdrawals(self):
        self.logger.info("starting calculation of crypto withdrawals")
        df_crypto_transfers = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_wallet_transfers_raw_data, self.t_1),
            is_raw=True,
            schema=crypto_currency_wallet_transfers_schema,
            return_empty_if_file_not_present=True
        )
        df_crypto_withdrawals = self.get_crypto_withdrawals_t_1(df_crypto_transfers)
        df_crypto_returns = self.get_crypto_returns()
        df_crypto_withdrawal_snapshot = self.get_crypto_withdrawal_snapshot(df_crypto_withdrawals, df_crypto_returns)
        df_crypto_withdrawal_snapshot = self.mark_crypto_withdrawals_success(
            df_crypto_withdrawal_snapshot,
            df_crypto_transfers
        )
        self.logger.info("writing updated crypto withdrawals snapshot")
        self.io_utils.write_csv_file(
            df=df_crypto_withdrawal_snapshot,
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_withdrawal_snapshot, self.t_1),
            partition=3
        )
        self.logger.info("successfully written crypto withdrawals snapshot")
        return df_crypto_withdrawal_snapshot

    def get_indo_stock_wallet_activity(self):
        indo_stock_activity = self.io_utils.read_parquet_data(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.indo_stock_wallet_activity, self.t_1)
        )
        indo_stock_topups = indo_stock_activity.filter(col("transaction_type") == Constants.TOP_UP)
        indo_stock_cashouts = indo_stock_activity.filter(col("transaction_type") == Constants.BANK_WITHDRAWAL)
        indo_stock_topups = indo_stock_topups.groupBy(["account_id"]).agg(
            sum("transaction_amount").cast(LongType()).alias("into_stock_topups")
        )
        indo_stock_cashouts = indo_stock_cashouts.groupBy(["account_id"]).agg(
            sum("transaction_amount").cast(LongType()).alias("into_stock_cashouts")
        )
        return indo_stock_topups, indo_stock_cashouts

    def start_processing(self):
        self.logger.info("Starting execution for daily aggregate profit and loss value calculations")
        cashout_s3_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.cashouts_agg, self.t_1)
        cashin_s3_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.topups_agg, self.t_1)
        portfilo_s3_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.portfolio_snapshot_folder, self.t_1)
        profit_loss_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["daily_agg_pnl_path"], self.t_1)
        forex_cashin_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.forex_top_ups_snapshot, self.t_1)
        forex_cashout_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.forex_cash_outs_snapshot, self.t_1)
        crypto_currency_wallets_transfers_path = "{}/{}/dt={}/".format(
            self.bucket_path, S3Paths.crypto_currency_wallet_transfers_de_dupe, self.t_1
        )

        try:
            self.logger.info("Reading cashouts from path {}".format(cashout_s3_path))
            cashout = self.io_utils.read_parquet_data(cashout_s3_path).select("account_id", "cashout")

            self.logger.info("Reading cashin from path {}".format(cashin_s3_path))
            cashin = self.io_utils.read_parquet_data(cashin_s3_path).select("account_id", "cashin")

            self.logger.info("Reading portfolio from path {}".format(portfilo_s3_path))
            position = self.io_utils.read_csv_file(portfilo_s3_path)
            position = position.select("account_id", "portfolioValue")

            self.logger.info("Reading forex cashouts from path {}".format(forex_cashout_path))
            forex_cashout = self.io_utils.read_csv_file(forex_cashout_path)
            forex_cashout = forex_cashout.filter(col("status") == "COMPLETED")\
                .select("account_id", "withdrawal_amount", "unit_price")
            forex_cashout = forex_cashout.withColumn("forex_cashout", col("withdrawal_amount")*col("unit_price"))
            snap_forex_cashout = forex_cashout.groupBy("account_id").agg(sum("forex_cashout").cast(LongType())
                                                                         .alias("forex_cashout"))

            self.logger.info("Reading forex cashin from path {}".format(forex_cashin_path))
            forex_cashin = self.io_utils.read_csv_file(forex_cashin_path)
            forex_cashin = forex_cashin.filter(col("status") == "COMPLETED")\
                .select("account_id", "final_amount", "unit_price")
            forex_cashin = forex_cashin.withColumn("forex_cashin", col("final_amount") * col("unit_price"))
            snap_forex_cashin = forex_cashin.groupBy("account_id").agg(sum("forex_cashin").cast(LongType())
                                                                       .alias("forex_cashin"))

            forex_cash = snap_forex_cashout.join(snap_forex_cashin, ["account_id"], "full")\
                .fillna({'forex_cashin': 0}).fillna({'forex_cashout': 0})

            indo_stock_topups, indo_stock_cashouts = self.get_indo_stock_wallet_activity()

            # crypto currency wallets transfers (add crypto withdrawals and remove crypto deposits)
            self.logger.info("Reading crypto currency wallets transfers from path {}"
                             .format(crypto_currency_wallets_transfers_path)
                             )
            crypto_currency_transfer = self.io_utils.read_csv_file(crypto_currency_wallets_transfers_path)
            crypto_currency_transfer = crypto_currency_transfer.filter(col("status").isin(["SUCCESS"]))
            crypto_currency_deposit = crypto_currency_transfer.filter(col("transaction_type") == "DEPOSIT")\
                .withColumn("total_crypto_receive", (col("unit_price") * col("total_quantity")).cast(LongType()))\
                .select("account_id", "total_crypto_receive")\
                .groupBy("account_id").agg(sum("total_crypto_receive").alias("total_crypto_receive"))
            crypto_currency_withdrawal = self.calculate_crypto_withdrawals()
            crypto_currency_withdrawal = crypto_currency_withdrawal.filter(col("status") == "SUCCESS")\
                .select("account_id", "total_withdrawal_amount").groupBy("account_id")\
                .agg(sum("total_withdrawal_amount").cast(LongType()).alias("total_crypto_send"))

            # Join
            pl = cashin.join(cashout, ["account_id"], "full")\
                .join(position, ["account_id"], "full")\
                .join(forex_cash, ["account_id"], "full")\
                .join(crypto_currency_deposit, ["account_id"], "full")\
                .join(crypto_currency_withdrawal, ["account_id"], "full") \
                .join(indo_stock_topups, ["account_id"], "full") \
                .join(indo_stock_cashouts, ["account_id"], "full") \
                .fillna(0)

            # calculating pl_value
            profit = pl.withColumn("account_id", col("account_id").cast(LongType())) \
                .withColumn("pl_value",
                        col("portfolioValue") + col("cashout") + col("forex_cashout")
                        + col("total_crypto_send") + col("into_stock_cashouts")
                        - col("total_crypto_receive") - col("cashin") - col("forex_cashin") - col("into_stock_topups"))\
                .withColumn("pl_value_percentage", (col("pl_value") * 100)
                    / (col("cashin") + col("forex_cashin") + col("total_crypto_receive") + col("into_stock_topups")))\
                .withColumn("dt", lit(self.t_1)).fillna(0)

            self.io_utils.write_parquet_file(profit, profit_loss_path)
        except Exception as e:
            self.logger.exception(e)
            raise e

    def run(self):
        self.start_processing()
        self.spark_utils.stop_spark(self.spark)
