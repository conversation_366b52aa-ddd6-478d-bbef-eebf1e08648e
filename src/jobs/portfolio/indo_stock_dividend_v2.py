from src.utils.spark_utils import *


class IndoStockDividend:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("indo_stock_dividend_v2")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.logger.info("Indo Stock Dividend initialised successfully with t_1: {}, t_2: {}, cutoff_ts: {}"
                         .format(self.t_1, self.t_2, self.config["cutoff_ts"]))

    def get_indo_stock_accounts(self):
        accounts_path = "{}/{}/dt={}".format(self.bucket_path, S3Paths.indo_stock_accounts_v2_t_2_files, self.t_1)
        self.logger.info("reading indo stock accounts from path: {}".format(accounts_path))
        accounts = self.io_utils.read_csv_file(accounts_path)
        self.logger.info("Successfully read accounts snapshot for dividend")
        return accounts

    def get_indo_stock_returns(self):
        returns_path = "{}/{}/dt={}".format(self.bucket_path, S3Paths.indo_stock_returns_v2_t_2_files, self.t_1)
        self.logger.info("reading indo stock returns from path: {}".format(returns_path))
        returns = self.io_utils.read_csv_file(returns_path)
        self.logger.info("Successfully read returns snapshot for dividend")
        return returns

    def calculate_global_stock_dividend(self):
        self.logger.info("Starting execution for indo stock dividend generation")
        indo_stock_accounts = self.get_indo_stock_accounts()
        indo_stock_returns = self.get_indo_stock_returns()
        indo_stock_accounts = indo_stock_accounts.drop("created", "updated", "id", "user_id")

        indo_stock = indo_stock_returns.join(indo_stock_accounts, on=["account_id", "stock_id"], how="inner")

        self.logger.info("Filtering data to get accounts with total value > 0")
        indo_stock = indo_stock.withColumn("total_quantity", col("total_quantity").cast("double"))
        indo_stock = indo_stock.where((col("total_quantity") > 0))
        indo_stock = indo_stock.withColumn("_stock_id", col("stock_id"))

        dividend_path = "{}/{}/dt={}".format(self.bucket_path, S3Paths.indo_stock_dividend, self.t_1)
        indo_stock.coalesce(1).write.mode('overwrite').partitionBy("_stock_id").csv(dividend_path, header=True)
        self.logger.info("Successfully written dividend records in s3: {}".format(dividend_path))
        self.logger.info("Completed indo stock dividend snapshotting")

    def run(self):
        self.calculate_global_stock_dividend()
