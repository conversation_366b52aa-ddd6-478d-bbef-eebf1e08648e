from src.utils.spark_utils import *
from datetime import timedelta


class APYConfigUpdate:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("apy_config_update")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.cutoff_ts = config["cutoff_ts"]
        self.pluang_partner_id = self.config["pluang_partner_id"]
        self.logger.info("APY Config Update initialised successfully with t_1: {}, t_2: {} and cutoff_ts: {}"
                         .format(self.t_1, self.t_2, self.cutoff_ts))

    def get_all_campaigns(self):
        raw_campaign = self.io_utils.read_json_data(
            path="{}/{}/dt={}".format(self.bucket_path, S3Paths.campaigns_raw_data, self.t_1),
            is_raw=True,
            schema=None,
            return_empty_if_file_not_present=True,
            alert_file_not_present=False
        )
        campaigns = self.io_utils.read_json_data(
            path="{}/{}/dt={}".format(self.bucket_path, S3Paths.campaigns, self.t_2),
            is_raw=False,
            schema=None,
            return_empty_if_file_not_present=True,
            alert_file_not_present=True
        )
        all_campaign = None
        if raw_campaign is not None and campaigns is not None:
            all_campaign = campaigns.union(raw_campaign)
        elif campaigns is not None:
            self.logger.info("No new campaign is found")
            all_campaign = campaigns
        elif raw_campaign is not None:
            all_campaign = raw_campaign

        if all_campaign is not None:
            all_campaign = self.ops.de_dupe_dataframe(all_campaign, keys=["id"], over="updated")
            self.io_utils.write_json_file(
                all_campaign,
                "{}/{}/dt={}/".format(self.bucket_path, S3Paths.campaigns, self.t_1)
            )
            self.logger.info("Total Campaigns are: {}".format(all_campaign.count()))
        return all_campaign

    def get_active_campaigns(self, all_campaigns):
        all_campaigns = all_campaigns.filter(col("status") == "ACTIVE") \
            .withColumn("start_ts", col("start_ts").cast(TimestampType())) \
            .withColumn("end_ts", col("end_ts").cast(TimestampType()))
        filtered = all_campaigns \
            .withColumn("start_ts_adj", F.expr("start_ts - INTERVAL 24 HOURS")) \
            .withColumn("end_ts_adj", F.expr("end_ts + INTERVAL 24 HOURS")) \
            .filter(
                (lit(self.cutoff_ts) >= col("start_ts_adj")) &
                (lit(self.cutoff_ts) <= col("end_ts_adj"))
            )
        apy_config = [row.asDict() for row in filtered.collect()]
        self.logger.info(apy_config)
        active_campaigns = []
        for conf in apy_config:
            active_campaigns.append(conf)
        self.logger.info("Total Active campaigns are: {}".format(len(active_campaigns)))
        return active_campaigns

    def read_campaign_user_list(self, path):
        s3_path = "{}/{}".format(self.bucket_path, path)
        self.logger.info("reading campaign user list from path: {}".format(s3_path))
        user_list = self.io_utils.read_csv_file(s3_path, alert_if_file_not_present=True)
        user_list = user_list.withColumnRenamed("userId", "user_id")
        return user_list

    def get_users_prev_tags(self, current_user_tags):
        user_tag_mappings = self.io_utils.read_csv_file(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.user_tag_mappings, self.t_1)
        )
        user_tag_mappings = user_tag_mappings.filter(col("tag_name").isin(self.config["plus_member_tag_name"])) \
            .select("user_id").distinct().withColumn("tag_name", lit("PLUANG_PLUS_LEVEL_0"))
        current_user_tags = current_user_tags.select("user_id")
        prev_tags = current_user_tags.join(user_tag_mappings, on=["user_id"], how="inner")
        return prev_tags

    def write_tags_in_mongo(self, current_tags):
        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_config['collection'] = self.config["data_store"]["user_price_tiering"]["collection"]
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)
        mongo_config["uri"] = mongo_uri
        mongo_config["batch_size"] = "500"
        mongo_config["mode"] = "append"
        self.io_utils.write_dataset_to_mongo(current_tags, mongo_config=mongo_config, asset_name="Apy Config Update",
                                             write_format="update", shardkey="{'accountId':1}", add_created_at=False)

    def add_account_id(self, all_user_tags):
        accounts = self.io_utils.read_csv_file("{}/{}/dt={}".format(self.bucket_path, S3Paths.accounts, self.t_1))
        accounts = accounts.select("user_id", col("id").alias("account_id"))
        all_user_tags = all_user_tags.join(accounts, on=["user_id"], how="left")
        check_null_account_id = all_user_tags.filter(col("account_id").isNull())
        if check_null_account_id.count() > 0:
            self.logger.warning("There are {} user id's for which account id not found".format(
                check_null_account_id.count())
            )
            all_user_tags = all_user_tags.filter(col("account_id").isNull())
        all_user_tags = all_user_tags.drop("user_id")
        return all_user_tags

    def get_users_current_tag(self, active_campaigns):
        current_user_tags = None
        for campaign in active_campaigns:
            if campaign.get("file_path") is None or campaign.get("tag") is None:
                self.logger.warning("There is active campaign for which file path is not found: {}".format(campaign))
                continue
            user_list = self.read_campaign_user_list(campaign.get("file_path"))
            user_list = user_list.withColumn("tag_name", lit(campaign.get("tag")))
            if current_user_tags is None:
                current_user_tags = user_list
            else:
                current_user_tags = current_user_tags.union(user_list)
        all_user_tags = None
        if current_user_tags is not None:
            prev_user_tags = self.get_users_prev_tags(current_user_tags)
            all_user_tags = current_user_tags.union(prev_user_tags)

        if all_user_tags is not None:
            all_user_tags = self.add_account_id(all_user_tags)
            all_user_tags = all_user_tags.groupBy(["account_id"]).agg(F.collect_set("tag_name").alias("tag_names"))
            current_ts = DateUtils.get_utc_timestamp()
            all_user_tags = all_user_tags.withColumn("updated_at", lit(current_ts))

        return all_user_tags

    def run(self):
        all_campaigns = self.get_all_campaigns()
        if all_campaigns is not None:
            active_campaigns = self.get_active_campaigns(all_campaigns)
            current_tags = self.get_users_current_tag(active_campaigns)
            if current_tags is not None:
                self.write_tags_in_mongo(current_tags)
        else:
            self.logger.info("No Campaign Found")
        self.spark_utils.stop_spark(self.spark)
