from src.utils.spark_utils import *
from datetime import timedelta


class IndoStockDailyStatement:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("indo_stock_daily_statement")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]

        self.cutoff_ts_0 = config["cutoff_ts"]
        self.cutoff_ts_1 = self.cutoff_ts_0 - timedelta(days=1)
        self.logger.info("Indo Stock Daily Statement initialised successfully with t_1: {}, t_2: {}, cutoff_ts_0: {},"
                         "cutoff_ts_1: {}"
                         .format(self.t_1, self.t_2, self.cutoff_ts_0, self.cutoff_ts_1))

    def get_current_day_indo_stock_transactions(self):
        raw_transactions_path = "{}/{}/dt={}".format(
            self.bucket_path, S3Paths.indo_stock_transactions_v2_raw_data, self.t_1
        )
        self.logger.info("reading raw indo stock transactions from path: {}".format(raw_transactions_path))
        indo_stock_transactions = self.io_utils.read_json_data(
            raw_transactions_path, is_raw=True,
            return_empty_if_file_not_present=True, alert_file_not_present=False
        )

        self.logger.info("Successfully read raw indo stock transactions")
        return indo_stock_transactions

    def filter_transactions(self, indo_stock_transactions: DataFrame):
        indo_stock_transactions = indo_stock_transactions.filter(
            (col("transaction_time") >= self.cutoff_ts_1) &
            (col("transaction_time") < self.cutoff_ts_0) &
            (col("trade_qty").isNotNull()) &
            (col("trade_qty") > 0)
        )
        indo_stock_transactions = indo_stock_transactions.select(
            col("id").alias("order_number"), "account_id", "user_id", "order_type", col("trade_qty").alias("quantity"),
            col("side").alias("type"), col("stock_code").alias("code"), col("response_price").alias("price"),
            col("broker_fee").alias("fee")
        )
        indo_stock_transactions = indo_stock_transactions.withColumn("total",
                            (col("quantity")*col("price") + col("fee")).cast(LongType())
        )
        buy_txn = indo_stock_transactions.filter((col("type").isin([Constants.BUY])))\
            .withColumn("buy_orders", F.struct(["order_number", "order_type", "code", "quantity", "price", "fee", "total", "type"])) \
            .groupBy(["account_id", "user_id"]).agg(F.collect_list("buy_orders").alias("buy_orders"), F.sum("total").alias("buy_total")) \
            .withColumn("buy_total", col("buy_total").cast(LongType()))

        sell_txn = indo_stock_transactions.filter((col("type").isin([Constants.SELL])))\
            .withColumn("sell_orders", F.struct(["order_number", "order_type", "code", "quantity", "price", "fee", "total", "type"])) \
            .groupBy(["account_id", "user_id"]).agg(F.collect_list("sell_orders").alias("sell_orders"), F.sum("total").alias("sell_total")) \
            .withColumn("sell_total", col("sell_total").cast(LongType()))
        return buy_txn, sell_txn

    def generate_statement(self, buy_txn, sell_txn):
        df_statement = buy_txn.join(sell_txn, on=["account_id", "user_id"], how="full")
        df_statement = df_statement.withColumn("buy_orders", when(col("buy_orders").isNull(), F.array()).otherwise(col("buy_orders"))) \
            .withColumn("sell_orders", when(col("sell_orders").isNull(), F.array()).otherwise(col("sell_orders"))) \
            .fillna({'buy_total': 0, 'sell_total': 0})
        df_statement = df_statement.select("account_id", "user_id", F.struct(["buy_orders", "sell_orders", "buy_total", "sell_total", ]).alias("indo_stocks"))
        return df_statement

    def check_if_already_published(self):
        flag = False
        try:
            published_events_s3_path = "{}/{}/dt={}/".format(
                self.bucket_path, S3Paths.indo_stocks_v2_daily_statement_folder, self.t_1
            )
            self.logger.info("reading published data from path {}".format(published_events_s3_path))
            df = self.io_utils.read_json_data(published_events_s3_path)
            cnt = df.count()
            self.logger.info("already published {} msgs".format(cnt))
            if cnt > 0:
                self.logger.info("msgs are already published")
                flag = True
        except Exception as e:
            self.logger.info("msgs are not published yet")
        return flag

    def write_data_to_s3_and_kafka(self, df_statement):
        is_already_published = self.check_if_already_published()
        if not is_already_published:
            df_statement = df_statement.withColumn("is_daily_report", lit(True))
            df_statement = df_statement.withColumn("is_monthly_report", lit(False))
            published_events_s3_path = "{}/{}/dt={}/".format(self.bucket_path,
                                            S3Paths.indo_stock_daily_statement_folder, self.t_1)
            self.io_utils.write_json_file(df_statement, published_events_s3_path)
            self.logger.info("successfully written statement data into s3")

            self.logger.info("started writing statement data into kafka topic")
            df_whitelisted_users = self.io_utils.read_csv_file("{}/{}".format(
                self.bucket_path, S3Paths.indo_stock_daily_statement_whitelisted_user_folder), None, True, False)

            if (df_whitelisted_users is not None) and (df_whitelisted_users.count() > 0):
                self.logger.info("whitelisted user list found")
                df_whitelisted_users = df_whitelisted_users.filter(col("is_whitelisted_user"))
                df_statement = df_statement.join(df_whitelisted_users, on=["user_id"], how="inner")\
                    .drop("is_whitelisted_user")
            else:
                self.logger.info("whitelisted user list not found. Checking blacklisted users")
                df_blacklisted_users = self.io_utils.read_csv_file("{}/{}".format(
                    self.bucket_path, S3Paths.indo_stock_daily_statement_blacklisted_user_folder), None, True, False)
                if (df_blacklisted_users is not None) and (df_blacklisted_users.count() > 0):
                    df_blacklisted_users = df_blacklisted_users.filter(col("is_blacklisted_user"))
                    df_statement = df_statement.join(df_blacklisted_users, on=["user_id"], how="left")\
                        .filter(col("is_blacklisted_user").isNull())\
                        .drop("is_blacklisted_user")

            df_statement = df_statement.select(
                col("user_id").cast(StringType()).alias("key"),
                F.to_json(F.struct(df_statement.columns)).alias("value"),
                F.array(
                    F.struct(lit("x-request-id").alias("key"),
                    F.expr("uuid()").cast("binary").alias("value"))
                ).alias("headers")
            )

            self.io_utils.write_data_in_kafka(df_statement,
                self.config.get("kafka_topics").get("daily_statement_topic")
            )
            self.logger.info("successfully written statement data into kafka")
        else:
            self.logger.info("Statement data will not be written into kafka since it is already published")

    def run(self):
        indo_stock_transactions = self.get_current_day_indo_stock_transactions()
        if indo_stock_transactions is not None:
            buy_txn, sell_txn = self.filter_transactions(indo_stock_transactions)
            statement = self.generate_statement(buy_txn, sell_txn)
            self.write_data_to_s3_and_kafka(statement)
        else:
            self.logger.info("No transactions found for the date: {}".format(self.t_1))

