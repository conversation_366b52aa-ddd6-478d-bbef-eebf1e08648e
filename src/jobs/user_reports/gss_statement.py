from src.utils.spark_utils import *
from datetime import timedelta, datetime
import pytz

class GssStatement:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("gss_statement")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.t_0 = self.t_1 + timedelta(days=1)
        self.ts_0 = datetime.now(tz=pytz.timezone(Constants.JKT_TIMEZONE))\
            .replace(hour=8, minute=0, second=0, microsecond=0)
        self.cutoff_ts = config["cutoff_ts"]
        self.partner_id = self.config["pluang_partner_id"]

        self.logger.info("GSS Statement initialised successfully with t_1: {}, t_2: {}, cutoff_ts: {}"
                         .format(self.t_1, self.t_2, self.config["cutoff_ts"]))

    def get_forex_balance(self):
        forex_accounts = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.forex_accounts_snapshot, self.t_1)
        )
        forex_accounts_t_1 = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.forex_accounts_raw_data, self.t_0),
            is_raw=True,
            return_empty_if_file_not_present=True
        )
        if forex_accounts_t_1 is not None:
            forex_accounts_t_1 = forex_accounts_t_1\
                .withColumn("ts", F.from_utc_timestamp(F.to_timestamp("updated"), Constants.JKT_TIMEZONE))\
                .filter(col("ts") <= self.ts_0).drop("ts") \
                .select("account_id", "balance", "blocked_balance", "partner_id", "updated")
            forex_accounts = forex_accounts.select("account_id", "balance", "blocked_balance", "partner_id", "updated")
            forex_accounts = forex_accounts.union(forex_accounts_t_1)
        else:
            forex_accounts = forex_accounts.select("account_id", "balance", "blocked_balance", "partner_id", "updated")

        forex_accounts = forex_accounts.filter(col("partner_id").isin(self.partner_id)).drop("partner_id")

        forex_accounts = self.ops.de_dupe_dataframe(forex_accounts, ["account_id"], "updated").drop("updated")

        forex_accounts = forex_accounts.groupBy(["account_id"]).agg(
            sum("balance").alias("balance"), sum("blocked_balance").alias("blocked_balance")
        )
        forex_accounts = forex_accounts.withColumn("total_usd_cash", F.round(col("balance")+col("blocked_balance"), 2))\
            .withColumn("pending_usd_cash", F.round(col("blocked_balance"), 2))\
            .select("account_id", "total_usd_cash", "pending_usd_cash")\
            .filter((col("total_usd_cash") > 0) | (col("pending_usd_cash") > 0))
        return forex_accounts
    
    def get_leverage_wallet_balance(self, df_gss_balance, df_gss_codes):
        leverage_wallet = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.leverage_wallet_accounts_snapshot, self.t_1)
        )
        leverage_wallet = leverage_wallet.select(
            "account_id", "partner_id", "balance", "locked_margin", "trading_margin", "updated"
        )
        leverage_wallet_t_1 = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.leverage_wallet_accounts_raw_data, self.t_0),
            is_raw=True,
            return_empty_if_file_not_present=True
        )
        if leverage_wallet_t_1 is not None:
            leverage_wallet_t_1 = leverage_wallet_t_1.select(leverage_wallet.columns)
            leverage_wallet = leverage_wallet.union(leverage_wallet_t_1)
            leverage_wallet = self.ops.de_dupe_dataframe(leverage_wallet, ["account_id"], "updated")

        leverage_wallet = leverage_wallet.drop("partner_id", "updated")

        df_gss_balance = df_gss_balance.select("account_id", "code", "unrealized_pnl")

        df_gss_codes = df_gss_codes.select("code", "stock_type")

        df_gss_balance = df_gss_balance.join(df_gss_codes, on=["code"], how="left")\
            .filter(col("stock_type") == "CFD_LEVERAGE")

        df_gss_balance = df_gss_balance.groupBy(["account_id"]).agg(sum("unrealized_pnl").alias("unrealized_pnl"))

        leverage_wallet = leverage_wallet.join(df_gss_balance, on=["account_id"], how="full").fillna(0) \
            .groupBy(["account_id"]).agg(
                sum("balance").alias("balance"), sum("locked_margin").alias("locked_margin"),
                sum("trading_margin").alias("trading_margin"), sum("unrealized_pnl").alias("unrealized_pnl")
            ) \
            .withColumn("total_usd_margin", F.round(
                col("balance") + col("unrealized_pnl") - col("locked_margin") - col("trading_margin"), 2)
            ) \
            .withColumn("pending_usd_margin", F.round(col("locked_margin"), 2)) \
            .select("account_id", "total_usd_margin", "pending_usd_margin") \
            .filter((col("total_usd_margin") > 0) | (col("pending_usd_margin") > 0))
        return leverage_wallet
    
    def get_global_stock_prices(self):
        self.logger.info("reading global stock prices from s3 for the date {}".format(self.t_0))
        price = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_price, self.t_0)
        )
        price = price.withColumnRenamed("_id", "code")\
            .withColumnRenamed("mid_price", "closing_price").select("code", "closing_price")
        self.logger.info("successfully fetched global stock prices from s3")
        return price
    
    def get_option_trade_price(self):
        price = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.options_contract_price, self.t_0)
        )
        price = price.withColumn("closing_price", round(col("price"), 2))
        price = price.select(col("globalStockId").alias("code"), col("optionsContractId").alias("options_contract_id"),
                             "closing_price")
        self.logger.info("Successfully loaded global stock options trade prices")
        return price
    
    def get_global_stock_codes(self):
        self.logger.info("reading global stock codes from kafka topic")
        gss_code = self.io_utils.read_from_kafka_in_memory(
            self.config["bootstrap_servers"], self.config["kafka_topics"]["global_stock_topic"]
        )
        gss_code = gss_code.select(
            col("value.id").alias("id"), col("value.pluang_company_code").alias("pluang_company_code"),
            col("value.stock_type").alias("stock_type"),
            col("value.__source_ts_ms").cast(LongType()).alias("__source_ts_ms")
        )
        gss_code = self.ops.de_dupe_dataframe(gss_code, ["id"], "__source_ts_ms")
        gss_code = gss_code.withColumnRenamed("id", "code") \
            .select("code", "pluang_company_code", "stock_type")
        self.logger.info("successfully read global stock codes from kafka")
        return gss_code
    
    def get_global_stock_options_contracts(self):
        self.logger.info("Starting calculation of global stock options contracts")
        options_contracts_t_1 = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.options_contracts_raw_data, self.t_0),
            is_raw=True,
            return_empty_if_file_not_present=True
        )
        if options_contracts_t_1 is not None:
            options_contracts_t_1 = options_contracts_t_1.select(
                "id", "global_stock_id", "contract_type", "expiration_date",
                "strike_price", "updated", "shares_per_contract"
            ) \
            .withColumn("expiration_date", col("expiration_date").cast(IntegerType())) \
            .withColumn("expiration_date", F.expr("date_add(to_date('1970-01-01'), expiration_date)").cast(DateType()))

        df_options_contracts = self.io_utils.read_csv_file(
                "{}/{}/dt={}/".format(self.bucket_path, S3Paths.options_contracts, self.t_1)
            ) \
            .select(
                "id", "global_stock_id", "contract_type", "expiration_date",
                "strike_price", "updated", "shares_per_contract"
            )

        df_options_contracts = self.ops.get_union(df_options_contracts, options_contracts_t_1)\
            .withColumnRenamed("id", "options_contract_id")

        df_options_contracts = self.ops.de_dupe_dataframe(df_options_contracts, ["options_contract_id", "global_stock_id"],
                                                          "updated").drop("updated")
        return df_options_contracts
    
    def get_global_stock_options_accounts(self):
        self.logger.info("Starting calculation of global stock options accounts")

        gss_options_accunts_t_1 = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.options_contract_accounts_raw_data, self.t_0),
            is_raw=True,
            return_empty_if_file_not_present=True
        )

        if gss_options_accunts_t_1 is not None:
            gss_options_accunts_t_1 = gss_options_accunts_t_1.withColumn("ts",
                        F.from_utc_timestamp(F.to_timestamp("updated"), Constants.JKT_TIMEZONE)
                ).filter(col("ts") <= self.ts_0).drop("ts") \
                .select("account_id", "options_contract_id","global_stock_id", "total_quantity", "partner_id", "updated","weighted_cost") \
                .filter(col("partner_id").isin(self.partner_id)) \
                .drop("partner_id")

        gss_options_accounts = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.options_contract_accounts, self.t_1)
        ).select(
            "account_id", "options_contract_id", "global_stock_id", "total_quantity",
            "partner_id", "updated", "weighted_cost"
        ).filter(col("partner_id").isin(self.partner_id)).drop("partner_id")

        gss_options_accounts = self.ops.get_union(gss_options_accunts_t_1, gss_options_accounts)

        gss_options_accounts = self.ops.de_dupe_dataframe(
            gss_options_accounts, ["account_id", "global_stock_id", "options_contract_id"], "updated"
        ).drop("updated")

        self.logger.info("Successfully calculated global stock options accounts")

        return gss_options_accounts
    
    def get_global_stock_accounts(self):
        self.logger.info("Starting calculation of global stock accounts")

        gss_accunts_t_1 = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_accounts_raw_data, self.t_0),
            is_raw=True,
            return_empty_if_file_not_present=True
        )

        if gss_accunts_t_1 is not None:
            gss_accunts_t_1 = gss_accunts_t_1.withColumn("ts", F.from_utc_timestamp(
                F.to_timestamp("updated"), Constants.JKT_TIMEZONE)).filter(col("ts") <= self.ts_0).drop("ts") \
                .select("account_id", "global_stock_id", "balance", "partner_id", "updated") \
                .filter(col("partner_id").isin(self.partner_id)) \
                .drop("partner_id")

        gss_accounts = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_accounts, self.t_1)
        ).select("account_id", "global_stock_id", "balance", "partner_id", "updated") \
        .filter(col("partner_id").isin(self.partner_id)).drop("partner_id")

        gss_accounts = self.ops.get_union(gss_accunts_t_1, gss_accounts)

        gss_accounts = self.ops.de_dupe_dataframe(
            gss_accounts, ["account_id", "global_stock_id"], "updated"
        ).drop("updated")

        # global stock pocket accounts

        gss_pocket_accunts_t_1 = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_pocket_accounts_raw_data, self.t_0),
            is_raw=True,
            return_empty_if_file_not_present=True
        )

        if gss_pocket_accunts_t_1 is not None:
            gss_pocket_accunts_t_1 = gss_pocket_accunts_t_1.withColumn("ts", F.from_utc_timestamp(
                F.to_timestamp("updated"), Constants.JKT_TIMEZONE))\
                .filter(col("ts") <= self.ts_0).drop("ts") \
                .select("account_id", "global_stock_id", "user_pocket_id", "balance", "partner_id", "updated") \
                .filter(col("partner_id").isin(self.partner_id)) \
                .drop("partner_id")

        df_gss_pocket_accounts = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_pocket_accounts, self.t_1)
        ).select("account_id", "global_stock_id", "user_pocket_id", "balance", "partner_id", "updated") \
        .filter(col("partner_id").isin(self.partner_id)) \
        .drop("partner_id")

        df_gss_pocket_accounts = self.ops.get_union(gss_pocket_accunts_t_1, df_gss_pocket_accounts)

        df_gss_pocket_accounts = self.ops.de_dupe_dataframe(
            df_gss_pocket_accounts, ["account_id", "global_stock_id", "user_pocket_id"], "updated"
        ).drop("updated", "user_pocket_id")

        gss_accounts = self.ops.get_union(gss_accounts, df_gss_pocket_accounts)

        gss_accounts = gss_accounts.groupBy(["account_id", "global_stock_id"]).agg(F.sum("balance").alias("balance"))

        self.logger.info("Successfully calculated global stock accounts")

        return gss_accounts
    
    def get_global_stock_returns(self):
        self.logger.info("Starting calculation of global stock returns")
        gss_returns_t_1 = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_returns_raw_data, self.t_0),
            is_raw=True,
            return_empty_if_file_not_present=True
        )

        if gss_returns_t_1 is not None:
            gss_returns_t_1 = gss_returns_t_1.withColumn("ts", F.from_utc_timestamp(
                F.to_timestamp("updated"), "Asia/Jakarta")).filter(col("ts") <= self.ts_0).drop("ts") \
                .select("account_id", "global_stock_id", "total_quantity", "weighted_cost", "updated")

        gss_returns = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_returns, self.t_1)
        ).select("account_id", "global_stock_id", "total_quantity", "weighted_cost", "updated")

        gss_returns = self.ops.get_union(gss_returns, gss_returns_t_1)

        gss_returns = self.ops.de_dupe_dataframe(gss_returns, ["account_id", "global_stock_id"], "updated").drop("updated")

        gss_pocket_returns_t_1 = self.io_utils.read_json_data(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_pocket_returns_raw_data, self.t_0),
            is_raw=True,
            return_empty_if_file_not_present=True
        )

        if gss_pocket_returns_t_1 is not None:
            gss_pocket_returns_t_1 = gss_pocket_returns_t_1.withColumn("ts", F.from_utc_timestamp(
                F.to_timestamp("updated"), "Asia/Jakarta")).filter(col("ts") <= self.ts_0).drop("ts") \
                .select("account_id", "global_stock_id", "user_pocket_id", "total_quantity", "weighted_cost", "updated")

        gss_pocket_returns = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.global_stock_pocket_returns, self.t_1)
        ) \
            .select("account_id", "global_stock_id", "user_pocket_id", "total_quantity", "weighted_cost", "updated")

        gss_pocket_returns = self.ops.get_union(gss_pocket_returns, gss_pocket_returns_t_1)

        gss_pocket_returns = self.ops.de_dupe_dataframe(
            gss_pocket_returns, ["account_id", "global_stock_id", "user_pocket_id"], "updated"
        ).drop("updated", "user_pocket_id")

        gss_returns = self.ops.get_union(gss_returns, gss_pocket_returns)

        gss_returns = gss_returns.withColumn("weighted_cost", round(col("weighted_cost") * col("total_quantity"), 10))

        gss_returns = gss_returns.groupBy(["account_id", "global_stock_id"])\
            .agg(F.sum("weighted_cost").alias("weighted_cost"), F.sum("total_quantity").alias("total_quantity"))

        gss_returns = gss_returns.withColumn("weighted_cost", round(col("weighted_cost") / col("total_quantity"), 10))\
            .fillna(0)

        self.logger.info("Successfully calculated global stock returns")

        return gss_returns
    
    def get_global_stock_balance(self):
        self.logger.info("Starting calculation of global stock balance")

        df_gss_accounts = self.get_global_stock_accounts()

        df_gss_returns = self.get_global_stock_returns()

        df_gss_balance = df_gss_accounts.join(df_gss_returns, on=["account_id", "global_stock_id"], how="left") \
            .select(
                col("account_id").cast(LongType()), col("global_stock_id").alias("code"),
                round(col("balance"), 10).alias("quantity"), round(col("weighted_cost"), 6).alias("avg_buy_price")
            )\
            .filter(col("quantity") > 0)

        df_price = self.get_global_stock_prices()

        df_gss_balance = df_gss_balance.join(df_price, on=["code"], how="left") \
            .fillna({'closing_price': 0}) \
            .withColumn("market_value", round(col("quantity") * col("closing_price"), 2).alias("market_value")) \
            .withColumn("unrealized_pnl", round(col("quantity")*(col("closing_price") - col("avg_buy_price")), 2))
        self.logger.info("Successfully calculated global stock balance")
        return df_gss_balance
    
    def get_global_stock_options_balance(self):
        self.logger.info("Starting calculation of global stock options balance")
        df_gss_options_balance = self.get_global_stock_options_accounts()
    
        df_gss_options_balance = df_gss_options_balance.select(
                col("account_id").cast(LongType()), col("options_contract_id"),
                col("global_stock_id").alias("code"), round(col("total_quantity"), 10).alias("quantity"),
                round(col("weighted_cost"), 6).alias("avg_buy_price")
            ) \
            .filter(col("quantity") > 0)

        df_price = self.get_option_trade_price()

        df_gss_options_balance = df_gss_options_balance.join(df_price, on=["code", "options_contract_id"], how="left") \
            .fillna({'closing_price': 0}) \
            .withColumn("market_value", round(col("quantity") * col("closing_price"), 2).alias("market_value")) \
            .withColumn("unrealized_pnl", round(col("quantity")*(col("closing_price") - col("avg_buy_price")), 2))
        self.logger.info("Successfully calculated global stock balance")
        return df_gss_options_balance
    
    def start_processing(self):
        df_gss_options_contracts = self.get_global_stock_options_contracts()
    
        df_gss_codes = self.get_global_stock_codes()
    
        df_gss_balance = self.get_global_stock_balance()
        df_gss_balance = df_gss_balance.withColumn("type", lit("globalStock")).withColumn("contract_code", lit(""))
    
        df_gss_options_balance = self.get_global_stock_options_balance()
        df_gss_options_balance = df_gss_options_balance.join(
            df_gss_options_contracts, on=["options_contract_id"], how="left"
        )
        gss_options_balance_with_zero_shares = df_gss_options_balance\
            .filter((col("shares_per_contract").isNull()) | (col("shares_per_contract") == 0)).count()

        if gss_options_balance_with_zero_shares > 0:
            self.logger.warning("count of gss options contract accounts which has 0 shares per contract : {}"
                                .format(gss_options_balance_with_zero_shares))

        df_gss_options_balance = df_gss_options_balance\
            .withColumn("market_value", round(col("market_value") * col("shares_per_contract"), 2)) \
            .withColumn("unrealized_pnl", round(col("unrealized_pnl") * col("shares_per_contract"), 2)) \
            .withColumn("expiration_date", F.date_format(col("expiration_date"), "yyMMdd")) \
            .withColumn("strike_price_symbol", lit("$")) \
            .withColumn("strike_price", F.concat("strike_price_symbol", "strike_price")).drop("strike_price_symbol") \
            .withColumn("contract_code", F.concat(
                col("expiration_date"), lit(" "), col("strike_price"), lit(" "), col("contract_type")
                )
            ).drop("expiration_date", "strike_price", "contract_type", "options_contract_id") \
            .withColumn("type", lit("globalStockOptions"))

        df_gss_balance = self.ops.get_union(df_gss_balance, df_gss_options_balance)
    
        df_forex_balance = self.get_forex_balance()

        df_leverage_balance = self.get_leverage_wallet_balance(df_gss_balance, df_gss_codes)

        df_usd_balance = df_forex_balance.join(df_leverage_balance, on=["account_id"], how="full").fillna(0)

        df_usd_balance.coalesce(1).write.mode("overwrite").csv("hdfs:///usd_balance/", header=True)
    
        df_gss_balance = df_gss_balance.join(df_gss_codes, on=["code"], how="left").drop("stock_type") \
            .withColumn("code", col("pluang_company_code")).drop("pluang_company_code") \
            .withColumn("code", when((col("type") == "globalStock"), col("code"))
                        .otherwise(F.concat(col("code"), lit(" "), col("contract_code")))
            )\
            .drop("contract_code")
        df_gss_balance.coalesce(1).write.mode("overwrite").csv("hdfs:///global_stock_balance/", header=True)

    def run(self):
        self.start_processing()
