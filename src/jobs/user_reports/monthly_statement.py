from src.utils.spark_utils import *
from datetime import timedelta


class MonthlyStatement:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("monthly_statement")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.price_utils = PriceUtils(self.spark, self.config)
        self.asset_utils = AssetUtils(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.t_0 = self.t_1 + timedelta(days=1)
        self.cutoff_ts_0 = config["cutoff_ts"]
        self.cutoff_ts_1 = self.cutoff_ts_0 - timedelta(days=1)
        self.partner_id = self.config["pluang_partner_id"]

        self.logger.info("Monthly Statement initialised successfully with t_1: {}, t_2: {}, cutoff_ts_0: {}"
                         .format(self.t_1, self.t_2, self.cutoff_ts_0))

    @staticmethod
    def round_udf(value, precision):
        return F.round(value * pow(10, precision)) / F.pow(10, precision)

    def get_currency_to_idr(self):
        current_forex_price = self.price_utils.get_current_forex_price(
            self.partner_id, self.t_0,
        )
        currency_to_idr = current_forex_price.collect()[0]["mid_price"]
        return currency_to_idr

    def get_crypto_codes(self):
        df_crypto_code = self.asset_utils.get_crypto_assets()
        df_crypto_code = df_crypto_code.select(
            col("id").alias("code"), col("symbol").alias("asset_code"),
            col("price_precision").alias("price_precision"), col("active").alias("active"),
            col("safety_label").alias("safety_label"), col("enable_sell").alias("enable_sell"),
            col("enable_withdrawal").alias("enable_withdrawal"), col("updated").alias("updated")
        )
        return df_crypto_code

    def add_crypto_futures_symbol(self, df):
        crypto_future_instruments = self.asset_utils.get_crypto_future_instrument_assets()
        crypto_future_instruments = crypto_future_instruments.select(
            col("id").alias("crypto_future_instrument_id"), col("future_pair_symbol").alias("future_pair_symbol")
        )
        df = df.join(crypto_future_instruments, on=["crypto_future_instrument_id"], how="left")
        symbol_not_found_count = df.filter(col("future_pair_symbol").isNull()).count()
        if symbol_not_found_count > 0:
            self.logger.error("Futures pair symbol not found: {}".format(symbol_not_found_count))
            raise Exception("Futures pair symbol not found!")
        df = df.withColumn("code", col("future_pair_symbol")).drop("future_pair_symbol")
        return df

    def get_fund_codes(self):
        df_fund_code = self.asset_utils.get_fund_assets()
        df_fund_code = df_fund_code.select("id", "code") \
            .withColumnRenamed("code", "asset_code") \
            .withColumnRenamed("id", "code")
        return df_fund_code

    def check_if_already_published(self):
        flag = False
        try:
            published_events_s3_path = "{}/{}/dt={}/".format(
                self.bucket_path, S3Paths.monthly_statement_output, self.t_1
            )
            self.logger.info("reading published data from path {}".format(published_events_s3_path))
            df = self.io_utils.read_json_data(
                path=published_events_s3_path,
                return_empty_if_file_not_present=True,
                alert_file_not_present=False
            )
            cnt = df.count()
            self.logger.info("already published {} msgs".format(cnt))
            if cnt > 0:
                self.logger.info("msgs are already published")
                flag = True
        except Exception as e:
            self.logger.info("msgs are not published yet")
        return flag

    def get_asset_statement(self, df_balance, asset_key_str):
        self.logger.info("Starting the generation of monthly statement for {}".format(asset_key_str))
    
        cols_for_balance_summary = ["code", "quantity", "avg_buy_price", "market_value", "closing_price", "unrealized_pnl"]
        if asset_key_str == "usd":
            cols_for_balance_summary = ["code", "quantity", "avg_buy_price", "market_value",
                                        "closing_price", "unrealized_pnl", "type"]
        elif asset_key_str == "indo_stock":
            cols_for_balance_summary = ["code", "quantity", "avg_buy_price", "stock_value", "market_value",
                                        "closing_price", "unrealized_pnl"]
        df_balance = df_balance.filter((col("code") != "") & (col("code").isNotNull()))\
            .withColumn("balance_summary", F.struct(cols_for_balance_summary)) \
            .withColumn("total_equity", col("market_value")) \
            .drop("code", "quantity", "avg_buy_price", "market_value",
                  "closing_price", "unrealized_pnl", "type", "stock_value") \
            .groupBy(["account_id"]).agg(
                F.collect_list("balance_summary").alias("balance_summary"), F.sum("total_equity").alias("total_equity")
            )
    
        # round total equity based on asset type
        if asset_key_str == "usd":
            df_usd_balance = self.io_utils.read_csv_file(path="hdfs:///usd_balance/")
            df_balance = df_balance.join(df_usd_balance, on=["account_id"], how="full") \
                .fillna({'total_usd_margin': 0, 'pending_usd_margin': 0, 'total_usd_cash': 0, 'pending_usd_cash': 0})
            df_balance = df_balance.withColumn("total_equity", round(col("total_equity"), 2))
        elif asset_key_str == "indo_stock":
            df_wallet_balance = self.io_utils.read_csv_file(
                path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.indo_stock_wallet_v2, self.t_1)
            ).select("account_id", "balance", "blocked_balance")
            df_balance = df_balance.join(df_wallet_balance, on=["account_id"], how="full") \
                .fillna({'balance': 0, 'blocked_balance': 0})
            df_balance = df_balance.withColumn("total_equity", round(col("total_equity"), 2))
        else:
            df_balance = df_balance.withColumn("total_equity", col("total_equity").cast(LongType()))
        df_balance = df_balance.withColumn("balance_summary", when(col("balance_summary").isNull(), F.array())
                .otherwise(col("balance_summary"))
            ) \
            .fillna({'total_equity': 0})

        if asset_key_str == "usd":
            df_balance = df_balance.select("account_id", F.struct([
                "balance_summary", "total_equity", "total_usd_margin",
                "pending_usd_margin", "total_usd_cash", "pending_usd_cash"]).alias(asset_key_str))
        elif asset_key_str == "indo_stock":
            df_balance = df_balance.select("account_id", F.struct([
                "balance_summary", "total_equity", "balance", "blocked_balance"]).alias(asset_key_str))
        else:
            df_balance = df_balance.select("account_id", F.struct(["balance_summary", "total_equity"])
                                           .alias(asset_key_str))
        self.logger.info("successfully generated statement data")
        return df_balance

    def validate_and_publish_data(self, df_statement):
        self.logger.info("Started writing data into kafka topic and s3")
    
        # filter pluang only users
        df_accounts = self.io_utils.read_csv_file("{}/{}/dt={}/".format(self.bucket_path, S3Paths.accounts, self.t_0)) \
            .filter(col("partner_id").isin(self.partner_id)) \
            .withColumn("account_id", col("id")) \
            .select(col("account_id").cast(LongType()), col("user_id").cast(LongType()))
        df_statement = df_statement.withColumn("account_id", col("account_id").cast(LongType()))
        df_statement = df_statement.join(df_accounts, on=["account_id"], how="left").filter(col("user_id").isNotNull())
    
        # add execution date and time for the period
        df_statement = df_statement.withColumn("execution_date", lit(self.t_1))
        execution_time = DateUtils.get_utc_timestamp()
        df_statement = df_statement.withColumn("execution_time", lit(execution_time))
    
        # publish the data into kafka if not already published
        is_already_published = self.check_if_already_published()
        if not is_already_published:
            # write the data being published into s3
            df_statement = df_statement.withColumn("is_daily_report", lit(False))
            df_statement = df_statement.withColumn("is_monthly_report", lit(True))
            df_statement.coalesce(1).write.mode("overwrite").json(
                "{}/{}/dt={}/".format(self.bucket_path, S3Paths.monthly_statement_output, self.t_1)
            )
            self.logger.info("successfully written statement data into s3")
    
            # check the whitelisted and blacklisted users and filter the users
            self.logger.info("started writing statement data into kafka topic")
            df_whitelisted_users = self.io_utils.read_csv_file(
                "{}/{}/".format(self.bucket_path, S3Paths.monthly_statement_whitelisted_users)
            )
            df_whitelisted_users = df_whitelisted_users.filter(col("is_whitelisted_user") == True)
            if df_whitelisted_users.count() > 0:
                self.logger.info("whitelisted user list found")
                df_statement = df_statement.join(df_whitelisted_users, on=["user_id"], how="full")\
                    .filter((col("is_whitelisted_user") == True) & (col("account_id").isNotNull()))\
                    .drop("is_whitelisted_user")
            else:
                self.logger.info("whitelisted user list not found")
                df_blacklisted_users = self.io_utils.read_csv_file(
                    "{}/{}/".format(self.bucket_path, S3Paths.monthly_statement_blacklisted_users)
                )
                df_blacklisted_users = df_blacklisted_users.filter(col("is_blacklisted_user") == True)
                df_statement = df_statement.join(df_blacklisted_users, on=["user_id"], how="full")\
                    .filter(
                        ((col("is_blacklisted_user") == False) | col("is_blacklisted_user").isNull())
                        & (col("account_id").isNotNull())
                    )\
                    .drop("is_blacklisted_user")
    
            # write statement data into kafka
            df_statement = df_statement.withColumn("user_id", col("user_id").cast(LongType()))
            df_statement = df_statement.select(
                col("user_id").cast(StringType()).alias("key"),
                F.to_json(F.struct(df_statement.columns)).alias("value"),
                F.array(F.struct(lit("x-request-id").alias("key"),
                F.expr("uuid()").cast("binary").alias("value"))).alias("headers")
            )
            self.io_utils.write_data_in_kafka(df_statement, self.config["kafka_topics"]["monthly_statement_topic"])
            self.logger.info("successfully written statement data into kafka")
        else:
            self.logger.info("Statement data will not be written into kafka")

    def get_gold_balance(self):
        self.logger.info("Started Calculation of gold balance")
        df_gold_balance = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.gold_snapshot, self.t_1)
        )
        df_gold_balance = df_gold_balance.select(col("account_id").cast(LongType()), lit("GOLD").alias("code"),
                                                 round(col("total_quantity"), 6).alias("quantity"),
                                                 col("weighted_cost").cast(LongType()).alias("avg_buy_price"),
                                                 col("unit_price").cast(LongType()).alias("closing_price")) \
            .filter(col("quantity") > 0) \
            .withColumn("market_value",
                        (col("quantity") * col("closing_price")).cast(LongType()).alias("market_value")
            ) \
            .withColumn("unrealized_pnl",
                        (col("quantity") * (col("closing_price") - col("avg_buy_price"))).cast(LongType())
            )
        self.logger.info("Successfully calculated for gold balance")
        return df_gold_balance

    def get_crypto_currency_balance(self):
        self.logger.info("starting calculation of crypto balance")
        df_crypto_snapshot = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_snapshot, self.t_1)
        )
        df_crypto_snapshot = df_crypto_snapshot.select(
            col("account_id").cast(LongType()), col("crypto_currency_id").alias("code"),
            round(col("total_quantity"), 8).alias("quantity"), col("weighted_cost").alias("avg_buy_price"),
            col("unit_price").alias("closing_price")
        ).filter(col("quantity") > 0)
    
        df_crypto_pocket_snapshot = self.io_utils.read_csv_file(
            path="{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_pocket_snapshot, self.t_1)
        )
        df_crypto_pocket_snapshot = df_crypto_pocket_snapshot.select(
            col("account_id").cast(LongType()), col("crypto_currency_id").alias("code"),
            round(col("total_quantity"), 8).alias("quantity"), col("weighted_cost").alias("avg_buy_price"),
            col("unit_price").alias("closing_price")
        ).filter(col("quantity") > 0)
    
        df_crypto_balance = df_crypto_snapshot.union(df_crypto_pocket_snapshot)
        df_crypto_balance = df_crypto_balance.withColumn("avg_buy_price", col("quantity") * col("avg_buy_price"))
        df_crypto_balance = df_crypto_balance.groupBy(["account_id", "code"])\
            .agg(
                sum("quantity").alias("quantity"), sum("avg_buy_price").alias("avg_buy_price"),
                count("closing_price").alias("cnt"), sum("closing_price").alias("closing_price")
        )
    
        df_crypto_currency_codes = self.get_crypto_codes()
        df_crypto_balance = df_crypto_balance.join(df_crypto_currency_codes, on=["code"], how="left")
    
        df_crypto_balance = df_crypto_balance.select(
            "account_id", "code", "active", "safety_label", "enable_sell", "enable_withdrawal",
            round(col("quantity"), 8).alias("quantity"), (col("avg_buy_price") / col("quantity")).alias("avg_buy_price"),
            (col("closing_price") / col("cnt")).alias("closing_price"), col("price_precision"), col("asset_code")
        )
        df_crypto_balance = df_crypto_balance.withColumn("avg_buy_price",
                    self.round_udf(col("avg_buy_price"), col("price_precision"))
            ) \
            .withColumn("closing_price", self.round_udf(col("closing_price"), col("price_precision"))) \
            .withColumn("market_value", (col("quantity") * col("closing_price")).cast(LongType())
                        .alias("market_value")
            ) \
            .withColumn("unrealized_pnl", (col("quantity")*(col("closing_price") - col("avg_buy_price")))
                        .cast(LongType())
            )
    
        count_of_delisted_coin_balances = df_crypto_balance.filter(
            (col("active").isNull()) | (col("active") == False) |
            ((col("safety_label") == "DELISTED") & (col("enable_sell") == False) & (col("enable_withdrawal") == False))
        )\
        .count()
        if count_of_delisted_coin_balances > 0:
            self.logger.warning("DE_DATA_VALIDATION_WARNING: number of accounts having balance in delisted coin: {}"
                                .format(count_of_delisted_coin_balances)
                                )
            df_crypto_balance = df_crypto_balance.filter(
                ~((col("active").isNull()) | (col("active") == False) |
                  ((col("safety_label") == "DELISTED") & (col("enable_sell") == False)
                   & (col("enable_withdrawal") == False)))
            )
    
        df_crypto_balance = df_crypto_balance.withColumn("code", col("asset_code"))\
            .drop("asset_code", "price_precision", "active", "safety_label", "enable_sell", "enable_withdrawal")
        self.logger.info("Successfully calculated crypto balance")
        return df_crypto_balance

    def get_fund_balance(self):
        self.logger.info("starting calculation of fund balance")
        currency_to_idr = self.get_currency_to_idr()
        df_fund_snapshot = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.fund_snapshot, self.t_1)
        )
        df_fund_snapshot = df_fund_snapshot.withColumn("unit_price",
                    when(col("currency") == "USD", col("unit_price") * currency_to_idr).otherwise(col("unit_price"))
        )
        df_fund_balance = df_fund_snapshot.select(
            col("account_id").cast(LongType()), col("fund_id").alias("code"),
            round(col("total_quantity"), 4).alias("quantity"),
            col("weighted_cost_idr").cast(LongType()).alias("avg_buy_price"),
            col("unit_price").cast(LongType()).alias("closing_price")
        ) \
        .filter(col("quantity") > 0) \
        .withColumn("market_value", (col("quantity") * col("closing_price")).cast(LongType()).alias("market_value")) \
        .withColumn("unrealized_pnl", (col("quantity")*(col("closing_price") - col("avg_buy_price"))).cast(LongType()))
        df_fund_codes = self.get_fund_codes()
        df_fund_balance = df_fund_balance.join(df_fund_codes, on=["code"], how="left")
        df_fund_balance = df_fund_balance.withColumn("code", col("asset_code")).drop("asset_code")
        self.logger.info("Successfully calculated fund balance")
        return df_fund_balance

    def get_indo_stock_balance(self):
        self.logger.info("starting calculation of indo stock balance")
        indo_stock_snapshot = self.io_utils.read_csv_file(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.indo_stock_returns_v2_snapshots, self.t_1)
        )
        indo_stock_balance = indo_stock_snapshot.select(
            col("account_id").cast(LongType()), col("stock_code").alias("code"),
            round(col("total_quantity"), 4).alias("quantity"),
            col("weighted_cost").cast(LongType()).alias("avg_buy_price"),
            col("unit_price").cast(LongType()).alias("closing_price")
        ) \
        .filter(col("quantity") > 0) \
        .withColumn("stock_value", (col("quantity") * col("avg_buy_price")).cast(LongType()).alias("stock_value")) \
        .withColumn("market_value", (col("quantity") * col("closing_price")).cast(LongType()).alias("market_value")) \
        .withColumn("unrealized_pnl", (col("quantity")*(col("closing_price") - col("avg_buy_price"))).cast(LongType()))

        self.logger.info("Successfully calculated indo stock balance")
        return indo_stock_balance

    def get_usd_balance(self):
        self.logger.info("starting calculation of usd balance")
        df_gss_balance = self.io_utils.read_csv_file(path="hdfs:///global_stock_balance/")
        self.logger.info("Successfully calculated usd balance")
        return df_gss_balance

    def get_crypto_future_statement(self, position, wallet_balance, crypto_currency_wallet_transfers):
        self.logger.info("Starting the generation of monthly statement for crypto future")

        cols_for_balance_summary = ["side", "quantity", "entry_price", "mark_price", "unrealized_pnl", "code"]

        position_with_quantity = position.filter((round(col("quantity").cast(DoubleType()), 12) != 0)
                                                 & (col("entry_price").isNotNull()) & (col("mark_price").isNotNull())
                                                 & (col("entry_price") != "null") & (col("mark_price") != "null"))
        realized_pnl = position.groupBy(["account_id"]).agg(F.sum("realized_pnl").alias("realized_pnl"))

        position_with_quantity = position_with_quantity.filter((col("code") != "") & (col("code").isNotNull())) \
            .withColumn("balance_summary", F.struct(cols_for_balance_summary)) \
            .groupBy(["account_id"]).agg(
                F.collect_list("balance_summary").alias("balance_summary"),
                F.sum("unrealized_pnl").alias("unrealized_pnl")
            )
            
        df_balance = position_with_quantity.join(wallet_balance, on=["account_id"], how="full") \
            .join(realized_pnl, on=["account_id"], how="full") \
            .join(crypto_currency_wallet_transfers, on=["account_id"], how="full") \
            .fillna({'unrealized_pnl': 0, 'realized_pnl': 0, 'deposit': 0, 'withdrawal': 0, 'order_margin': 0,
                     'margin_balance': 0, 'available_to_move': 0, 'maintenance_margin': 0, 'wallet_balance': 0})
        
        df_balance = df_balance.withColumn("unrealized_pnl", round(col("unrealized_pnl"), 2)) \
            .withColumn("realized_pnl", round(col("realized_pnl"), 2)) \
            .withColumn("deposit", round(col("deposit"), 2)) \
            .withColumn("withdrawal", round(col("withdrawal"), 2)) \
            .withColumn("order_margin", round(col("order_margin"), 2)) \
            .withColumn("margin_balance", round(col("margin_balance"), 2)) \
            .withColumn("available_to_move", round(col("available_to_move"), 2)) \
            .withColumn("maintenance_margin", round(col("maintenance_margin"), 2)) \
            .withColumn("wallet_balance", round(col("wallet_balance"), 2))

        df_balance = df_balance.withColumn("margin_balance", round(col("wallet_balance") + col("unrealized_pnl"), 2))
        df_balance = df_balance.withColumn("unrealized_pnl_temp", when(col("unrealized_pnl") <= 0, col("unrealized_pnl")).otherwise(0))
        df_balance = df_balance.withColumn("available_to_move", round(col("wallet_balance") + col("unrealized_pnl_temp") - col("maintenance_margin") - col("order_margin"), 2))
        df_balance = df_balance.drop("unrealized_pnl_temp")

        df_balance = df_balance.withColumn("balance_summary", when(col("balance_summary").isNull(),
                                                                   F.array()).otherwise(col("balance_summary")))
        df_balance = df_balance.select("account_id", F.struct(
            ["balance_summary", "unrealized_pnl", "realized_pnl", "deposit", "withdrawal", "order_margin",
             "margin_balance", "available_to_move", "maintenance_margin", "wallet_balance"]).alias("crypto_futures"))
        # df_balance = df_balance.filter(
        #     (F.size(F.col("balance_summary")) > 0) | (col("unrealized_pnl") != 0)
        #     | (col("order_margin") != 0) | (col("margin_balance") != 0)
        #     | (col("available_to_move") != 0) | (col("maintenance_margin") != 0) | (col("wallet_balance") != 0))
        df_balance = df_balance.filter((F.size(F.col("balance_summary")) > 0))
        self.logger.info("successfully generated statement data for crypto futures")
        return df_balance

    def get_crypto_futures_positions(self):
        crypto_futures_positions = self.io_utils.read_parquet_data(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_futures_positions_t_2_files, self.t_1))
        exchange_response_schema = F.schema_of_json("""
                {  
                  "entryPrice": "double",
                  "markPrice": "double",
                  "unrealisedPnl": "double"
                }
        """)
        crypto_futures_positions = crypto_futures_positions.withColumn(
            "exchange_response_parsed",
            F.from_json(col("exchange_response"), exchange_response_schema)
        )
        crypto_futures_positions = crypto_futures_positions.select(
            "account_id", col("crypto_future_instrument_id"),
            when((col("total_quantity") > 0), "Long").otherwise("Short").alias("side"),
            col("total_quantity").alias("quantity"), col("realised_pnl").alias("realized_pnl"),
            col("exchange_response_parsed.entryPrice").alias("entry_price"),
            col("exchange_response_parsed.markPrice").alias("mark_price"),
            col("exchange_response_parsed.unrealisedPnl").alias("unrealisedPnl"))

        crypto_futures_positions = crypto_futures_positions.withColumn("unrealized_pnl",
                        round(col("quantity")*(col("mark_price") - col("entry_price")), 2)) \
            .withColumn("quantity", F.abs(col("quantity")))
        crypto_futures_positions = self.add_crypto_futures_symbol(crypto_futures_positions)
        return crypto_futures_positions

    def get_crypto_currency_wallet_transfers(self):
        crypto_currency_wallet_transfers = self.io_utils.read_parquet_data(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_currency_wallet_transfers, self.t_1))
        crypto_currency_wallet_transfers = crypto_currency_wallet_transfers.filter(
            (col("status") == Constants.SUCCESS) & (col("network") == Constants.MARGIN_INTERNAL)
            & (col("crypto_currency_id") == 10005))
        crypto_currency_wallet_transfers = crypto_currency_wallet_transfers.withColumn(
            "deposit", when(col("transaction_type") == "WITHDRAWAL", col("total_quantity")).otherwise(0))
        crypto_currency_wallet_transfers = crypto_currency_wallet_transfers.withColumn(
            "withdrawal", when(col("transaction_type") == "DEPOSIT", col("total_quantity")).otherwise(0))
        crypto_currency_wallet_transfers = crypto_currency_wallet_transfers.groupBy(["account_id"])\
            .agg(F.sum("deposit").alias("deposit"), F.sum("withdrawal").alias("withdrawal"))
        crypto_currency_wallet_transfers = crypto_currency_wallet_transfers.withColumn("deposit",
                                                                                       round(col("deposit"), 2))
        crypto_currency_wallet_transfers = crypto_currency_wallet_transfers.withColumn("withdrawal",
                                                                                       round(col("withdrawal"), 2))
        return crypto_currency_wallet_transfers

    def get_crypto_margin_wallet(self):
        crypto_margin_wallet = self.io_utils.read_json_data(
            "{}/{}/dt={}/".format(self.bucket_path, S3Paths.crypto_margin_wallets, self.t_1)
        )
        crypto_margin_wallet = crypto_margin_wallet.filter(col("exchange_settlement_asset_id") == 10000)
        exchange_response_schema = F.schema_of_json("""
            {
              "asset": "string",
              "account": "string",
              "marginRatio": "double",
              "orderMargin": "double",
              "marginBalance": "double",
              "unrealizedPnl": "double",
              "walletBalance": "double",
              "availableMargin": "double",
              "effectiveLeverage": "double",
              "maintenanceMargin": "double",
              "totalPositionValue": "double",
              "withdrawableMargin": "double"
            }
        """)
        crypto_margin_wallet = crypto_margin_wallet.withColumn(
            "exchange_response_parsed",
            F.from_json(col("exchange_response"), exchange_response_schema)
        )
        crypto_margin_wallet = crypto_margin_wallet.select(
            col("account_id"),
            col("exchange_response_parsed.orderMargin").alias("order_margin"),
            col("exchange_response_parsed.marginBalance").alias("margin_balance"),
            col("exchange_response_parsed.maintenanceMargin").alias("maintenance_margin"),
            col("exchange_response_parsed.withdrawableMargin").alias("available_to_move"),
            col("exchange_response_parsed.walletBalance").alias("wallet_balance")
        )
        crypto_margin_wallet = crypto_margin_wallet.fillna({'order_margin': 0, 'margin_balance': 0,
                                        'maintenance_margin': 0, 'available_to_move': 0, 'wallet_balance': 0})
        return crypto_margin_wallet
    
    def start_processing(self):
        df_gold_balance = self.get_gold_balance()
        df_crypto_balance = self.get_crypto_currency_balance()
        df_fund_balance = self.get_fund_balance()
        df_usd_balance = self.get_usd_balance()
        df_indo_stock_balance = self.get_indo_stock_balance()
        df_crypto_future_positions = self.get_crypto_futures_positions()
        df_crypto_future_margin_wallet_balances = self.get_crypto_margin_wallet()
        df_crypto_currency_wallet_transfers = self.get_crypto_currency_wallet_transfers()
    
        df_gold_balance = self.ops.cast_to_bigdecimal(
            df_gold_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True
        )
        df_crypto_balance = self.ops.cast_to_bigdecimal(
            df_crypto_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True
        )
        df_fund_balance = self.ops.cast_to_bigdecimal(
            df_fund_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True
        )
        df_indo_stock_balance = self.ops.cast_to_bigdecimal(
            df_indo_stock_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True
        )
        df_usd_balance = self.ops.cast_to_bigdecimal(
            df_usd_balance, ["quantity", "avg_buy_price", "closing_price"], remove_trailing_zero=True
        )
        df_crypto_future_positions = self.ops.cast_to_bigdecimal(
            df_crypto_future_positions, ["quantity", "entry_price", "mark_price"], remove_trailing_zero=True
        )

        # Generate Statements
        df_usd_statement = self.get_asset_statement(df_usd_balance, "usd")
        df_gold_statement = self.get_asset_statement(df_gold_balance, "gold")
        df_crypto_currency_statement = self.get_asset_statement(df_crypto_balance, "crypto_currency")
        df_fund_statement = self.get_asset_statement(df_fund_balance, "fund")
        df_indo_stock_statement = self.get_asset_statement(df_indo_stock_balance, "indo_stock")
        df_crypto_future_statement = self.get_crypto_future_statement(
            df_crypto_future_positions, df_crypto_future_margin_wallet_balances, df_crypto_currency_wallet_transfers
        )
    
        df_statement = df_usd_statement.join(df_gold_statement, on=["account_id"], how="full") \
            .join(df_crypto_currency_statement, on=["account_id"], how="full") \
            .join(df_fund_statement, on=["account_id"], how="full") \
            .join(df_indo_stock_statement, on=["account_id"], how="full") \
            .join(df_crypto_future_statement, on=["account_id"], how="full")
        self.validate_and_publish_data(df_statement)

    def run(self):
        if self.t_1.month != self.t_0.month:
            self.start_processing()
        else:
            self.logger.info("{} is not last day of the month".format(self.t_1))
