from src.utils.spark_utils import *
from pyspark.sql import functions as F
from pyspark.sql.functions import col, lit


class IndoStockTopDividend:
    """
    This class processes Indo stock data to find the top dividend-paying stocks.
    It reads corporate action data, filters and maps Indo stocks, computes the top 20 stocks by total dividend,
    prepares the final payload, and publishes the results to a Kafka topic.
    """
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.logger.info("Initializing IndoStockTopDividend class")

        # Extract and log config
        self.config = config
        self.t_1 = config.get("t_1")
        self.bucket = config.get("bucket")
        self.kafka_topic = self.config.get("kafka_topics", {}).get("pre_define_watch_list_topic")
        self.job_config = config.get("pre_define_watch_list", {}).get("indo_stocks_v2", {})
        self.logger.debug(f"Config Loaded: offset={self.t_1}, bucket={self.bucket}, kafka_topic={self.kafka_topic}")

        # Initialize utilities
        self.spark_utils = SparkUtils("IndoStockTopDividend")
        self.spark = self.spark_utils.create_spark_session()
        self.date_utils = DateUtils()
        self.io_utils = IOUtils(self.spark, self.config)
        self.ops = Operations(self.spark)
        self.user_props = UserProperties(self.spark, self.config)
        self.s3_path = S3Paths()
        self.job_config = self.config.get("pre_define_watch_list").get("indo_stocks_v2")
        self.limit_for_top_dividend_stocks = self.job_config.get("limit_for_top_dividend_stocks")

        self.logger.info("Initialization complete")

    def run(self):
        """ Main method to execute the job logic."""
        exec_date = self.t_1
        self.logger.info(f"Execution date set to: {exec_date}")

        corporate_action_df = self._read_corporate_action_data(exec_date)
        stock_map_df = self._filter_and_map_indo_stocks()

        self.logger.info("Joining corporate action data with stock map")
        joined_df = corporate_action_df.join(
            stock_map_df,
            corporate_action_df.stockCode == stock_map_df.indo_stock_code,
            "left"
        ).filter(col("indo_stock_id").isNotNull()) \
         .drop("indo_stock_code")

        self.logger.info(f"Records after join and filtering nulls: {joined_df.count()}")
        top_dividend_df = self._get_top_20_by_total_dividend(joined_df)

        if top_dividend_df.isEmpty():
            self.logger.warning("No dividend data found")
            return
        
        record_count = top_dividend_df.count()
        if record_count < self.limit_for_top_dividend_stocks:
            self.logger.warning(f"Only {record_count} records found in Top Dividends, which is less than {self.limit_for_top_dividend_stocks}.")

        final_payload_df = self._prepare_final_payload(top_dividend_df, exec_date)

        output_path = f"s3a://{self.bucket}/{S3Paths.indo_stock_price_v2_watchlist_max_dividend}/dt={exec_date}/"
        self.io_utils.write_json_file(final_payload_df, output_path)
        # self._publish_to_kafka(final_payload_df, self.kafka_topic)

    def _read_corporate_action_data(self, date: str) -> DataFrame:
        """ Reads corporate action data from S3 for the given date."""
        try:
            s3_path = f"s3a://{self.bucket}/{self.s3_path.indo_stock_corporate_action_v2}/dt={date}/"
            self.logger.info(f"Reading corporate action data from: {s3_path}")
            df = self.io_utils.read_csv_file(s3_path).select("stockCode", "totalDividend")
            self.logger.info(f"Read {df.count()} records from corporate action data")
            return df
        except Exception:
            self.logger.error("Failed to read corporate action data", exc_info=True)
            raise

    def _filter_and_map_indo_stocks(self) -> DataFrame:
        """ Fetches and filters Indo stock data from Kafka."""
        try:
            topic = self.config["kafka_topics"]["indo_stocks_v2_topic"]
            servers = self.config["bootstrap_servers"]
            self.logger.info(f"Reading Indo stocks from Kafka topic: {topic}")

            df = self.io_utils.read_from_kafka_in_memory(bootstrap_servers=servers, topics=topic).select(
                col("value.id").alias("indo_stock_id"),
                col("value.code").alias("indo_stock_code"),
                col("value.status"),
                col("value.updated")
            ).distinct()

            self.logger.debug(f"Initial stock count from Kafka: {df.count()}")
            df = self.ops.de_dupe_dataframe(df, ["indo_stock_id"], "updated") \
                         .filter(col("status").isin("ACTIVE", "active")) \
                         .select("indo_stock_id", "indo_stock_code")

            self.logger.info(f"Filtered stock list count: {df.count()}")
            return df
        except Exception:
            self.logger.error("Failed to fetch or process Indo stock map", exc_info=True)
            raise

    def _get_top_20_by_total_dividend(self, df: DataFrame) -> DataFrame:
        """ Computes the top 20 stocks by total dividend."""
        try:
            self.logger.info("Computing top 20 stocks by total dividend")
            result = df.groupBy("indo_stock_id", "stockCode") \
                .agg(F.max("totalDividend").alias("total_dividend")) \
                .orderBy(F.desc("total_dividend")) \
                .limit(self.limit_for_top_dividend_stocks)
            self.logger.debug(f"Top 20 stocks result count: {result.count()}")
            return result
        except Exception:
            self.logger.error("Failed to compute top dividend stocks", exc_info=True)
            raise

    def _prepare_final_payload(self, df: DataFrame, exec_date: str) -> DataFrame:
        """ Prepares the final payload for Kafka publishing."""
        try:
            self.logger.info("Preparing final payload")
            df = df.withColumnRenamed("indo_stock_id", "id") \
                   .withColumn("indo_stocks", F.struct("id", "stockCode", "total_dividend")) \
                   .withColumn("asset_type", lit("indo_stock")) \
                   .groupBy("asset_type") \
                   .agg(F.collect_list("indo_stocks").alias("indo_stocks")) \
                   .withColumn("content", F.struct("indo_stocks")) \
                   .withColumn("category", lit("max_total_dividend")) \
                   .withColumn("execution_date", lit(exec_date)) \
                   .drop("asset_type", "indo_stocks")
            return df
        except Exception:
            self.logger.error("Failed to prepare final payload", exc_info=True)
            raise

    def _publish_to_kafka(self, df: DataFrame, topic: str):
        """ Publishes the DataFrame to a Kafka topic."""
        self.logger.info(f"Starting _publish_to_kafka for topic {topic}")
        try:
            self.logger.info(f"Publishing DataFrame to Kafka topic: {topic}")
            df = df.withColumn("x_request_id", F.expr("uuid()"))
            df_kafka = df.select(
                col("category").cast(StringType()).alias("key"),
                F.to_json(F.struct(*df.columns)).alias("value"),
                F.array(F.struct(lit("x-request-id").alias("key"),
                                 col("x_request_id").cast("binary").alias("value"))).alias("headers")
            )
            self.logger.debug(f"Kafka DataFrame count: {df_kafka.count()}")
            self.io_utils.write_data_in_kafka(df=df_kafka, topic=topic)
            self.logger.info(f"Published to Kafka topic: {topic}")
        except Exception as e:
            self.logger.error(f"Error publishing to Kafka topic: {topic}", exc_info=True)
            raise
        finally:
            self.logger.info("Completed _publish_to_kafka for topic {topic}")
            self.spark_utils.stop_spark(self.spark)
            self.logger.info("Spark session stopped")
        self.logger.info("Completed IndoStockTopDividend job")

