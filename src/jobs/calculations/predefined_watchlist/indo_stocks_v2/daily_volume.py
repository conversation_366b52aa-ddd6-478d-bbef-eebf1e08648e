from src.utils.spark_utils import *
from pyspark.sql import functions as F
from pyspark.sql.functions import col, lit
from pyspark.sql.types import StringType


class IndoStockDailyVolume:
    """
    Class to calculate and publish daily volume shockers for Indo stocks.
    """

    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.logger.info("Initializing IndoStockDailyVolume class")

        self.config = config
        self.t_1 = config.get("t_1")
        self.t_2 = config.get("t_2")
        self.bucket = config.get("bucket")
        self.kafka_topic = self.config.get("kafka_topics", {}).get("pre_define_watch_list_topic")

        self.job_config = config.get("pre_define_watch_list", {}).get("indo_stocks_v2", {})
        self.volume_ratio = self.job_config.get("volume_ratio", 2)

        self.spark_utils = SparkUtils("IndoStockDailyVolume")
        self.spark = self.spark_utils.create_spark_session()

        self.date_utils = DateUtils()
        self.io_utils = IOUtils(self.spark, config)
        self.ops = Operations(self.spark)
        self.user_props = UserProperties(self.spark, config)
        self.s3_path = S3Paths()

        self.logger.info("IndoStockDailyVolume class initialized")

    def _read_ohlc_data(self, date: str) -> DataFrame:
        """Reads OHLC data from S3 for a given date."""
        try:
            s3_path = f"s3a://{self.bucket}/{self.s3_path.indo_stock_price_v2}/dt={date}/"
            self.logger.info(f"Reading OHLC data from: {s3_path}")
            df = self.io_utils.read_csv_file(s3_path)
            # Deduplicate on candle_end_time (date) and stock_code
            self.logger.info("Applying deduplication on candle_end_time and stock_code")
            df = self.ops.de_dupe_dataframe(df, ["stock_code"], "candle_end_time")
            self.logger.info(f"After deduplication: {df.count()} records remaining")
            return df
        except Exception:
            self.logger.error(f"Failed to read OHLC data for date: {date}", exc_info=True)
            raise

    def _filter_and_map_indo_stocks(self) -> DataFrame:
        """Fetches active Indo stocks from Kafka and maps required fields."""
        self.logger.info("Fetching active Indo stock metadata from Kafka")

        try:
            topic = self.config.get("kafka_topics", {}).get("indo_stocks_v2_topic")
            servers = self.config.get("bootstrap_servers")

            df = (
                self.io_utils.read_from_kafka_in_memory(bootstrap_servers=servers, topics=topic)
                .select(
                    col("value.id").alias("indo_stock_id"),
                    col("value.code").alias("indo_stock_code"),
                    col("value.status").alias("status"),
                    col("value.updated").alias("updated"),
                    col("value.stock_type").alias("stock_type")
                )
                .distinct()
            )

            self.logger.debug(f"Fetched {df.count()} raw Indo stock records")

            df = self.ops.de_dupe_dataframe(df, ["indo_stock_id"], "updated")
            df = df.filter(col("status").isin("ACTIVE", "active"))
            mapped_df = df.select("indo_stock_id", "indo_stock_code")

            self.logger.info(f"Filtered {mapped_df.count()} ACTIVE Indo stock records")
            return mapped_df

        except Exception:
            self.logger.error("Error while fetching or filtering Indo stocks from Kafka", exc_info=True)
            raise

    def _calculate_daily_volume(self, current_df: DataFrame, previous_df: DataFrame) -> DataFrame:
        """Calculates volume shockers by comparing current vs previous day."""
        try:
            self.logger.info("Calculating volume ratio between current and previous day")

            df = current_df.join(previous_df, ["indo_stock_id"], "left").fillna(0.0)
            df = df.withColumn("ratio", col("current_day_volume") / col("previous_day_volume")).select("indo_stock_id", "current_day_volume", "previous_day_volume","ratio")
            shockers_df = df.filter(col("ratio") > self.volume_ratio)
            self.logger.info(f"{shockers_df.count()} records passed the volume shocker threshold > {self.volume_ratio}")

            return shockers_df

        except Exception:
            self.logger.error("Failed to calculate daily volume shockers", exc_info=True)
            raise

    def _prepare_final_payload(self, df: DataFrame, execution_date: str) -> DataFrame:
        """Prepares final output format for S3 and Kafka."""
        try:
            self.logger.info("Preparing final payload for publishing")

            df = (
                df.withColumnRenamed("current_day_volume", "volume")
                .select("indo_stock_id", "volume", "ratio")
                .withColumnRenamed("indo_stock_id", "id")
                .withColumn("indo_stocks", F.struct("id", "volume", "ratio"))
                .withColumn("asset_type", lit("indo_stock"))
                .groupBy("asset_type")
                .agg(F.collect_list("indo_stocks").alias("indo_stocks"))
                .withColumn("content", F.struct("indo_stocks"))
                .withColumn("category", lit("volume_shockers"))
                .withColumn("execution_date", lit(execution_date))
                .drop("asset_type", "indo_stocks")
            )

            return df
        except Exception:
            self.logger.error("Failed to prepare final payload", exc_info=True)
            raise

    def _publish_to_kafka(self, df: DataFrame, topic: str):
        """Publishes the payload to Kafka."""
        try:
            self.logger.info(f"Publishing to Kafka topic: {topic}")
            df = df.withColumn("x_request_id", F.expr("uuid()"))

            kafka_df = df.select(
                col("category").cast(StringType()).alias("key"),
                F.to_json(F.struct(*df.columns)).alias("value"),
                F.array(F.struct(lit("x-request-id").alias("key"),
                                 col("x_request_id").cast("binary").alias("value"))).alias("headers")
            )

            self.io_utils.write_data_in_kafka(df=kafka_df, topic=topic)

        except Exception:
            self.logger.error("Error while publishing to Kafka", exc_info=True)
            raise

    def run(self):
        """Main run loop for IndoStockDailyVolume job."""
        self.logger.info("Starting IndoStockDailyVolume run()")
        try:

            # Load OHLC data
            current_df = self._read_ohlc_data(self.t_1).select(
                col("stock_code"),
                col("volume").cast("long").alias("current_day_volume")
            )
            previous_df = self._read_ohlc_data(self.t_2).select(
                col("stock_code"),
                col("volume").cast("long").alias("previous_day_volume")
            )

            # Indo stock metadata
            stock_map_df = self._filter_and_map_indo_stocks()

            # Join stock metadata
            current_df = current_df.join(stock_map_df, current_df["stock_code"] == stock_map_df["indo_stock_code"], "left") \
                                   .drop("indo_stock_code")

            previous_df = previous_df.join(stock_map_df, previous_df["stock_code"] == stock_map_df["indo_stock_code"], "left") \
                                     .drop("indo_stock_code")

            # Volume shocker calculation
            volume_df = self._calculate_daily_volume(current_df, previous_df)

            if volume_df.rdd.isEmpty():
                self.logger.warning("No volume shockers found for today")
                return
            
            if volume_df.count() < 20:
                self.logger.info(f"Volume shockers found are less than 20: {volume_df.count()}")

            # Prepare and write payload
            payload_df = self._prepare_final_payload(volume_df, self.t_1)
            output_path = f"s3a://{self.bucket}/{self.s3_path.indo_stock_price_v2_watchlist_daily_volume}/dt={self.t_1}/"
            self.logger.info(f"Writing payload to: {output_path}")
            payload_df.coalesce(1).write.mode("overwrite").json(output_path)

            # Publish to Kafka
            # self._publish_to_kafka(payload_df, self.kafka_topic)
            self.logger.info("IndoStockDailyVolume job completed successfully")

        except Exception:
            self.logger.error("Unexpected error occurred in run()", exc_info=True)
            raise
        finally:
            self.spark_utils.stop_spark(self.spark)
            self.logger.info("Spark session stopped")
