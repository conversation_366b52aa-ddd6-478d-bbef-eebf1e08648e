from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.enums.crypto_staking import StakingStatus

class StakingRewardsAccrual:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("staking_rewards_accrual")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")

        # Handling time based variables
        self.offset = self.config["offset"]
        self.execution_time = self.config["execution_time"]
        self.t_1 = self.config["t_1"]
        self.t_2 = self.config["t_2"]
        self.current_date = DateUtils.get_jkt_date(self.offset)

    def get_staking_snapshot_data(self):

        def get_staking_accounts_data():
            staking_accounts_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.staking_accounts_snapshot, self.t_1)
            self.logger.info("reading Staking Accounts Data from path {}".format(staking_accounts_path))
            staking_accounts_df = self.io_utils.read_parquet_data(staking_accounts_path)
            staking_accounts_df = staking_accounts_df.select("id", "account_id", "user_id", "client_id", "partner_id", "crypto_currency_id",
                                                                "staked_balance", "unstaking_requested_balance")
            return staking_accounts_df

        def get_staking_assets_data():
            staking_assets_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.staking_assets_snapshot, self.t_1)
            self.logger.info("reading Staking Assets Data from path {}".format(staking_assets_path))
            staking_assets_df = self.io_utils.read_parquet_data(staking_assets_path)
            staking_assets_df = staking_assets_df.withColumn("reward_frequency", F.get_json_object(col("reward_disbursal_frequency"), "$.interval")) \
                            .withColumn("reward_cron_schedule", F.get_json_object(col("reward_disbursal_frequency"), "$.cron"))
            staking_assets_df = staking_assets_df.select("crypto_currency_id", "reward_frequency", "reward_cron_schedule")
            return staking_assets_df

        staking_accounts_df = get_staking_accounts_data()
        staking_assets_df = get_staking_assets_data()

        return staking_accounts_df, staking_assets_df

    def get_staked_balance_for_today(self, staking_accounts_df):
        staking_accounts_df = staking_accounts_df.withColumn(
            "eligible_staked_quantity",
            round(col("staked_balance") + col("unstaking_requested_balance"), 8)
        )
        # Filter for accounts with positive eligible staked quantity
        staking_accounts_df = staking_accounts_df.filter(col("eligible_staked_quantity") > 0)
        current_date_formatted = self.current_date.strftime("%Y%m%d")

        staking_accounts_df = staking_accounts_df.withColumn(
            "ref_id",
            concat(
                col("account_id"),
                lit("_"),
                col("crypto_currency_id"),
                lit("_"),
                lit(current_date_formatted)
            )
        )
        return staking_accounts_df


    def create_kafka_events(self, staked_balance_for_today):

        def transform_nested_rewards(staked_balance_df):
            grouped_df = staked_balance_df.groupBy("account_id", "user_id", "client_id", "partner_id", "reward_trigger_time"
            ).agg(F.collect_list(
                    F.struct(
                        col("crypto_currency_id"),
                        col("ref_id"),
                        col("eligible_staked_quantity"),
                        col("reward_frequency")
                    )
                ).alias("rewards_list"))

            # Convert list to map structure
            result_df = grouped_df.select("account_id", "user_id", "client_id", "partner_id", "reward_trigger_time",
                                            F.map_from_arrays(
                                                F.transform(col("rewards_list"), lambda x: x.crypto_currency_id),
                                                F.transform(col("rewards_list"), lambda x: F.struct(
                                                    x.ref_id.alias("ref_id"),
                                                    x.eligible_staked_quantity.alias("eligible_staked_quantity"),
                                                    x.reward_frequency.alias("reward_frequency")
                                                ))
                                            ).alias("rewards"))
            return result_df

        publish_time = DateUtils.get_utc_timestamp()
        staked_balance_for_today = staked_balance_for_today.withColumn("reward_trigger_time", lit(publish_time))
        # kafka_event = self.ops.convert_columns_snake_to_camel_case(staked_balance_for_today)
        kafka_event = staked_balance_for_today
        kafka_event = transform_nested_rewards(kafka_event)
        cols_to_publish = kafka_event.columns
        kafka_event = kafka_event.withColumn("data", F.struct(cols_to_publish))

        kafka_event = kafka_event.withColumn("type", lit("admin")).withColumn("id", lit(self.config["crypto_staking_topic_actor_prop"]))
        kafka_event = kafka_event.withColumn("actor", F.struct(["type", "id"])).drop("id", "type")
        kafka_event = kafka_event.withColumn("x-request-id", F.expr("uuid()"))
        kafka_columns = ["actor", "data"]
        kafka_event = kafka_event.select(col("account_id").cast(StringType()).alias("key"),
                                         F.to_json(F.struct(kafka_columns)).alias("value"),
                                         F.array(F.struct(lit("x-request-id").alias("key"), col("x-request-id").cast("binary").alias("value"))).alias("headers")).drop("x-request-id")
        return kafka_event

    def write_reward_accrual_history(self, staked_balance_for_today):
        self.io_utils.write_parquet_file(staked_balance_for_today, "{}/{}/dt={}/".format(self.bucket_path, S3Paths.staking_rewards_accrual_history, self.t_1),3)

    def start_processing(self):
        staking_accounts_data, staking_assets_data = self.get_staking_snapshot_data()
        staking_accounts_data = staking_accounts_data.join(staking_assets_data, on="crypto_currency_id", how="inner")
        staked_balance_for_today = self.get_staked_balance_for_today(staking_accounts_data)
        # staked_balance_for_today.show(truncate=False)
        staked_balance_for_today = staked_balance_for_today.select("id", "account_id", "user_id", "client_id",
                                                                   "partner_id", "crypto_currency_id",
                                                                   "eligible_staked_quantity",
                                                                   "ref_id", "reward_frequency", "reward_cron_schedule")
        kafka_events = self.create_kafka_events(staked_balance_for_today)
        # kafka_events.show(truncate=False)
        count_kafka_events = kafka_events.count()
        self.logger.info(f"Total accounts with staked balance: {count_kafka_events}")
        self.io_utils.write_data_in_kafka(kafka_events, self.config["kafka_topics"]["staking_user_accrued_rewards_topic"])
        self.write_reward_accrual_history(staked_balance_for_today)

    def run(self):
        self.start_processing()
        self.spark_utils.stop_spark(self.spark)
