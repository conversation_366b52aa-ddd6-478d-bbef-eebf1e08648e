from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.utils.asset_utils import AssetUtils
from src.enums.crypto_staking import StakingStatus
from src.schema.crypto_currency_schema import crypto_currency_rewards_disbursal_history_schema
from croniter import croniter
import requests
import json
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type, before_sleep


class StakingRewardsDisbursal:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("staking_rewards_accrual")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.asset_utils = AssetUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")

        # Handling time based variables
        self.offset = self.config["offset"]
        self.execution_time = self.config["execution_time"]
        self.t_1 = self.config["t_1"]
        self.t_2 = self.config["t_2"]
        self.logger.info("Staking Rewards Disbursal initialised successfully with t_1: {}, t_2: {}".format(self.t_1, self.t_2))
        self.current_date = DateUtils.get_jkt_date(self.offset)

    def get_crypto_codes(self):
        self.logger.info("reading crypto coin codes from kafka topic")
        df_crypto_code = self.asset_utils.get_crypto_assets()
        df_crypto_code = df_crypto_code.select(col("id"), col("symbol"))
        self.logger.info("successfully read crypto codes from kafka")
        return df_crypto_code

    def get_crypto_price_with_retries(self, symbol, max_retries=3, retry_delay=2):
        def log_retry_attempt(retry_state):
            self.logger.warning(
                f"API call failed for {symbol}, retrying... Attempt {retry_state.attempt_number}/{max_retries}. "
                f"Exception: {retry_state.outcome.exception()}")

        @retry(
            stop=stop_after_attempt(max_retries),
            wait=wait_fixed(retry_delay),
            retry=retry_if_exception_type((requests.exceptions.RequestException, ValueError, KeyError)),
            before_sleep=log_retry_attempt
        )
        def _make_api_call():
            url = f"{self.config['crypto_currency_price_api']}?cryptocurrency={symbol}"
            response = requests.get(url)
            if response.status_code > 200:
                raise requests.exceptions.HTTPError(f"HTTP error {response.status_code} for {symbol}")

            price_data = response.json()
            if "data" not in price_data or "sellPrice" not in price_data["data"]:
                raise KeyError(f"Invalid response format for {symbol}")

            return float(price_data["data"]["sellPrice"])

        try:
            return _make_api_call()
        except Exception as e:
            self.logger.error(f"Failed to get price for {symbol} after {max_retries} attempts: {str(e)}")
            raise

    def get_staking_snapshot_data(self):
        
        def get_staking_assets_data():
            staking_assets_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.staking_assets_snapshot, self.t_1)
            self.logger.info("reading Staking Assets Data from path {}".format(staking_assets_path))
            staking_assets_df = self.io_utils.read_parquet_data(staking_assets_path)
            # self.logger.info(" Initial staking_assets_df data")
            # staking_assets_df.show(truncate=False)
            staking_assets_df = staking_assets_df.withColumn("reward_frequency",
                                                             F.get_json_object("reward_disbursal_frequency","$.interval")) \
                                                    .withColumn("reward_cron_schedule",
                                                             F.get_json_object("reward_disbursal_frequency", "$.cron")) \
                                                    .withColumn("reward_disbursal_window",
                                                                F.get_json_object("reward_disbursal_frequency", "$.windowType"))
            staking_assets_df = staking_assets_df.select("crypto_currency_id", "reward_frequency", "reward_cron_schedule", "reward_disbursal_window")
            # self.logger.info("staking_assets_df data")
            # staking_assets_df.show(truncate=False)
            return staking_assets_df

        def get_staking_accrued_rewards_data():
            staking_accrued_rewards_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.staking_accrued_rewards_snapshot, self.t_1)
            self.logger.info("reading Staking accrued rewards Data from path {}".format(staking_accrued_rewards_path))
            staking_accrued_rewards_df = self.io_utils.read_parquet_data(staking_accrued_rewards_path)
            staking_accrued_rewards_df = staking_accrued_rewards_df.select("id","account_id", "user_id", "client_id", "partner_id",
                                                                         "crypto_currency_id", "ref_id", "eligible_staked_quantity",
                                                                         "gross_reward_quantity", "reward_trigger_time","created")
            # staking_accrued_rewards_df.show(truncate=False)
            return staking_accrued_rewards_df

        staking_assets_df = get_staking_assets_data()
        staking_accrued_rewards_df = get_staking_accrued_rewards_data()

        return staking_assets_df, staking_accrued_rewards_df

    def transform_accrued_rewards_data(self, staking_accounts_data):
        def cron_info(cron_expr):
            try:
                now = datetime.now()
                curr_run = croniter(cron_expr, now).get_current(datetime)
                # check if cron is weekly or monthly
                fields = cron_expr.split()
                if len(fields) != 5:
                    return False, None

                day_of_month, month, day_of_week = fields[2], fields[3], fields[4]
                is_weekly = day_of_week != "*"
                is_monthly = day_of_month != "*"

                if not (is_weekly or is_monthly):
                    return False, None

                return True, curr_run.date()
            except Exception:
                return False, None

        cron_info_udf = F.udf(cron_info, "struct<is_cron_valid:boolean,cron_run_date:date>")
        staking_accounts_data_with_cron = staking_accounts_data.withColumn("cron_info", cron_info_udf(F.col("reward_cron_schedule"))) \
            .withColumn("is_cron_valid", F.col("cron_info.is_cron_valid")) \
            .withColumn("cron_run_date", F.col("cron_info.cron_run_date"))
        # self.logger.info("staking_accounts_data_with_cron data")
        # staking_accounts_data_with_cron.select("id", "account_id", "crypto_currency_id", "reward_cron_schedule", "is_cron_valid", "cron_run_date").show(truncate=False)
        return staking_accounts_data_with_cron

    def validate_accrued_rewards_data(self, transformed_staking_accounts_data):
        invalid_count = transformed_staking_accounts_data.filter(~col("is_cron_valid")).count()
        if invalid_count > 0:
            self.logger.error(f"Found {invalid_count} invalid cron expressions in reward_cron_schedule")

        staking_accrual_history_df = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(
                                                                                    self.bucket_path,
                                                                                           S3Paths.staking_rewards_accrual_history,
                                                                                           self.t_1)).select("account_id",
                                                                                                        "crypto_currency_id",
                                                                                                        "ref_id",
                                                                                                        "eligible_staked_quantity"
                                                                                                        ).alias("history_df")
        transformed_df_for_comparison = transformed_staking_accounts_data.select(
                                            "account_id", "crypto_currency_id", "ref_id",
                                            "eligible_staked_quantity").alias("transformed_df")
        joined_df = transformed_df_for_comparison.join(staking_accrual_history_df,
                                                    on=["account_id", "crypto_currency_id", "ref_id"],
                                                    how="inner")
        mismatched_records = joined_df.filter(
                                    col("transformed_df.eligible_staked_quantity") != col("history_df.eligible_staked_quantity")
                                    )

        mismatch_count = mismatched_records.count()
        if mismatch_count > 0:
            mismatched_ref_ids = mismatched_records.select("ref_id").rdd.flatMap(lambda x: x).collect()
            self.logger.error(f"Found {mismatch_count} records with mismatched eligible_staked_quantity values")
            self.logger.error(f"Mismatched ref_ids: {mismatched_ref_ids}")

        self.logger.info("Validation successful: All eligible_staked_quantity values match between the two dataframes")

    def get_crypto_currency_unit_price(self, staking_data):
        coin_code = self.get_crypto_codes().withColumnRenamed("id", "crypto_currency_id")
        self.logger.info("Getting crypto currency unit price")

        distinct_crypto_ids = staking_data.select("crypto_currency_id").distinct()
        distinct_crypto_with_symbols = distinct_crypto_ids.join(
            coin_code,
            on="crypto_currency_id",
            how="inner"
        )
        distinct_symbols = distinct_crypto_with_symbols.collect()
        self.logger.info(f"Found {len(distinct_symbols)} distinct cryptocurrencies in staking data")

        price_data = []
        for row in distinct_symbols:
            crypto_id = row["crypto_currency_id"]
            symbol = row["symbol"]
            try:
                price = self.get_crypto_price_with_retries(symbol)
                price_data.append((crypto_id, float(price)))

            except Exception as e:
                self.logger.error(f"Error getting price for {symbol}: {str(e)}")

        price_schema = StructType([
            StructField("crypto_currency_id", StringType(), False),
            StructField("unit_price", DoubleType(), False)
        ])
        price_df = self.spark.createDataFrame(price_data, price_schema)

        # self.logger.info("price_df data")
        # price_df.show(truncate=False)
        # self.logger.info(f"price_df count data = {price_df.count()}")

        staking_data_with_coin_code = staking_data.join(
            coin_code.join(price_df, on="crypto_currency_id", how="inner"),
            on="crypto_currency_id",
            how="inner"
        )

        # self.logger.info("staking_data_with_coin_code data")
        # staking_data_with_coin_code.show(truncate=False)
        return staking_data_with_coin_code

    def get_aggregated_accrued_rewards(self, staking_accounts_df):
        def fetch_rewards_disbursal_history():
            staking_disbursal_history_df = self.io_utils.read_parquet_data("{}/{}/dt={}/".format(
                                                                    self.bucket_path,
                                                                   S3Paths.staking_rewards_disbursal_history, self.t_2),
                                                                   crypto_currency_rewards_disbursal_history_schema,
                                        True).select("account_id",
                                                                             "crypto_currency_id",
                                                                             "last_reward_disbursal_date")
            # self.logger.info("printing staking_disbursal_history_df data")
            # staking_disbursal_history_df.show(truncate=False)
            if staking_disbursal_history_df.count() != 0:
                window_spec = Window.partitionBy("account_id", "crypto_currency_id").orderBy(F.desc("last_reward_disbursal_date"))
                staking_disbursal_history_df = staking_disbursal_history_df.withColumn("row_number", row_number().over(window_spec)) \
                    .filter(F.col("row_number") == 1) \
                    .drop("row_number")
            return staking_disbursal_history_df

        def get_cutoff_date(reward_frequency, reward_disbursal_window):
            if reward_frequency == "WEEKLY":
                if reward_disbursal_window == "ROLLING":
                    return current_date
                elif reward_disbursal_window == "CALENDAR":
                    return last_sunday
            elif reward_frequency == "MONTHLY":
                if reward_disbursal_window == "ROLLING":
                    return current_date
                elif reward_disbursal_window == "CALENDAR":
                    return last_day_of_prev_month
            return current_date

        current_date = self.current_date
        days_since_sunday = current_date.weekday() + 1  # +1 because weekday() returns 0 for Monday, 6 for Sunday
        last_sunday = current_date - timedelta(days=days_since_sunday)
        last_day_of_prev_month = current_date.replace(day=1) - timedelta(days=1)

        staking_disbursal_history_df = fetch_rewards_disbursal_history()
        staking_accounts_with_history = staking_accounts_df.join(
            staking_disbursal_history_df,
            on=["account_id", "crypto_currency_id"],
            how="left"
        )
        staking_accounts_with_history = self.get_crypto_currency_unit_price(staking_accounts_with_history)
        staking_accounts_with_history = staking_accounts_with_history.select("id", "account_id",
                                                                             "user_id", "client_id", "partner_id",
                                                                             "crypto_currency_id",
                                                                             "unit_price", "ref_id", "eligible_staked_quantity",
                                                                             "gross_reward_quantity", "reward_trigger_time",
                                                                             "reward_frequency", "reward_cron_schedule",
                                                                             "reward_disbursal_window", "cron_run_date", "created",
                                                                             "last_reward_disbursal_date")

        get_cutoff_date_udf = F.udf(get_cutoff_date, DateType())
        staking_accounts_with_history = staking_accounts_with_history.withColumn(
            "cutoff_date",
            get_cutoff_date_udf(col("reward_frequency"), col("reward_disbursal_window"))
        )
        self.logger.info("staking_accounts_with_history rewards")
        # staking_accounts_with_history.show(truncate=False)
        filtered_accounts = staking_accounts_with_history.filter(
            (col("cron_run_date") == self.current_date) &
            ((col("last_reward_disbursal_date").isNull()) |
             (F.date_format(col("created"), "yyyy-MM-dd") > col("last_reward_disbursal_date"))
             ) &
            (F.date_format(col("created"), "yyyy-MM-dd") <= col("cutoff_date"))
        )
        self.logger.info("Filtering rewards")
        # filtered_accounts.show(truncate=False)

        aggregated_rewards = filtered_accounts.groupBy("account_id", "crypto_currency_id") \
            .agg(
            sum("gross_reward_quantity").alias("gross_reward_quantity"),
            F.min("id").alias("first_accrued_reward_id"),
            F.max("id").alias("last_accrued_reward_id"),
            F.min("created").alias("first_accrued_reward_timestamp"),
            F.max("created").alias("last_accrued_reward_timestamp"),
            first("user_id").alias("user_id"),
            first("client_id").alias("client_id"),
            first("partner_id").alias("partner_id"),
            last("reward_frequency").alias("reward_frequency"),
            last("unit_price").alias("unit_price")
        )

        aggregated_rewards = aggregated_rewards.filter(col("gross_reward_quantity") > 0)
        current_date_formatted = self.current_date.strftime("%Y%m%d")
        aggregated_rewards = aggregated_rewards.withColumn("ref_id",
                                                                 concat(
                                                                     col("account_id"),
                                                                     F.lit("_"),
                                                                     col("crypto_currency_id"),
                                                                     F.lit("_"),
                                                                     F.lit(current_date_formatted)
                                                                 )) \
                                                .withColumn("last_reward_disbursal_date", lit(self.current_date))
        # self.logger.info("Aggregated rewards")
        # aggregated_rewards.show(truncate=False)
        return aggregated_rewards

    def create_kafka_events(self, staking_rewards_df):

        def transform_nested_rewards(staking_rewards_df):
            grouped_df = staking_rewards_df.groupBy("account_id", "user_id", "client_id", "partner_id", "reward_trigger_time"
            ).agg(F.collect_list(
                    F.struct(
                        col("crypto_currency_id"),
                        col("ref_id"),
                        col("gross_reward_quantity"),
                        col("reward_frequency"),
                        col("first_accrued_reward_id"),
                        col("last_accrued_reward_id"),
                        col("first_accrued_reward_timestamp"),
                        col("last_accrued_reward_timestamp")
                    )
                ).alias("rewards_list"))

            # Convert list to map structure
            result_df = grouped_df.select("account_id", "user_id", "client_id", "partner_id", "reward_trigger_time",
                                            F.map_from_arrays(
                                                F.transform(col("rewards_list"), lambda x: x.crypto_currency_id),
                                                F.transform(col("rewards_list"), lambda x: F.struct(
                                                    x.ref_id.alias("ref_id"),
                                                    x.gross_reward_quantity.alias("gross_reward_quantity"),
                                                    x.reward_frequency.alias("reward_frequency"),
                                                    x.first_accrued_reward_id.alias("first_accrued_reward_id"),
                                                    x.last_accrued_reward_id.alias("last_accrued_reward_id"),
                                                    x.first_accrued_reward_timestamp.alias("first_accrued_reward_timestamp"),
                                                    x.last_accrued_reward_timestamp.alias("last_accrued_reward_timestamp")
                                                ))
                                            ).alias("rewards"))
            return result_df

        publish_time = DateUtils.get_utc_timestamp()
        staking_rewards_df = staking_rewards_df.withColumn("reward_trigger_time", lit(publish_time))
        # kafka_event = self.ops.convert_columns_snake_to_camel_case(staking_rewards_df)
        kafka_event = staking_rewards_df
        kafka_event = transform_nested_rewards(kafka_event)
        cols_to_publish = kafka_event.columns
        kafka_event = kafka_event.withColumn("data", F.struct(cols_to_publish))

        kafka_event = kafka_event.withColumn("type", lit("admin")).withColumn("id", lit(self.config["crypto_staking_topic_actor_prop"]))
        kafka_event = kafka_event.withColumn("actor", F.struct(["type", "id"])).drop("id", "type")
        kafka_event = kafka_event.withColumn("x-request-id", F.expr("uuid()"))
        kafka_columns = ["actor", "data"]
        kafka_event = kafka_event.select(col("account_id").cast(StringType()).alias("key"),
                                         F.to_json(F.struct(kafka_columns)).alias("value"),
                                         F.array(F.struct(lit("x-request-id").alias("key"), col("x-request-id").cast("binary").alias("value"))).alias("headers")).drop("x-request-id")
        return kafka_event

    def write_rewards_disbursal_history(self, staking_aggregated_rewards):
        self.io_utils.write_parquet_file(staking_aggregated_rewards, "{}/{}/dt={}/".format(self.bucket_path, S3Paths.staking_rewards_disbursal_history, self.t_1),3)

    def start_processing(self):
        staking_accounts_data, staking_accrued_rewards_data = self.get_staking_snapshot_data()
        staking_accounts_data = staking_accounts_data.join(staking_accrued_rewards_data, on="crypto_currency_id", how="inner")
        staking_accounts_data = staking_accounts_data.select("id","account_id", "user_id", "client_id", "partner_id",
                                                                         "crypto_currency_id", "ref_id", "eligible_staked_quantity",
                                                                         "gross_reward_quantity","reward_frequency", "reward_cron_schedule",
                                                                        "reward_disbursal_window","reward_trigger_time","created")
        # self.logger.info("Start processing staking rewards disbursal data")
        # staking_accounts_data.show(truncate=False)
        transformed_staking_accounts_data = self.transform_accrued_rewards_data(staking_accounts_data)
        self.validate_accrued_rewards_data(transformed_staking_accounts_data)
        staking_aggregated_rewards = self.get_aggregated_accrued_rewards(transformed_staking_accounts_data)
        kafka_events = self.create_kafka_events(staking_aggregated_rewards)
        # kafka_events.show(truncate=False)
        count_kafka_events = kafka_events.count()
        self.logger.info(f"Total accounts with staked balance: {count_kafka_events}")
        self.io_utils.write_data_in_kafka(kafka_events, self.config["kafka_topics"]["staking_user_disbursed_rewards_topic"])
        self.write_rewards_disbursal_history(staking_aggregated_rewards)

    def run(self):
        self.start_processing()
        self.spark_utils.stop_spark(self.spark)
