from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.utils.asset_utils import AssetUtils
from src.enums.crypto_staking import StakingStatus

class StakingRequestsProcessing:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("staking_requests_processing")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.asset_utils = AssetUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")

        # Handling time based variables
        self.offset = self.config["offset"]
        self.execution_time = self.config["execution_time"]
        self.t_1 = self.config["t_1"]
        self.t_2 = self.config["t_2"]
        self.current_date = DateUtils.get_jkt_date(self.offset)

    def get_crypto_codes(self):
        self.logger.info("reading crypto coin codes from kafka topic")
        df_crypto_code = self.asset_utils.get_crypto_assets()
        df_crypto_code = df_crypto_code.select(col("id"), col("symbol"))
        self.logger.info("successfully read crypto codes from kafka")
        return df_crypto_code

    def get_requests_snapshot_data(self):
        staking_requests_snapshot_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.staking_requests_snapshot, self.t_1)
        self.logger.info("reading Staking Requests Data from path {}".format(staking_requests_snapshot_path))
        staking_requests_df = self.io_utils.read_parquet_data(staking_requests_snapshot_path)
        return staking_requests_df

    def get_pending_requests_for_today(self, staking_requests_df):
        pending_requests_today = staking_requests_df.filter(
            (col("system_status").isin([s.value for s in StakingStatus.non_terminal_status()])) &
            (F.to_date(F.from_utc_timestamp("next_transition_time", Constants.JKT_TIMEZONE)) == lit(
                str(self.current_date)).cast("date"))
        )
        self.logger.info(f"Pending requests today: {pending_requests_today.count()}")
        pending_requests_today = pending_requests_today.select(col("id").alias("transaction_id"), "system_status", "user_status")

        return pending_requests_today

    def validate_requests_snapshot_data(self, staking_requests_data):
        """ Check for Records with terminal status and non-null next_transition_time
        """
        requests_with_terminal_status = staking_requests_data.filter(col("system_status").isin([s.value for s in StakingStatus.terminal_status()]))
        null_next_time_count = requests_with_terminal_status.filter(col("next_transition_time").isNull()).count()
        if null_next_time_count != requests_with_terminal_status.count():
            non_null_next_transition_time = self.ops.check_non_null_values(requests_with_terminal_status, "id", "next_transition_time")
            self.logger.error("request ids with non-null next_transition_time: {}".format(non_null_next_transition_time))

        """ Check for records with next_transition_time < today
        """
        requests_with_invalid_next_transition_time = staking_requests_data.filter(col("next_transition_time") < self.current_date)
        if requests_with_invalid_next_transition_time.count() > 0:
            self.logger.error("request ids with invalid next_transition_time: {}".format(requests_with_invalid_next_transition_time.select("id").rdd.flatMap(lambda x: x).collect()))

    def create_user_transaction_updates_kafka_events(self, pending_requests_today):
        publish_time = DateUtils.get_utc_timestamp()
        pending_requests_today = pending_requests_today.withColumn("trigger_time", lit(publish_time))
        pending_requests_today = pending_requests_today.select("transaction_id", col("user_status").alias("current_user_status"), col("system_status").alias("current_system_status"), "trigger_time")
        # kafka_event = self.ops.convert_columns_snake_to_camel_case(pending_requests_today)
        kafka_event = pending_requests_today
        cols_to_publish = kafka_event.columns
        kafka_event = kafka_event.withColumn("data", F.struct(cols_to_publish))

        kafka_event = kafka_event.withColumn("type", lit("admin")).withColumn("id", lit(self.config["crypto_staking_topic_actor_prop"]))
        kafka_event = kafka_event.withColumn("actor", F.struct(["type", "id"])).drop("id", "type")
        kafka_event = kafka_event.withColumn("x-request-id", F.expr("uuid()"))
        kafka_columns = ["actor", "data"]
        kafka_event = kafka_event.select(col("transaction_id").cast(StringType()).alias("key"),
                                         F.to_json(F.struct(kafka_columns)).alias("value"),
                                         F.array(F.struct(lit("x-request-id").alias("key"), col("x-request-id").cast("binary").alias("value"))).alias("headers")).drop("x-request-id")
        return kafka_event

    def get_net_staking_amount(self, staking_requests_df):
        staking_requests_df = staking_requests_df.select("crypto_currency_id", "quantity","system_status", "created")
        staking_requests_df = staking_requests_df.withColumn("created", F.from_utc_timestamp("created", Constants.JKT_TIMEZONE))
        yesterday = DateUtils.get_jkt_date(1)
        jkt_timezone = Constants.JKT_TIMEZONE
        yesterday_9am = F.to_timestamp(
            F.concat(
                F.lit(str(yesterday)),
                F.lit(" 09:00:00")
            ),
            "yyyy-MM-dd HH:mm:ss"
        )
        yesterday_9am = F.to_utc_timestamp(yesterday_9am, jkt_timezone)
        self.logger.info(f"yesterday_9am: {yesterday_9am}")
        filtered_df = staking_requests_df.filter(
            (col("created") >= yesterday_9am) &
            (col("system_status").isin([
                StakingStatus.STAKING_REQUESTED.value,
                StakingStatus.UNSTAKING_IN_WAITING.value
            ]))
        )
        net_staking_amount = filtered_df.groupBy("crypto_currency_id").agg(
            F.sum(
                F.when(
                    col("system_status") == StakingStatus.STAKING_REQUESTED.value,
                    col("quantity")
                ).otherwise(F.lit(0))
            ).alias("staking_requested_amount"),

            F.sum(
                F.when(
                    col("system_status") == StakingStatus.UNSTAKING_IN_WAITING.value,
                    col("quantity")
                ).otherwise(F.lit(0))
            ).alias("unstaking_in_waiting_amount")
        )
        net_staking_amount = net_staking_amount.withColumn(
            "net_staking_amount",
            col("staking_requested_amount") - col("unstaking_in_waiting_amount")
        )
        net_staking_amount = net_staking_amount.withColumn(
            "transaction_type",
            F.when(col("net_staking_amount") < 0, "UNSTAKING").otherwise("STAKING")
        )
        net_staking_amount = net_staking_amount.withColumnRenamed("net_staking_amount", "quantity")
        net_staking_amount = net_staking_amount.select("crypto_currency_id", "quantity", "transaction_type")
        coin_code = self.get_crypto_codes().withColumnRenamed("id", "crypto_currency_id")
        joined_df = net_staking_amount.join(coin_code, on="crypto_currency_id", how="left")
        current_date_formatted = self.current_date.strftime("%Y%m%d")
        result_df = joined_df.withColumn(
            "ref_id",
            concat(
                col("crypto_currency_id"),
                lit("_"),
                col("symbol"),
                lit("_"),
                lit(current_date_formatted)
            )
        )
        return result_df

    def create_net_staking_amount_kafka_events(self, net_staking_amount):
        # kafka_event = self.ops.convert_columns_snake_to_camel_case(net_staking_amount)
        kafka_event = net_staking_amount
        cols_to_publish = [c for c in kafka_event.columns]
        cols_to_publish.remove("transaction_type")
        kafka_event = kafka_event.withColumn("data", F.struct(cols_to_publish))

        kafka_event = kafka_event.withColumn("x-request-id", F.expr("uuid()")) \
                                    .withColumn("x-referer-service", lit("data-engineering"))
        kafka_columns = ["data"]
        kafka_event = kafka_event.select(col("crypto_currency_id").cast(StringType()).alias("key"),
                                         F.to_json(F.struct(kafka_columns)).alias("value"),
                                         F.array(
                                             F.struct(lit("x-request-id").alias("key"),
                                                      col("x-request-id").cast("binary").alias("value")),
                                             F.struct(lit("x-referer-service").alias("key"),
                                                      col("x-referer-service").cast("binary").alias("value")),
                                             F.struct(lit("transaction_type").alias("key"),
                                                      col("transaction_type").cast("binary").alias("value"))
                                         ).alias("headers")).drop("x-request-id", "x-referer-service")
        return kafka_event

    def write_staking_events_to_kafka(self, kafka_events, topic):
        # kafka_events.show(truncate=False)
        count_kafka_events = kafka_events.count()
        self.logger.info(f"Total accounts with staked balance: {count_kafka_events}")
        self.io_utils.write_data_in_kafka(kafka_events,topic)

    def start_processing(self):
        staking_requests_data = self.get_requests_snapshot_data()
        self.validate_requests_snapshot_data(staking_requests_data)
        pending_requests_today = self.get_pending_requests_for_today(staking_requests_data)
        self.logger.info("Pending requests today: {}".format(pending_requests_today.count()))
        # pending_requests_today.show(truncate=False)

        # Processing for Transaction Updates
        user_transaction_updates_kafka_events = self.create_user_transaction_updates_kafka_events(pending_requests_today)
        self.write_staking_events_to_kafka(user_transaction_updates_kafka_events, self.config["kafka_topics"]["staking_user_transactions_topic"])

        # Processing for Net Staking Amount
        net_staking_amount = self.get_net_staking_amount(staking_requests_data)
        net_staking_amount_kafka_events = self.create_net_staking_amount_kafka_events(net_staking_amount)
        self.write_staking_events_to_kafka(net_staking_amount_kafka_events, self.config["kafka_topics"]["crypto_net_staking_requests"])

    def run(self):
        self.start_processing()
        self.spark_utils.stop_spark(self.spark)
