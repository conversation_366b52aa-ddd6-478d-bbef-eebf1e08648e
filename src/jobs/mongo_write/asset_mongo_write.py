from src.utils.spark_utils import *
from src.utils.io_utils import IOUtils
from src.utils.date_utils import DateUtils
from src.utils.s3_paths import S3Paths
from src.utils.python_utils import PythonUtils

class AssetMongoWrite:
    """
    AssetMongoWrite handles reading, transforming, and writing asset-related data
    to MongoDB from an S3 location.
    """
    INDO_STOCK_RETURNS_V2 = "indo_stock_returns_v2"

    def __init__(self, config: dict, **kwargs):
        """
        Initialize class variables and utilities.

        Args:
            config (dict): Configuration dictionary with paths, Mongo config, etc.
        """
        self.logger = get_logger()
        self.spark_utils = SparkUtils("AssetMongoWrite")
        self.spark = self.spark_utils.create_spark_session()
        self.config = config
        self.offset = config.get("offset")
        self.t_1_date = config.get("t_1")
        self.bucket = config.get("bucket")
        self.io_utils = IOUtils(self.spark, config)
        self.date_utils = DateUtils()
        self.s3_path = S3Paths()
        self.asset_name = kwargs.get("asset_name")

    def transform_indo_stock_returns_v2(self, df):
        """
        Transform Indo stock returns data to the required nested Mongo format.

        Args:
            df (DataFrame): Raw Indo stock returns DataFrame.

        Returns:
            DataFrame: Transformed DataFrame with nested returns and createdAt.
        """
        self.logger.info("Transforming Indo stock returns data")

        returns_df = df.select(
            "accountId",
            F.struct(
                "id", "stockId", "weightedCost", "realisedGain", "createdAt",
                "userId", "totalQuantity", "unitPrice", "totalValue",
                F.col("unrealisedGain").cast("string")
            ).alias("returns")
        )

        grouped_df = returns_df.groupBy("accountId").agg(
            F.collect_list("returns").alias("returns")
        )

        return grouped_df.withColumn("createdAt", F.lit(self.date_utils.get_utc_timestamp()))

    def transform_column_name(self, col):
        """
        Rename 'created' column to 'created_at', otherwise keep unchanged.

        Args:
            col (str): Column name.

        Returns:
            str: Transformed column name.
        """
        return f"{col}_at" if col == "created" else col

    def convert_to_camel_case(self, df):
        """
        Convert column names from snake_case to camelCase.

        Args:
            df (DataFrame): Input DataFrame.

        Returns:
            DataFrame: Renamed DataFrame.
        """
        self.logger.info("Converting column names to camelCase")

        rename_map = {
            col: PythonUtils.convert_snake_to_camel_case(self.transform_column_name(col))
            for col in df.columns
        }

        for old, new in rename_map.items():
            if old != new:
                df = df.withColumnRenamed(old, new)

        return df

    def read_returns_data(self):
        """
        Read returns data from S3 (or local for debug) for a given asset.

        Returns:
            DataFrame: Spark DataFrame with input data.
        """
        path_to_s3 = None
        if self.asset_name == self.INDO_STOCK_RETURNS_V2:
            path_to_s3 = f"s3a://{self.bucket}/{self.s3_path.indo_stock_returns_v2_snapshots}/dt={self.t_1_date}"
        else:
            # Can add more assets here as needed
            pass
            
        df = self.io_utils.read_csv_file(path=path_to_s3)
        self.logger.info(f"Read {self.asset_name} returns data from: {path_to_s3}, Count: {df.count()}") 
        return df

    def transform_before_mongo_write(self, df):
        """
        Perform asset-specific transformation before Mongo write.

        Args:
            self.asset_name (str): Asset identifier.
            df (DataFrame): Input DataFrame.

        Returns:
            DataFrame: Transformed DataFrame.
        """
        if self.asset_name == self.INDO_STOCK_RETURNS_V2:
            return self.transform_indo_stock_returns_v2(df)
        else:
            # Add more asset transformations as needed
            pass
            
        return df

    def prepare_mongo_write_config(self):
        """
        Build Mongo write configuration for a given asset.

        Args:
            self.asset_name (str): Asset identifier.

        Returns:
            dict: Mongo write configuration dictionary.
        """
        mongo_uri= str()
        collection = None
        shard_key = None
        write_format = "insert"
        if self.asset_name ==self.INDO_STOCK_RETURNS_V2:
            mongo_cfg = self.config["data_store"]["reporting_mongo"]
            collection = self.config["data_store"]["indo_stock_v2"]["collection"]
            mongo_cfg["collection"] = collection
            mongo_uri = self.io_utils.get_mongo_connection_string(mongo_cfg)
            shard_key = "{'accountId':1,'createdAt':1}"
            write_format  = "update"
        else:
            # Add more asset configurations as needed
            pass

        config = {
            "uri": mongo_uri,
            "collection": collection,
            "batch_size": "500",
            "mode": "append"
        }
        self.logger.info(f"Prepared Mongo write config for {self.asset_name}, collection: {collection}, uri: {mongo_uri}")
        return config, shard_key, write_format

    def write_to_mongo(self, df, mongo_write_config: dict, shard_key: str, write_format: str):
        """
        Write transformed data to MongoDB.

        Args:
            self.asset_name (str): Asset identifier.
            df (DataFrame): Transformed DataFrame.
            mongo_write_config (dict): MongoDB write config.
        """
        self.logger.info(f"Writing {self.asset_name} data to MongoDB")
        
        self.io_utils.write_dataset_to_mongo(
            df,
            mongo_write_config,
            f"{self.asset_name}_mongo_write",
            write_format,
            shard_key,
            add_created_at=False
        )

    def run(self):
        """
        Main execution flow for reading, transforming, and writing data.
        """
        self.logger.info(f"Starting Mongo write process for asset: {self.asset_name}")


        df = self.read_returns_data()
        if df is None:
            self.logger.error("No data found to process.")
            return

        # Convert to camel case
        df = self.convert_to_camel_case(df)

        # Apply transformation
        transformed_df = self.transform_before_mongo_write(df)

        # Prepare Mongo config
        mongo_config, shard_key, write_format = self.prepare_mongo_write_config()

        # Step 5: Write to Mongo
        self.write_to_mongo(transformed_df, mongo_config, shard_key, write_format)

        self.logger.info(f"Completed Mongo write for asset: {self.asset_name}")
