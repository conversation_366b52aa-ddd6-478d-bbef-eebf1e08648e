from src.utils.spark_utils import *
import time


class PortfolioMongoWrite:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("portfolio_mongo_write")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.num_of_partition = config["portfolio_mongo_write_num_of_partition"]
        self.sleep_time = config["portfolio_mongo_write_sleep_time"]

        self.write_format = "insert"
        if kwargs.get("write_format") is not None:
            self.write_format = kwargs.get("write_format")

        self.mongo_collection_shard_key = "{'accountId':1,'createdAt':1}"

        self.logger.info("Portfolio Mongo Write initialised successfully with t_1: {}, t_2: {}"
                         .format(self.t_1, self.t_2))

    def transform_portfolio_and_write_in_mongo(self):
        portfolio_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.portfolio_snapshot_folder, self.t_1)
        )
        portfolio_snap = portfolio_snap.filter(
            (col("idrAssetsValue") > 0) | (col("usdAssetsValueInIdr") > 0) |
            (col("realisedGainValue") != 0) | (col("unrealisedGainValue") != 0) |
            (col("portfolioValue") > 0) | (col("portfolioAUM") > 0) |
            (col("cashDiffValue") > 0) | (col("totalCost") > 0) |
            (col("usdAssetsValueInUsd") > 0)
        )

        current_timestamp = DateUtils.get_jkt_timestamp(self.config["offset"]+1).date()
        portfolio_snap = portfolio_snap.withColumn("created", lit(current_timestamp))

        updated_timestamp = DateUtils.get_jkt_timestamp()
        portfolio_snap = portfolio_snap.withColumn("updated_at", lit(updated_timestamp))

        self.logger.info("Updating calculated value to mongo")

        for i in range(0, self.num_of_partition):
            self.logger.info("writing in mongo for partition number: {}".format(i))
            df = portfolio_snap.filter(col("account_id") % self.num_of_partition == i)

            mongo_config = self.config["data_store"]["reporting_mongo"]
            mongo_config["collection"] = "{}_{}_{}".format(
                self.config["data_store"]["portfolio_snapshot"]["collection"],
                "snapshot", i)

            mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)

            mongo_write_config = {
                "uri": mongo_uri,
                "collection": mongo_config["collection"],
                "batch_size": "500",
                "mode": "append"
            }
            self.io_utils.write_dataset_to_mongo(
                df,
                mongo_config=mongo_write_config,
                asset_name="portfolio snapshot",
                write_format=self.write_format,
                shardkey=self.mongo_collection_shard_key,
                add_created_at=False
            )
            time.sleep(self.sleep_time)

    def run(self):
        self.transform_portfolio_and_write_in_mongo()
        self.spark_utils.stop_spark(self.spark)
