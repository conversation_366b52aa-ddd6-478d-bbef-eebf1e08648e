# Core PySpark and Data Processing
pyspark==3.4.1
py4j==********

# AWS S3 Support for PySpark
boto3>=1.26.0
botocore>=1.29.0

# Data Processing and Utilities
pandas>=1.5.0
numpy>=1.21.0

# Date and Time Utilities
python-dateutil==2.9.0.post0
pytz==2025.2

# Redis for Caching and Data Storage
redis==6.2.0

# Logging and Monitoring
json-logging==1.5.1

# Retry and Error Handling
tenacity>=8.0.0

# HTTP and Async Support
async-timeout==5.0.1

# Compatibility
six==1.17.0

# Optional: For enhanced data processing
# Uncomment if needed for specific use cases
# scipy>=1.9.0
# scikit-learn>=1.1.0
# matplotlib>=3.5.0
# seaborn>=0.11.0

# Development and Testing (optional)
# pytest>=7.0.0
# pytest-cov>=4.0.0
# pytest-mock>=3.10.0
# mock>=5.0.0
