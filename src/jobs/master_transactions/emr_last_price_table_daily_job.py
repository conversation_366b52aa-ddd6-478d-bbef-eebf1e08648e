from typing import Optional, Dict, Any

from pyspark.sql import SparkSession, DataFrame

from src.utils.config_loader import ConfigLoader
from src.utils.spark_utils import SparkUtils
from src.utils.custom_logger import get_logger
from src.utils.io_utils import IOUtils
from src.jobs.master_transactions.transformers.post_processors.last_price_table_daily_processor import (
    build_detail_last_price_table_daily,
)


class LastPriceTableDailyJob:
    """
    EMR-compatible job wrapper to build detail_last_price_table_daily using Spark.
    Mirrors dbt model logic and writes partitioned by day.
    """

    def __init__(self, config: Dict[str, Any], **kwargs):
        self.logger = get_logger()

        config_loader = ConfigLoader("aws")
        master_config = config_loader.load_master_transaction_config()
        self.config = {**config, **master_config}
        self.kwargs = kwargs

        self.t_1 = self.config.get("t_1") or kwargs.get("t_1")
        self.h_1 = self.config.get("h_1") or kwargs.get("h_1") or self.t_1

        self.spark_utils = SparkUtils("LastPriceTableDailyJob")
        self.spark = self.spark_utils.create_spark_session()
        self.logger.info("✅ Spark session created for LastPriceTableDailyJob")

    def _resolve_path(self, base: Optional[str], suffix: str) -> Optional[str]:
        if base is None:
            return None
        if base.startswith("s3a://") or base.startswith("s3://"):
            return f"{base}dt={self.t_1}/{suffix}"
        if base.startswith("file://"):
            sep = "" if base.endswith("/") else "/"
            return f"{base}{sep}dt={self.t_1}/{suffix}"
        bucket_path = self.config.get("bucket_path", "s3a://pluang-datalake-calculated-staging/")
        sep = "" if base.startswith("/") else ""
        return f"{bucket_path}{base}{sep}dt={self.t_1}/{suffix}"

    def execute(self):
        self.logger.info("🚀 Building detail_last_price_table_daily")
        self.logger.info(f"Processing date: {self.t_1}")

        try:
            io_utils = IOUtils(self.spark, self.config)

            # Resolve input paths from config; allow flexible keys
            lp_conf = self.config.get("last_price_table_daily", {})
            app_base = (
                lp_conf.get("app_price_path")
                or self.config.get("detail_last_price_table_app_daily_path")
            )
            hedging_base = (
                lp_conf.get("lp_price_path")
                or self.config.get("detail_last_price_table_hedging_daily_path")
            )
            gss_lookup_path = (
                lp_conf.get("gss_lookup_path")
                or self.config.get("gss_lookup_path")
                or self.config.get("pluang_global_stocks_lookup_path")
            )

            if app_base is None or hedging_base is None:
                raise ValueError(
                    "Input paths for app or hedging prices are not configured."
                )

            app_path = self._resolve_path(app_base, "")
            lp_path = self._resolve_path(hedging_base, "")

            self.logger.info(f"Reading app prices from: {app_path}")
            self.logger.info(f"Reading hedging prices from: {lp_path}")

            app_df = io_utils.read_parquet(app_path)
            lp_df = io_utils.read_parquet(lp_path)
            gss_df: Optional[DataFrame] = None
            if gss_lookup_path:
                try:
                    gss_df = io_utils.read_parquet(gss_lookup_path)
                except Exception:
                    self.logger.warning("GSS lookup path not found or unreadable; continuing without it")

            if app_df is None or lp_df is None:
                self.logger.warning("No input data for last price build; skipping write")
                return

            result = build_detail_last_price_table_daily(
                app_price_df=app_df,
                lp_price_df=lp_df,
                gss_lookup_df=gss_df,
                start_date=self.t_1,
                end_date=self.t_1,
            )

            output_base = (
                lp_conf.get("output_path")
                or self.config.get("detail_last_price_table_daily_output_path")
            )
            if output_base is None:
                bucket_name = self.config.get(
                    "bucket_name", "pluang-datalake-calculated-staging"
                )
                output_base = (
                    self.config.get(
                        "last_price_output_base",
                        f"s3a://{bucket_name}/processed_data/last_price/detail_last_price_table_daily/",
                    )
                )

            self.logger.info(f"Writing last_price_table_daily to: {output_base}")
            (
                result.write.mode("overwrite").partitionBy("day").parquet(output_base)
            )

        except Exception as e:
            self.logger.error(f"❌ Last price table build failed: {e}")
            import traceback
            traceback.print_exc()
            raise

    def run(self):
        try:
            self.execute()
        finally:
            if hasattr(self, "spark_utils"):
                self.spark_utils.stop_spark(self.spark)
        self.logger.info("🏁 detail_last_price_table_daily build completed")



