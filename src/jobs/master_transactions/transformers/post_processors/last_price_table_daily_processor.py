from typing import Optional

from pyspark.sql import DataFrame
from pyspark.sql import functions as F


def _split_key(idx: int):
    return F.split(F.col("join_unique_key"), "\\|").getItem(idx)


def build_detail_last_price_table_daily(
    app_price_df: DataFrame,
    lp_price_df: DataFrame,
    gss_lookup_df: Optional[DataFrame],
    start_date: str,
    end_date: str,
) -> DataFrame:
    """
    Replicates logic of dsa-dbt model core/DA_aggregate_published__detail_last_price_table_daily.sql
    using Spark DataFrames.

    Inputs are expected to already be normalized to the same schema as the dbt sources:
    - app_price_df: detail_last_price_table_app_daily
    - lp_price_df:  detail_last_price_table_hedging_daily
    - gss_lookup_df: pluang_global_stocks__global_stocks (optional, used for PALN leverage migration)
    """

    date_predicate = (F.col("day") >= F.lit(start_date)) & (F.col("day") <= F.lit(end_date))

    app_price = app_price_df.where(date_predicate)

    # PALN leverage migration append
    if gss_lookup_df is not None:
        paln_migration = (
            app_price_df.alias("a")
            .join(
                gss_lookup_df.alias("b"),
                (F.col("a.product") == F.col("b.company_code"))
                & (F.col("b.stock_type") == F.lit("CFD_LEVERAGE")),
                "inner",
            )
            .where(
                (F.col("a.asset_type") == "gss")
                & (F.col("a.asset_subtype") == "PALN")
                & (F.col("a.day") >= F.lit("2025-01-22"))
                & (F.col("a.day") <= F.lit(end_date))
            )
        )

        # Keep all original app_price columns except the ones replaced/derived
        passthrough_cols = [
            c
            for c in app_price_df.columns
            if c not in {"created", "day", "asset_type", "asset_subtype", "join_unique_key"}
        ]

        paln_migration = paln_migration.select(
            F.col("a.created").alias("created"),
            F.col("a.day").alias("day"),
            F.lit("gss_leverage").alias("asset_type"),
            F.lit("PALN_LEVERAGE").alias("asset_subtype"),
            *[F.col(f"a.{c}") for c in passthrough_cols],
            F.concat_ws(
                "|",
                F.lit("gss_leverage"),
                F.lit("PALN_LEVERAGE"),
                F.col("a.product"),
                F.col("b.id").cast("string"),
                F.coalesce(F.col("a.contract_name"), F.lit("")),
                F.coalesce(F.col("a.contract_id").cast("string"), F.lit("")),
                F.lit(""),
            ).alias("join_unique_key"),
        )

        app_price = app_price.unionByName(paln_migration, allowMissingColumns=True)

    lp_price = lp_price_df.where(date_predicate)

    # USDT margin wallet slice (crypto futures margin wallet)
    usdt_margin_wallet = (
        app_price.alias("a")
        .join(lp_price.alias("b"), on=["day", "join_unique_key"], how="full")
        .where(
            date_predicate
            & (F.col("a.asset_type") == "crypto")
            & (F.col("a.product") == "USDT")
        )
        .select(
            F.coalesce(F.col("a.created"), F.col("b.created")).alias("created"),
            F.col("day"),
            F.lit("crypto_futures").alias("asset_type"),
            F.lit("crypto_margin_wallet").alias("asset_subtype"),
            F.coalesce(F.col("a.product"), F.col("b.product")).alias("product"),
            F.coalesce(F.col("a.product_id"), F.col("b.product_id")).alias("product_id"),
            F.lit(None).cast("string").alias("contract_name"),
            F.lit(None).cast("long").alias("contract_id"),
            F.col("a.buy_back_price_usd").alias("buy_back_price_usd"),
            F.col("a.sell_price_usd").alias("sell_price_usd"),
            F.col("b.buy_back_price_usd").alias("hedging_buy_back_price_usd"),
            F.col("b.sell_price_usd").alias("hedging_sell_price_usd"),
            ((F.col("b.buy_back_price_usd") + F.col("b.sell_price_usd")) / 2).alias(
                "mid_price_usd"
            ),
            F.col("a.ref_id").alias("ref_id"),
            F.col("a.filled_from_last_value").alias("filled_from_last_value"),
            F.col("a.buy_back_price_idr").alias("buy_back_price_idr"),
            F.col("a.sell_price_idr").alias("sell_price_idr"),
            F.col("b.buy_back_price_idr").alias("hedging_buy_back_price_idr"),
            F.col("b.sell_price_idr").alias("hedging_sell_price_idr"),
            ((F.col("b.buy_back_price_idr") + F.col("b.sell_price_idr")) / 2).alias(
                "mid_price_idr"
            ),
            F.col("a.day_end_fx").alias("day_end_fx"),
            F.col("a.fx_ref_id").alias("fx_ref_id"),
            F.regexp_replace(
                F.col("join_unique_key"),
                "crypto\\|crypto",
                "crypto_futures|crypto_margin_wallet",
            ).alias("join_unique_key"),
        )
    )

    # General full-join block (non-fx, non-crypto_futures)
    general_block = (
        app_price.alias("a")
        .join(lp_price.alias("b"), on=["day", "join_unique_key"], how="full")
        .where(
            date_predicate
            & (F.coalesce(F.col("a.asset_type"), F.col("b.asset_type")) != "fx")
            & (F.coalesce(F.col("a.asset_type"), F.col("b.asset_type")) != "crypto_futures")
            & ~(
                (F.coalesce(F.col("a.product"), F.col("b.product")) == "STRAX")
                & (
                    F.coalesce(F.col("a.product_id"), F.col("b.product_id"))
                    == F.lit(10235)
                )
                & (F.coalesce(F.col("a.day"), F.col("b.day")) >= F.lit("2024-03-28"))
            )
        )
        .select(
            F.coalesce(F.col("a.created"), F.col("b.created")).alias("created"),
            F.col("day"),
            _split_key(0).alias("asset_type"),
            _split_key(1).alias("asset_subtype"),
            _split_key(2).alias("product"),
            _split_key(3).cast("long").alias("product_id"),
            F.nullif(_split_key(4), F.lit("")).alias("contract_name"),
            F.nullif(_split_key(5), F.lit(""))
            .cast("long")
            .alias("contract_id"),
            F.col("a.buy_back_price_usd").alias("buy_back_price_usd"),
            F.col("a.sell_price_usd").alias("sell_price_usd"),
            F.when(
                F.col("a.asset_subtype") == "PALN_LEVERAGE",
                F.col("a.buy_back_price_usd"),
            )
            .otherwise(F.col("b.buy_back_price_usd"))
            .alias("hedging_buy_back_price_usd"),
            F.col("b.sell_price_usd").alias("hedging_sell_price_usd"),
            F.when(
                _split_key(0).isin("gss", "crypto"),
                (F.col("b.buy_back_price_usd") + F.col("b.sell_price_usd")) / 2,
            )
            .when(
                _split_key(0).isin("gold", "idss", "stock_index", "fx", "mutual_fund"),
                (F.col("a.buy_back_price_usd") + F.col("a.sell_price_usd")) / 2,
            )
            .otherwise((F.col("a.buy_back_price_usd") + F.col("a.sell_price_usd")) / 2)
            .alias("mid_price_usd"),
            F.col("a.ref_id").alias("ref_id"),
            F.col("a.filled_from_last_value").alias("filled_from_last_value"),
            F.col("a.buy_back_price_idr").alias("buy_back_price_idr"),
            F.col("a.sell_price_idr").alias("sell_price_idr"),
            F.when(
                F.col("a.asset_subtype") == "PALN_LEVERAGE",
                F.col("a.buy_back_price_idr"),
            )
            .otherwise(F.col("b.buy_back_price_idr"))
            .alias("hedging_buy_back_price_idr"),
            F.col("b.sell_price_idr").alias("hedging_sell_price_idr"),
            F.when(
                _split_key(0).isin("gss", "crypto"),
                (F.col("b.buy_back_price_idr") + F.col("b.sell_price_idr")) / 2,
            )
            .when(
                _split_key(0).isin("gold", "idss", "stock_index", "fx", "mutual_fund"),
                (F.col("a.buy_back_price_idr") + F.col("a.sell_price_idr")) / 2,
            )
            .otherwise((F.col("a.buy_back_price_idr") + F.col("a.sell_price_idr")) / 2)
            .alias("mid_price_idr"),
            F.col("a.day_end_fx").alias("day_end_fx"),
            F.col("a.fx_ref_id").alias("fx_ref_id"),
            F.col("join_unique_key"),
        )
    )

    # FX JISDOR join
    fx_jisdor = (
        app_price.alias("a")
        .join(lp_price.alias("b"), on=["day", "asset_type", "product"], how="full")
        .where(
            date_predicate
            & (F.col("a.asset_type") == "fx")
            & (F.col("b.asset_type") == "fx")
            & (F.col("a.asset_subtype") == "fx")
            & (F.col("b.asset_subtype") == "JISDOR")
        )
        .select(
            F.coalesce(F.col("a.created"), F.col("b.created")).alias("created"),
            F.col("day"),
            F.col("asset_type"),
            F.lit("JISDOR").alias("asset_subtype"),
            F.col("product"),
            F.lit(10001).alias("product_id"),
            F.lit(None).cast("string").alias("contract_name"),
            F.lit(None).cast("long").alias("contract_id"),
            F.col("a.buy_back_price_usd").alias("buy_back_price_usd"),
            F.col("a.sell_price_usd").alias("sell_price_usd"),
            F.col("b.buy_back_price_usd").alias("hedging_buy_back_price_usd"),
            F.col("b.sell_price_usd").alias("hedging_sell_price_usd"),
            ((F.col("a.buy_back_price_usd") + F.col("a.sell_price_usd")) / 2).alias(
                "mid_price_usd"
            ),
            F.col("a.ref_id").alias("ref_id"),
            F.col("a.filled_from_last_value").alias("filled_from_last_value"),
            F.col("a.buy_back_price_idr").alias("buy_back_price_idr"),
            F.col("a.sell_price_idr").alias("sell_price_idr"),
            F.col("b.buy_back_price_idr").alias("hedging_buy_back_price_idr"),
            F.col("b.sell_price_idr").alias("hedging_sell_price_idr"),
            ((F.col("a.buy_back_price_idr") + F.col("a.sell_price_idr")) / 2).alias(
                "mid_price_idr"
            ),
            F.col("a.day_end_fx").alias("day_end_fx"),
            F.col("a.fx_ref_id").alias("fx_ref_id"),
            F.col("b.join_unique_key").alias("join_unique_key"),
        )
    )

    # FX other join
    fx_other = (
        app_price.alias("a")
        .join(lp_price.alias("b"), on=["day", "asset_type", "product"], how="full")
        .where(
            date_predicate
            & (F.col("a.asset_type") == "fx")
            & (F.col("b.asset_type") == "fx")
            & (F.col("b.asset_subtype") != "JISDOR")
        )
        .select(
            F.coalesce(F.col("a.created"), F.col("b.created")).alias("created"),
            F.col("day"),
            F.col("asset_type"),
            F.col("a.asset_subtype").alias("asset_subtype"),
            F.col("product"),
            F.col("a.product_id").alias("product_id"),
            F.lit(None).cast("string").alias("contract_name"),
            F.lit(None).cast("long").alias("contract_id"),
            F.col("a.buy_back_price_usd").alias("buy_back_price_usd"),
            F.col("a.sell_price_usd").alias("sell_price_usd"),
            F.col("b.buy_back_price_usd").alias("hedging_buy_back_price_usd"),
            F.col("b.sell_price_usd").alias("hedging_sell_price_usd"),
            ((F.col("a.buy_back_price_usd") + F.col("a.sell_price_usd")) / 2).alias(
                "mid_price_usd"
            ),
            F.col("a.ref_id").alias("ref_id"),
            F.col("a.filled_from_last_value").alias("filled_from_last_value"),
            F.col("a.buy_back_price_idr").alias("buy_back_price_idr"),
            F.col("a.sell_price_idr").alias("sell_price_idr"),
            F.col("b.buy_back_price_idr").alias("hedging_buy_back_price_idr"),
            F.col("b.sell_price_idr").alias("hedging_sell_price_idr"),
            ((F.col("a.buy_back_price_idr") + F.col("a.sell_price_idr")) / 2).alias(
                "mid_price_idr"
            ),
            F.col("a.day_end_fx").alias("day_end_fx"),
            F.col("a.fx_ref_id").alias("fx_ref_id"),
            F.col("a.join_unique_key").alias("join_unique_key"),
        )
    )

    # Crypto futures USD→IDR using USDT margin wallet rate
    crypto_fut_usd_idr = (
        app_price.alias("a")
        .join(lp_price.alias("b"), on=["day", "join_unique_key"], how="full")
        .join(
            usdt_margin_wallet.select("day", "mid_price_idr", "day_end_fx").alias("u"),
            on=["day"],
            how="inner",
        )
        .where(date_predicate & (F.col("a.asset_type") == "crypto_futures"))
        .select(
            F.coalesce(F.col("a.created"), F.col("b.created")).alias("created"),
            F.col("day"),
            _split_key(0).alias("asset_type"),
            _split_key(1).alias("asset_subtype"),
            _split_key(2).alias("product"),
            _split_key(3).cast("long").alias("product_id"),
            F.nullif(_split_key(4), F.lit("")).alias("contract_name"),
            F.nullif(_split_key(5), F.lit(""))
            .cast("long")
            .alias("contract_id"),
            (F.col("a.buy_back_price_usd") * F.col("u.mid_price_idr") / F.col("u.day_end_fx")).alias(
                "buy_back_price_usd"
            ),
            (F.col("a.sell_price_usd") * F.col("u.mid_price_idr") / F.col("u.day_end_fx")).alias(
                "sell_price_usd"
            ),
            (F.col("b.buy_back_price_usd") * F.col("u.mid_price_idr") / F.col("u.day_end_fx")).alias(
                "hedging_buy_back_price_usd"
            ),
            (F.col("b.sell_price_usd") * F.col("u.mid_price_idr") / F.col("u.day_end_fx")).alias(
                "hedging_sell_price_usd"
            ),
            ((F.col("b.buy_back_price_usd") + F.col("b.sell_price_usd")) / 2 * F.col("u.mid_price_idr") / F.col("u.day_end_fx")).alias(
                "mid_price_usd"
            ),
            F.col("a.ref_id").alias("ref_id"),
            F.col("a.filled_from_last_value").alias("filled_from_last_value"),
            (F.col("a.buy_back_price_usd") * F.col("u.mid_price_idr")).alias(
                "buy_back_price_idr"
            ),
            (F.col("a.sell_price_usd") * F.col("u.mid_price_idr")).alias(
                "sell_price_idr"
            ),
            (F.col("b.buy_back_price_usd") * F.col("u.mid_price_idr")).alias(
                "hedging_buy_back_price_idr"
            ),
            (F.col("b.sell_price_usd") * F.col("u.mid_price_idr")).alias(
                "hedging_sell_price_idr"
            ),
            ((F.col("b.buy_back_price_usd") + F.col("b.sell_price_usd")) / 2 * F.col("u.mid_price_idr")).alias(
                "mid_price_idr"
            ),
            F.col("a.day_end_fx").alias("day_end_fx"),
            F.col("a.fx_ref_id").alias("fx_ref_id"),
            F.col("join_unique_key"),
        )
    )

    result = (
        general_block.unionByName(fx_jisdor, allowMissingColumns=True)
        .unionByName(fx_other, allowMissingColumns=True)
        .unionByName(usdt_margin_wallet, allowMissingColumns=True)
        .unionByName(crypto_fut_usd_idr, allowMissingColumns=True)
    )

    return result


