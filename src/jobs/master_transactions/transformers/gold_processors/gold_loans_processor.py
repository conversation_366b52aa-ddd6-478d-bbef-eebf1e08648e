"""
Gold Loans Processor
Builds gold loan-related records in alignment with the SQL mapping:
 - Loan installments (loan_firstinstallment / loan_installment / pending / reject / expire)
 - First installment paid-off where installment_payments is missing
"""

from typing import Any, Dict, Optional
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col, lit, when, coalesce, row_number, lead, current_timestamp, lower, concat
)
from pyspark.sql.window import Window

from src.utils.custom_logger import get_logger
from src.utils.io_utils import IOUtils


class GoldLoansProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.io_utils = IOUtils(self.spark, self.config)
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.transactions_snapshot_path = self.config.get(
            "transactions_snapshot_path",
            "s3a://pluang-datalake-calculated-staging/raw_data/",
        )

    def _load_spreads(self) -> Optional[DataFrame]:
        partner_path = f"{self.transactions_snapshot_path}partner_gold_prices/dt={self.t_1}/"
        master_path = f"{self.transactions_snapshot_path}master_gold_prices/dt={self.t_1}/"
        pgp = self.io_utils.read_json_data(partner_path, True, None, False)
        if pgp is None:
            self.logger.warning("⚠️  No partner gold prices found for loans spreads")
            return None
        mgp = self.io_utils.read_json_data(master_path, True, None, False)
        partner = pgp.alias("pgp")
        if mgp is not None:
            joined = partner.join(mgp.alias("mgp"), col("pgp.master_gold_price_id")==col("mgp.id"), "left")
        else:
            joined = partner
        mgp_mid = when(
            col("mgp.buy_back_price").isNotNull() & col("mgp.sell_price").isNotNull(),
            (col("mgp.buy_back_price") + col("mgp.sell_price")) / 2,
        ).otherwise((col("pgp.buy_back_price") + col("pgp.sell_price")) / 2)
        spreads = joined.select(
            col("pgp.created").alias("created"),
            col("pgp.partner_id").alias("partner_id"),
            ((col("pgp.sell_price") - col("pgp.buy_back_price")) / col("pgp.sell_price")).alias("spread"),
            ((mgp_mid - col("pgp.buy_back_price")) / col("pgp.sell_price")).alias("buy_spread"),
            ((col("pgp.sell_price") - mgp_mid) / col("pgp.sell_price")).alias("sell_spread"),
        )
        w = Window.partitionBy("partner_id").orderBy(col("created").asc())
        spreads = spreads.withColumn("next_created", coalesce(lead("created").over(w), current_timestamp()))
        dw = Window.partitionBy("created", "partner_id").orderBy(col("created").asc())
        spreads = spreads.withColumn("rn", row_number().over(dw)).filter(col("rn")==1).drop("rn")
        return spreads

    def _read_loans(self) -> Optional[DataFrame]:
        path = f"{self.transactions_snapshot_path}gold_loans/dt={self.t_1}/"
        df = self.io_utils.read_json_data(path, True, None, False)
        if df is None:
            self.logger.warning("⚠️  No gold_loans found")
        return df

    def _read_installments(self) -> Optional[DataFrame]:
        path = f"{self.transactions_snapshot_path}installment_payments/dt={self.t_1}/"
        try:
            df = self.io_utils.read_json_data(path, True, None, False)
            if df is None:
                self.logger.warning("⚠️  No installment_payments found")
            return df
        except Exception:
            self.logger.warning("⚠️  installment_payments path missing; proceeding without payments")
            return None

    def _build_main_installments(self, loans: DataFrame, pays: DataFrame, spreads: Optional[DataFrame]) -> DataFrame:
        # a = installment_payments, b = gold_loans
        a = pays.alias("a")
        b = loans.alias("b")
        df = a.join(b, col("a.gold_loan_id")==col("b.id"), "right")
        if spreads is not None:
            s = spreads.alias("s")
            df = df.join(
                s,
                (col("b.created") > col("s.created")) & (col("b.created") <= col("s.next_created")) & (col("b.partner_id") == col("s.partner_id")),
                "left",
            )

        # rn is installment_index from payments
        df = df.withColumn("rn", col("a.installment_index").cast("int"))

        # Dedupe loan_installment rows: keep 1 row per (loan_id, rn, transaction_time)
        df = df.withColumn("key_ts", coalesce(col("a.transaction_time"), col("b.transaction_time")))
        dedupe_w = Window.partitionBy(col("b.id"), col("rn"), col("key_ts")).orderBy(col("a.updated").desc_nulls_last(), col("a.created").desc_nulls_last())
        df = df.withColumn("_d_rn", row_number().over(dedupe_w))
        df = df.filter((col("rn").isNull()) | (col("rn") <= 1) | ((col("rn") > 1) & (col("_d_rn") == 1)))

        # Select mapped columns per SQL
        result = df.select(
            col("b.user_id").alias("user_id"),
            col("b.account_id").alias("account_id"),
            lit("gold").alias("asset_type"),
            lit("gold").alias("product"),
            lit(0).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("gold").alias("asset_subtype"),
            when(col("rn").isNull() & (lower(col("b.status"))=="rejected"), lit("loan_reject"))
            .when(col("rn").isNull() & (lower(col("b.status"))=="expired"), lit("loan_expire"))
            .when(col("rn") == 1, lit("loan_firstinstallment"))
            .when(col("rn") > 1, lit("loan_installment"))
            .otherwise(concat(lit("loan_pending_"), col("b.status"))).alias("activity"),
            when(col("rn") == 1, col("b.gold_loan_amount"))
            .cast("string").alias("quantity"),
            when(col("b.status").isin("PAID_OFF","ACCEPTED","CANCELLED"), when(col("rn") == 1, col("b.gold_loan_amount")))
            .cast("string").alias("net_quantity"),
            coalesce(col("a.date_paid_on"), col("b.updated")).alias("created"),
            col("b.status").alias("status"),
            lit("BUY").alias("transaction_type"),
            col("b.partner_id").alias("partner_id"),
            col("b.client_id").alias("client_id"),
            lit("IDR").alias("currency"),
            col("b.issued_price").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            when(col("rn") == 1, col("b.down_payment").cast("double") + col("b.total_principal").cast("double")).alias("gtv"),
            lit(None).alias("gtv_usd"),
            when(col("b.status").isin("PAID_OFF","ACCEPTED","CANCELLED"), when(col("rn") == 1, col("b.down_payment").cast("double") + col("b.total_principal").cast("double"))).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            when(col("b.status").isin("PAID_OFF","ACCEPTED","CANCELLED"), when(col("rn") == 1, (col("b.total_principal").cast("double") + col("b.down_payment").cast("double")) * coalesce(col("s.sell_spread"), lit(0.0))))
            .alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            when(col("b.status").isin("PAID_OFF","ACCEPTED","CANCELLED"), when(col("rn") == 1, col("b.total_installment").cast("double") - col("b.total_principal").cast("double"))).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("b.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            when(col("rn") >= 1, col("a.commission_amount")).alias("partner_commission"),
            when(col("rn") >= 1, col("a.commission_percent")).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            coalesce(col("a.transaction_time"), col("b.transaction_time")).alias("transaction_time"),
        )
        return result

    def _build_paid_off_without_payments(self, loans: DataFrame, spreads: Optional[DataFrame]) -> DataFrame:
        # gl is loans; no installment_payments present for that loan
        gl = loans.alias("gl")
        df = gl
        if spreads is not None:
            s = spreads.alias("s")
            df = df.join(
                s,
                (col("gl.created") > col("s.created")) & (col("gl.created") <= col("s.next_created")) & (col("gl.partner_id") == col("s.partner_id")),
                "left",
            )
        # Select mapping for first installment paid-off
        result = df.select(
            col("gl.user_id").alias("user_id"),
            col("gl.account_id").alias("account_id"),
            lit("gold").alias("asset_type"),
            lit("gold").alias("product"),
            lit(0).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("gold").alias("asset_subtype"),
            lit("loan_firstinstallment").alias("activity"),
            col("gl.gold_loan_amount").alias("quantity"),
            col("gl.gold_loan_amount").alias("net_quantity"),
            coalesce(col("gl.created"), col("gl.updated")).alias("created"),
            col("gl.status").alias("status"),
            lit("BUY").alias("transaction_type"),
            col("gl.partner_id").alias("partner_id"),
            col("gl.client_id").alias("client_id"),
            lit("IDR").alias("currency"),
            col("gl.issued_price").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            (col("gl.down_payment") + col("gl.total_principal")).alias("gtv"),
            lit(None).alias("gtv_usd"),
            (col("gl.down_payment") + col("gl.total_principal")).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            ((col("gl.total_principal") + col("gl.down_payment")) * coalesce(col("s.sell_spread"), lit(0))).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            (col("gl.total_installment") - col("gl.total_principal")).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("gl.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(0).alias("partner_commission"),
            lit(0).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("gl.transaction_time").alias("transaction_time"),
        )
        return result

    def _build_loan_apply(self, loans: DataFrame) -> DataFrame:
        b = loans.alias("b")
        result = b.select(
            col("b.user_id").alias("user_id"),
            col("b.account_id").alias("account_id"),
            lit("gold").alias("asset_type"),
            lit("gold").alias("product"),
            lit(0).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("gold").alias("asset_subtype"),
            lit("loan_apply").alias("activity"),
            col("b.gold_loan_amount").cast("string").alias("quantity"),
            (col("b.gold_loan_amount") * lit(-1)).cast("string").alias("net_quantity"),
            col("b.created").alias("created"),
            lit(None).cast("string").alias("status"),
            lit("BUY").alias("transaction_type"),
            col("b.partner_id").alias("partner_id"),
            col("b.client_id").alias("client_id"),
            lit("IDR").alias("currency"),
            col("b.issued_price").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            lit(None).alias("gtv"),
            lit(None).alias("gtv_usd"),
            lit(None).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("b.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).cast("string").alias("partner_commission"),
            lit(None).cast("string").alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("b.transaction_time").alias("transaction_time"),
        )
        return result

    def _build_loan_cancel(self, loans: DataFrame, spreads: Optional[DataFrame]) -> DataFrame:
        b = loans.alias("b")
        df = b
        if spreads is not None:
            s = spreads.alias("s")
            df = df.join(
                s,
                (col("b.created") > col("s.created")) & (col("b.created") <= col("s.next_created")) & (col("b.partner_id") == col("s.partner_id")),
                "left",
            )
        # per DA_aggregate_published__detail_all_transactions_daily.sql, loan_cancel applies only to CANCELLED loans
        df = df.filter(col("b.status") == "CANCELLED")

        basic = (col("b.total_installment").cast("double") - col("b.total_principal").cast("double"))
        penalty_pre_oct = (
            when(
                col("b.cash_back").cast("double") - lit(0.6) * basic / col("b.tenure").cast("double") * col("b.installment_index").cast("double") > 0,
                col("b.fine").cast("double")
            ).otherwise(
                when(
                    col("b.fine").cast("double") + col("b.cash_back").cast("double") - lit(0.6) * basic / col("b.tenure").cast("double") * col("b.installment_index").cast("double") > 0,
                    col("b.fine").cast("double") + col("b.cash_back").cast("double") - lit(0.6) * basic / col("b.tenure").cast("double") * col("b.installment_index").cast("double")
                ).otherwise(lit(0.0))
            )
        )
        penalty_post = when(col("b.cash_back").cast("double") > 0, col("b.fine").cast("double")).otherwise(
            when(col("b.fine").cast("double") + col("b.cash_back").cast("double") > 0, col("b.fine").cast("double") + col("b.cash_back").cast("double")).otherwise(lit(0.0))
        )

        result = df.select(
            col("b.user_id").alias("user_id"),
            col("b.account_id").alias("account_id"),
            lit("gold").alias("asset_type"),
            lit("gold").alias("product"),
            lit(0).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("gold").alias("asset_subtype"),
            lit("loan_cancel").alias("activity"),
            col("b.gold_loan_amount").cast("string").alias("quantity"),
            (col("b.gold_loan_amount") * lit(-1)).cast("string").alias("net_quantity"),
            coalesce(col("b.fine_pay_date"), col("b.updated")).alias("created"),
            col("b.status").alias("status"),
            lit("SELL").alias("transaction_type"),
            col("b.partner_id").alias("partner_id"),
            col("b.client_id").alias("client_id"),
            lit("IDR").alias("currency"),
            col("b.issued_price").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            lit(None).alias("gtv"),
            lit(None).alias("gtv_usd"),
            ((col("b.total_principal").cast("double") + col("b.down_payment").cast("double")) * lit(-1)).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            (((col("b.total_principal").cast("double") + col("b.down_payment").cast("double")) * lit(-1)) * coalesce(col("s.sell_spread"), lit(0.0))).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            when(col("b.partner_id") == lit("1000001"), -lit(0.4) * basic).otherwise(-basic).alias("installment_revenue"),
            when(col("b.partner_id") == lit("1000001"), col("b.down_payment").cast("double")).otherwise(lit(None)).alias("downpayment_revenue"),
            when(
                (col("b.partner_id") == lit("1000001")) & (col("b.updated").substr(1,10) < lit("2021-10-06")),
                penalty_pre_oct
            ).otherwise(penalty_post).alias("penalty_revenue"),
            col("b.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).cast("string").alias("partner_commission"),
            lit(None).cast("string").alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("b.transaction_time").alias("transaction_time"),
        )
        return result

    def process(self) -> Optional[DataFrame]:
        spreads = self._load_spreads()
        loans = self._read_loans()
        pays = self._read_installments()
        if loans is None:
            return None
        unions: Optional[DataFrame] = None
        if pays is not None:
            main_df = self._build_main_installments(loans, pays, spreads)
            unions = main_df if unions is None else unions.unionByName(main_df, allowMissingColumns=True)
        # First-installment paid-off without payment rows (subset): filter loans status PAID_OFF only
        paid_off = loans.filter(col("status") == "PAID_OFF")
        if paid_off is not None:
            first_df = self._build_paid_off_without_payments(paid_off, spreads)
            unions = first_df if unions is None else unions.unionByName(first_df, allowMissingColumns=True)
        # Loan apply and cancel
        apply_df = self._build_loan_apply(loans)
        unions = apply_df if unions is None else unions.unionByName(apply_df, allowMissingColumns=True)
        cancel_df = self._build_loan_cancel(loans, spreads)
        unions = cancel_df if unions is None else unions.unionByName(cancel_df, allowMissingColumns=True)
        return unions


