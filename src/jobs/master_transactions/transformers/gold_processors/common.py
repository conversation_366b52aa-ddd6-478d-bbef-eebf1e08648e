from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col

from src.utils.custom_logger import get_logger
from src.utils.io_utils import IOUtils


class CommonGoldUtils:
    """
    Shared helpers for gold processors (paths, reads, simple joins).
    Non-opinionated: keeps existing processors' logic intact while centralizing IO helpers.
    """

    def __init__(self, spark: SparkSession, config: Dict[str, Any]):
        self.logger = get_logger()
        self.spark = spark
        self.config = config
        self.io_utils = IOUtils(self.spark, self.config)
        self.transactions_snapshot_path = self.config.get(
            "transactions_snapshot_path",
            "s3a://pluang-datalake-calculated-staging/raw_data/",
        )
        gp_cfg = self.config.get("gold_prices", {})
        self.partner_gold_prices_path = gp_cfg.get("partner_gold_prices_path", "partner_gold_prices/")
        self.master_gold_prices_path = gp_cfg.get("master_gold_prices_path", "master_gold_prices/")

    def read_json(self, table: str, dt: str) -> Optional[DataFrame]:
        path = f"{self.transactions_snapshot_path}{table}/dt={dt}/"
        try:
            df = self.io_utils.read_json_data(path, True, None, False)
            if df is None:
                self.logger.warning(f"⚠️  No {table} found for dt={dt}")
            return df
        except Exception:
            self.logger.info(f"No {table} path found for dt={dt}; skipping")
            return None

    def read_partner_gold_prices(self, dt: str) -> Optional[DataFrame]:
        path = f"{self.transactions_snapshot_path}{self.partner_gold_prices_path}dt={dt}/"
        try:
            return self.io_utils.read_json_data(path, True, None, False)
        except Exception:
            self.logger.info("No partner_gold_prices path found; skipping")
            return None

    def read_master_gold_prices(self, dt: str) -> Optional[DataFrame]:
        path = f"{self.transactions_snapshot_path}{self.master_gold_prices_path}dt={dt}/"
        try:
            return self.io_utils.read_json_data(path, True, None, False)
        except Exception:
            self.logger.info("No master_gold_prices path found; skipping")
            return None

    def left_join(self, left: DataFrame, right: Optional[DataFrame], on_expr, alias_left: str = "a", alias_right: str = "b") -> DataFrame:
        if right is None:
            return left
        return left.alias(alias_left).join(right.alias(alias_right), on_expr, "left")


