from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit

from src.utils.custom_logger import get_logger
from src.utils.io_utils import IOUtils


class GoldDormantFeesProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.io_utils = IOUtils(self.spark, self.config)
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.transactions_snapshot_path = self.config.get(
            "transactions_snapshot_path",
            "s3a://pluang-datalake-calculated-staging/raw_data/",
        )

    def _read_dormant_fees(self) -> Optional[DataFrame]:
        path = f"{self.transactions_snapshot_path}gold_dormant_fees/dt={self.t_1}/"
        try:
            df = self.io_utils.read_json_data(path, True, None, False)
            if df is None:
                self.logger.info("No gold_dormant_fees found; skipping")
                return None
            return df
        except Exception:
            self.logger.info("No gold_dormant_fees path found; skipping")
            return None

    def process(self) -> Optional[DataFrame]:
        df = self._read_dormant_fees()
        if df is None:
            return None

        # Map fields per SQL split: reference/split_sqls/gold_transactions.sql (Gold Dormant Fees section)
        result = df.select(
            col("user_id").alias("user_id"),
            col("account_id").alias("account_id"),
            lit("gold").alias("asset_type"),
            lit("gold").alias("product"),
            lit(0).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("gold").alias("asset_subtype"),
            lit("gold_dormant_fee").alias("activity"),
            col("quantity").cast("string").alias("quantity"),
            (col("quantity").cast("double") * lit(-1)).cast("string").alias("net_quantity"),
            col("transaction_time").alias("created"),
            lit("SUCCESS").alias("status"),
            lit("FEES").alias("transaction_type"),
            lit("1000002").alias("partner_id"),
            lit("3").alias("client_id"),
            lit("IDR").alias("currency"),
            col("unit_price").cast("string").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            col("total_amount").cast("double").alias("gtv"),
            lit(None).alias("gtv_usd"),
            (col("total_amount").cast("double") * lit(-1)).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("id").cast("string").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).cast("string").alias("partner_commission"),
            lit(None).cast("string").alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("transaction_time").alias("transaction_time"),
        )

        return result


