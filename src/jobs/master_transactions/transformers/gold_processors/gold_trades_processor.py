"""
Gold Trades Processor (BUY/SELL)
Refactored from gold_processor.py
"""

from typing import Dict, Any, Optional
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col, lit, when, coalesce, cast, expr, row_number, lead, current_timestamp, desc
)
from pyspark.sql.window import Window

from src.utils.custom_logger import get_logger
from src.utils.io_utils import IOUtils
from src.utils.operations import Operations


class GoldProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.io_utils = IOUtils(self.spark, self.config)
        self.ops = Operations(self.spark)
        self.transactions_snapshot_path = self.config.get(
            "transactions_snapshot_path",
            "s3a://pluang-datalake-calculated-staging/raw_data/",
        )
        self.logger.info("✅ GoldProcessor initialized")

    def _load_gold_spreads(self) -> Optional[DataFrame]:
        try:
            self.logger.info("Loading gold spread data...")
            partner_prices_path = f"{self.transactions_snapshot_path}partner_gold_prices/dt={self.t_1}/"
            pgp_df = self.io_utils.read_json_data(partner_prices_path, True, None, False)
            if pgp_df is None:
                self.logger.warning("⚠️  No partner gold prices found")
                return None

            master_prices_path = f"{self.transactions_snapshot_path}master_gold_prices/dt={self.t_1}/"
            mgp_df = self.io_utils.read_json_data(master_prices_path, True, None, False)
            if mgp_df is None:
                self.logger.warning("⚠️  No master gold prices found, will use partner prices only")

            partner_prices = pgp_df.alias("pgp")
            if mgp_df is not None:
                joined_df = partner_prices.join(
                    mgp_df.alias("mgp"),
                    col("pgp.master_gold_price_id") == col("mgp.id"),
                    "left",
                )
            else:
                joined_df = partner_prices

            mgp_mid_price_expr = when(
                col("mgp.buy_back_price").isNotNull() & col("mgp.sell_price").isNotNull(),
                (col("mgp.buy_back_price") + col("mgp.sell_price")) / 2,
            ).otherwise((col("pgp.buy_back_price") + col("pgp.sell_price")) / 2)

            spread_df = joined_df.select(
                col("pgp.created"),
                col("pgp.partner_id"),
                col("pgp.buy_back_price"),
                col("pgp.sell_price"),
                ((col("pgp.sell_price") - col("pgp.buy_back_price")) / col("pgp.sell_price")).alias("spread"),
                mgp_mid_price_expr.alias("mgp_mid_price"),
                ((col("pgp.buy_back_price") + col("pgp.sell_price")) / 2).alias("pgp_mid_price"),
                ((mgp_mid_price_expr - col("pgp.buy_back_price")) / col("pgp.sell_price")).alias("buy_spread"),
                ((col("pgp.sell_price") - mgp_mid_price_expr) / col("pgp.sell_price")).alias("sell_spread"),
            )

            window_spec = Window.partitionBy("partner_id").orderBy(col("created").asc())
            spread_df = spread_df.withColumn(
                "next_created", coalesce(lead("created").over(window_spec), current_timestamp())
            )

            dedup_window = Window.partitionBy("created", "partner_id").orderBy(col("created").asc())
            spread_df = (
                spread_df.withColumn("rn", row_number().over(dedup_window)).filter(col("rn") == 1).drop("rn")
            )
            self.logger.info(f"✅ Loaded {spread_df.count()} gold spread records")
            return spread_df
        except Exception as e:
            self.logger.error(f"❌ Error loading gold spreads: {str(e)}")
            return None

    def process_transactions(self) -> Optional[DataFrame]:
        try:
            self.logger.info("Loading gold transactions (BUY/SELL)...")
            transactions_path = f"{self.transactions_snapshot_path}gold_transactions/dt={self.t_1}/"
            gt_df = self.io_utils.read_json_data(transactions_path, True, None, False)
            if gt_df is None:
                self.logger.warning("⚠️  No gold transactions found")
                return None

            self.logger.info(f"Raw gold transactions: {gt_df.count()} records (no dedup)")

            gold_spreads_df = self._load_gold_spreads()
            if gold_spreads_df is not None:
                gt_df = gt_df.alias("gt")
                gold_spreads_df = gold_spreads_df.alias("gs")
                gt_df = gt_df.join(
                    gold_spreads_df,
                    (col("gt.created") > col("gs.created"))
                    & (col("gt.created") <= col("gs.next_created"))
                    & (col("gt.partner_id") == col("gs.partner_id")),
                    "left",
                )

            result_df = gt_df.select(
                col("gt.user_id").alias("user_id"),
                col("gt.account_id").alias("account_id"),
                lit("gold").alias("asset_type"),
                lit("gold").alias("product"),
                lit(0).alias("product_id"),
                lit(None).cast("string").alias("contract_id"),
                lit(None).cast("string").alias("contract_name"),
                lit("gold").alias("asset_subtype"),
                lit("transaction").alias("activity"),
                col("gt.quantity").alias("quantity"),
                when(col("gt.status") == "SUCCESS",
                     when(col("gt.transaction_type") == "BUY", col("gt.quantity"))
                     .otherwise(-col("gt.quantity"))
                ).otherwise(lit(None)).alias("net_quantity"),
                coalesce(col("gt.transaction_time"), col("gt.created")).alias("created"),
                col("gt.status").alias("status"),
                col("gt.transaction_type").alias("transaction_type"),
                col("gt.partner_id").alias("partner_id"),
                col("gt.client_id").alias("client_id"),
                lit("IDR").alias("currency"),
                col("gt.unit_price").alias("unit_price"),
                lit(None).alias("unit_price_usd"),
                lit(None).alias("usdt_mid_price"),
                lit(None).alias("underlying_asset_price"),
                when(col("gt.transaction_type") == "BUY",
                     col("gt.final_amount").cast("double") - col("gt.fees").cast("double"))
                .otherwise(col("gt.final_amount").cast("double") + col("gt.fees").cast("double")).alias("gtv"),
                lit(None).alias("gtv_usd"),
                when(col("gt.status") == "SUCCESS",
                     when(col("gt.transaction_type") == "BUY",
                          col("gt.final_amount").cast("double") - col("gt.fees").cast("double"))
                     .otherwise(-(col("gt.final_amount").cast("double") + col("gt.fees").cast("double")))
                ).otherwise(lit(None)).alias("net_gtv"),
                lit(None).alias("trading_margin_usd"),
                lit(None).alias("net_trading_margin_usd"),
                lit(None).alias("external_fees"),
                lit(None).alias("overnight_fee_revenue"),
                lit(None).alias("overnight_fee_revenue_usd"),
                when(col("gt.status") == "SUCCESS",
                     when(col("gt.transaction_type") == "BUY",
                          col("gt.unit_price").cast("double") * col("gt.quantity").cast("double") * coalesce(col("gs.sell_spread"), lit(0)))
                     .when(col("gt.transaction_type") == "SELL",
                           (col("gt.unit_price").cast("double") * col("gt.quantity").cast("double")) / (1 - coalesce(col("gs.spread"), lit(0.01))) * coalesce(col("gs.buy_spread"), lit(0)))
                ).otherwise(lit(None)).alias("spread_revenue"),
                lit(None).alias("fee_revenue"),
                lit(None).alias("commission_revenue"),
                coalesce(expr("cast(get_json_object(gt.transaction_fee_info, '$[0].amount') as double)"), lit(0)).alias("exchange_fee_revenue"),
                lit(None).alias("reg_taf_revenue"),
                lit(None).alias("promo_cost"),
                coalesce(expr("cast(get_json_object(gt.transaction_fee_info, '$[1].amount') as double)"), lit(0)).alias("tax_revenue"),
                lit(None).alias("spread_cost"),
                when(col("gt.status") == "SUCCESS",
                     col("gt.final_amount").cast("double") - (col("gt.unit_price").cast("double") * col("gt.quantity").cast("double"))
                ).otherwise(lit(None)).alias("rounding_revenue"),
                lit(None).alias("fx_spread_revenue"),
                lit(None).alias("installment_revenue"),
                lit(None).alias("downpayment_revenue"),
                lit(None).alias("penalty_revenue"),
                col("gt.id").alias("ref_id"),
                lit(None).cast("string").alias("recurring_transaction_id"),
                lit(None).cast("string").alias("user_pocket_id"),
                lit(None).cast("string").alias("idr2usd_ref_id"),
                lit(None).cast("string").alias("usd2usdmargin_ref_id"),
                lit(None).cast("string").alias("usdmargin2usd_ref_id"),
                lit(None).alias("prorated_hedge_pnl"),
                lit(None).alias("prorated_hedge_pnl_usd"),
                col("gt.commission_amount").alias("partner_commission"),
                col("gt.commission_percent").alias("partner_commission_pct"),
                lit(None).alias("broker_fee"),
                lit(None).alias("broker_fee_tax"),
                lit(None).alias("market_maker_fee"),
                lit(None).alias("market_maker_fee_tax"),
                lit(None).cast("string").alias("ref_id_hedge"),
                lit(None).cast("string").alias("network"),
                col("gt.transaction_fee_info").alias("taxes_and_fees"),
                lit(None).alias("realised_gain"),
                lit(None).alias("realised_gain_native"),
                lit(None).cast("boolean").alias("is_liquidation"),
                col("gt.transaction_time").alias("transaction_time"),
            )

            self.logger.info(f"✅ Processed {result_df.count()} gold transaction records")
            return result_df
        except Exception as e:
            import traceback
            self.logger.error(f"❌ Error processing gold transactions: {str(e)}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None


