"""
Gold Withdrawals Processor
Builds gold withdrawal request/cancel records to align with master transactions schema.
"""

from typing import Any, Dict, Optional
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col,
    lit,
    when,
    coalesce,
    row_number,
)
from pyspark.sql.window import Window

from src.utils.custom_logger import get_logger
from src.utils.io_utils import IOUtils


class GoldWithdrawalsProcessor:
    """
    Processes gold withdrawals from the snapshot path:
      - withdrawal_request rows
      - withdrawal_cancel rows (including EXPIRED/REJECTED)
    """

    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.io_utils = IOUtils(self.spark, self.config)
        self.t_1 = t_1
        self.h_1 = h_1 if h_1 else t_1
        self.transactions_snapshot_path = self.config.get(
            "transactions_snapshot_path",
            "s3a://pluang-datalake-calculated-staging/raw_data/",
        )

    def _read_and_deduplicate(self) -> Optional[DataFrame]:
        """
        Read raw withdrawals json (no deduplication).
        """
        path = f"{self.transactions_snapshot_path}gold_withdrawals/dt={self.t_1}/"
        df = self.io_utils.read_json_data(path, True, None, False)
        if df is None:
            self.logger.warning("⚠️  No gold withdrawals found")
            return None
        self.logger.info(f"Raw gold withdrawals: {df.count()} records (no dedup)")
        return df

    def _build_requests(self, base_df: DataFrame) -> DataFrame:
        """
        Build withdrawal_request rows (mirrors SQL mapping).
        """
        unit_price_col = coalesce(col("unit_price"), col("sell_price")).cast("double")
        net_amount_col = col("net_amount").cast("double")

        return base_df.select(
            col("user_id").alias("user_id"),
            col("account_id").alias("account_id"),
            lit("gold").alias("asset_type"),
            lit("gold").alias("product"),
            lit(0).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("gold").alias("asset_subtype"),
            lit("withdrawal_request").alias("activity"),
            net_amount_col.cast("string").alias("quantity"),
            (net_amount_col * lit(-1)).cast("string").alias("net_quantity"),
            coalesce(col("transaction_time"), col("created")).alias("created"),
            col("status").alias("status"),
            lit("withdrawal").alias("transaction_type"),
            lit("1000002").alias("partner_id"),
            col("client_id").alias("client_id"),
            lit("IDR").alias("currency"),
            unit_price_col.cast("string").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            lit(None).alias("gtv"),
            lit(None).alias("gtv_usd"),
            (unit_price_col * lit(-1) * net_amount_col).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            when(col("printing_fee_type") == "GOLD", unit_price_col * col("fee").cast("double"))
            .otherwise(col("fee").cast("double"))
            .alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("id").cast("string").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).cast("string").alias("partner_commission"),
            lit(None).cast("string").alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("transaction_time").alias("transaction_time"),
        )

    def _build_cancellations(self, base_df: DataFrame) -> DataFrame:
        """
        Build withdrawal_cancel rows (for EXPIRED / REJECTED and general cancel activity).
        """
        unit_price_col = coalesce(col("unit_price"), col("sell_price")).cast("double")
        net_amount_col = col("net_amount").cast("double")

        canc_df = base_df.select(
            col("user_id").alias("user_id"),
            col("account_id").alias("account_id"),
            lit("gold").alias("asset_type"),
            lit("gold").alias("product"),
            lit(0).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("gold").alias("asset_subtype"),
            lit("withdrawal_cancel").alias("activity"),
            net_amount_col.cast("string").alias("quantity"),
            net_amount_col.cast("string").alias("net_quantity"),
            coalesce(col("transaction_time"), col("updated")).alias("created"),
            col("status").alias("status"),
            lit("withdrawal").alias("transaction_type"),
            lit("1000002").alias("partner_id"),
            col("client_id").alias("client_id"),
            lit("IDR").alias("currency"),
            unit_price_col.cast("string").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            lit(None).alias("gtv"),
            lit(None).alias("gtv_usd"),
            (unit_price_col * net_amount_col).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            when(col("printing_fee_type") == "GOLD", unit_price_col * col("fee").cast("double"))
            .otherwise(col("fee").cast("double"))
            .alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("id").cast("string").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).cast("string").alias("partner_commission"),
            lit(None).cast("string").alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("transaction_time").alias("transaction_time"),
        )

        return canc_df

    def process(self) -> Optional[DataFrame]:
        base_df = self._read_and_deduplicate()
        if base_df is None:
            return None
        requests_df = self._build_requests(base_df)
        cancels_df = self._build_cancellations(base_df)
        return requests_df.unionByName(cancels_df)

