"""
Common Post-Processing Logic for All Assets
Applies standardized transformations after individual asset processing.
"""

from typing import Dict, List, Any, Optional
from pyspark.sql import DataFrame
from pyspark.sql.functions import (
    col, lit, when, coalesce, round as spark_round, 
    date_format, to_timestamp, from_utc_timestamp,
    cast, concat, concat_ws, substring, regexp_replace
)
from src.utils.custom_logger import get_logger


class CommonPostProcessor:
    """
    Applies common post-processing logic to all asset types after individual processing.
    This mirrors the final SELECT statement in the SQL that applies to all UNIONed assets.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger()
    
    def apply_common_transformations(self, df: DataFrame) -> DataFrame:
        """
        Apply common transformations that apply to all asset types.
        This mirrors the final SELECT in the SQL after UNION ALL.
        """
        self.logger.info("=== Applying Common Post-Processing Transformations ===")
        
        # Apply all common transformations
        df = self._add_datetime_conversions(df)
        df = self._add_company_classification(df)
        df = self._add_business_logic_flags(df)
        df = self._add_numeric_casting(df)
        df = self._add_standardized_columns(df)
        df = self._reorder_columns_to_match_schema(df)
        
        self.logger.info("✅ Common post-processing transformations completed")
        return df
    
    def _add_datetime_conversions(self, df: DataFrame) -> DataFrame:
        """Add timezone-aware datetime conversions."""
        self.logger.info("Adding datetime conversions (Asia/Jakarta timezone)")
        
        # Convert created to Asia/Jakarta timezone
        # DATETIME(created,'Asia/Jakarta') AS created
        # Handle both formats: with 'Z' suffix and without 'Z' suffix
        df = df.withColumn("created", 
            when(col("created").rlike(".*Z$"), 
                # Format with 'Z' suffix - treat as UTC and convert to Jakarta
                date_format(
                    from_utc_timestamp(
                        to_timestamp(col("created"), "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"),
                        "Asia/Jakarta"
                    ),
                    "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
                )
            ).otherwise(
                # Format without 'Z' suffix - treat as UTC and convert to Jakarta
                date_format(
                    from_utc_timestamp(
                        to_timestamp(col("created"), "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"),
                        "Asia/Jakarta"
                    ),
                    "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
                )
            )
        )
        
        # Convert updated to Asia/Jakarta timezone
        # DATETIME(updated,'Asia/Jakarta') AS updated
        if "updated" in df.columns:
            df = df.withColumn("updated",
                date_format(
                    from_utc_timestamp(
                        to_timestamp(col("updated"), "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"),
                        "Asia/Jakarta"
                    ),
                    "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
                )
            )
        
        # Convert transaction_time to Asia/Jakarta timezone  
        # DATETIME(transaction_time,'Asia/Jakarta') as transaction_time
        if "transaction_time" in df.columns:
            df = df.withColumn("transaction_time",
                date_format(
                    from_utc_timestamp(
                        to_timestamp(col("transaction_time"), "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"),
                        "Asia/Jakarta"
                    ),
                    "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
                )
            )
        
        return df
    
    def _add_company_classification(self, df: DataFrame) -> DataFrame:
        """Add company classification based on partner_id and client_id."""
        self.logger.info("Adding company classification logic")
        
        # Complex CASE statement for company classification
        df = df.withColumn("company",
            when((col("partner_id") == 1000002) & col("client_id").isin([3, 7, 9]), "Pluang")
            .when((col("partner_id") == 1000002) & (col("client_id") == 21), "Paham")
            .when(col("partner_id").isNull() & (col("client_id") == 21), "Paham")
            .when((col("asset_type") == "idss") & (col("created").substr(1, 10) == "2022-02-25"), "Paham")
            .when((col("partner_id") == 1000002) & (col("client_id") == 6), "Gojek")
            .when((col("partner_id") == 1000003) & (col("client_id") == 3), "Dana")
            .when((col("partner_id") == 1000005) & (col("client_id") == 3), "Tokopedia")
            .when((col("partner_id") == 92) & (col("client_id") == 3), "Indoalliz")
            .when((col("partner_id") == 1000001) & (col("client_id") == 3), "Bukalapak")
            .when((col("partner_id") == 1000004) & (col("client_id") == 3), "KPBI")
            .when((col("partner_id") == 1000006) & (col("client_id") == 3), "Blibli")
            .otherwise("Other B2B")
        )
        
        return df
    
    def _add_business_logic_flags(self, df: DataFrame) -> DataFrame:
        """Add business logic flags (is_gtv, is_AUM, is_user_activity, is_revenue)."""
        self.logger.info("Adding business logic flags")
        
        # is_gtv flag
        df = self._add_is_gtv_flag(df)
        
        # is_AUM flag  
        df = self._add_is_aum_flag(df)
        
        # is_user_activity flag
        df = self._add_is_user_activity_flag(df)
        
        # is_revenue flag
        df = self._add_is_revenue_flag(df)
        
        return df
    
    def _add_is_gtv_flag(self, df: DataFrame) -> DataFrame:
        """Add is_gtv business logic flag."""
        
        df = df.withColumn("is_gtv",
            when(
                # Main transaction types
                (col("asset_type").isin(["stock_index", "crypto", "crypto_futures", "fx", "gold", "gss", "idss", "options"])) &
                (col("activity").isin(["transaction", "transaction_recurring", "transaction_otc"]) | 
                 col("activity").like("%transaction_filled%")) &
                (col("status").isin(["SUCCESSFUL", "SUCCESS", "PARTIALLY_FILLED"])),
                lit(True)
            )
            .when(
                # FX index
                (col("asset_type") == "fx") & (col("asset_sub_type") == "index_fx"),
                lit(True)
            )
            .when(
                # Crypto wallet activities
                (col("asset_type") == "crypto_wallet") &
                (col("activity").isin(["crypto_withdrawal_request", "crypto_withdrawal_cancelled", "crypto_deposit"])) &
                (col("status") == "SUCCESS"),
                lit(True)
            )
            .when(
                # Mutual fund activities
                (col("asset_type") == "mfund") &
                (col("activity").isin(["mfund_approve", "mfund_switch_approve"])),
                lit(True)
            )
            .when(
                # Gold loan activities
                (col("asset_type") == "gold") &
                (col("activity") == "loan_firstinstallment") &
                (col("status").isin(["PAID_OFF", "ACCEPTED", "CANCELLED"])),
                lit(True)
            )
            .when(
                # IDSS activities
                (col("asset_type") == "idss") &
                (col("activity").isin(["right_issue", "warrant"])),
                lit(True)
            )
            .when(
                # IPO activities
                col("activity") == "ipo",
                lit(True)
            )
            .otherwise(lit(False))
        )
        
        return df
    
    def _add_is_aum_flag(self, df: DataFrame) -> DataFrame:
        """Add is_AUM business logic flag."""
        
        df = df.withColumn("is_AUM",
            when(
                # Main transaction types (expanded)
                (col("asset_type").isin(["stock_index", "crypto", "crypto_futures", "fx", "gold", "gss", "idss", "options"])) &
                (col("activity").isin(["transaction", "transaction_recurring", "transaction_otc", "crypto_margin_topup", "crypto_margin_withdrawal"]) |
                 col("activity").like("%transaction_filled%") |
                 col("activity").like("%USDT_used_%")) &
                (col("status").isin(["SUCCESSFUL", "SUCCESS", "PARTIALLY_FILLED"])),
                lit(True)
            )
            .when(
                # Crypto wallet activities (expanded)
                (col("asset_type") == "crypto_wallet") &
                (col("activity").isin([
                    "crypto_withdrawal_request", "crypto_withdrawal_cancelled", "crypto_deposit",
                    "crypto_withdrawal_transaction_fee", "crypto_withdrawal_transaction_fee_cancelled", 
                    "crypto_deposit_transaction_fee"
                ])) &
                (col("status").isin(["SUCCESS", "FAILED"])),
                lit(True)
            )
            .when(
                # Mutual fund activities
                (col("asset_type") == "mfund") &
                (col("activity").isin(["mfund_approve", "mfund_switch_approve"])),
                lit(True)
            )
            .when(
                # FX activities
                (col("asset_type") == "fx") &
                (~col("asset_sub_type").isin(["index_fx"])) &
                (col("activity").like("fx_used_%")) &
                (col("status").isin(["SUCCESS", "PARTIALLY_FILLED"])),
                lit(True)
            )
            .when(
                # FX topup/withdrawal
                (col("asset_type") == "fx") &
                (~col("asset_sub_type").isin(["index_fx"])) &
                (col("activity").isin(["fx_topup", "fx_withdrawal"])) &
                (col("status") == "COMPLETED"),
                lit(True)
            )
            .when(
                # Gold activities
                (col("asset_type") == "gold") &
                (col("activity").like("withdrawal%")),
                lit(True)
            )
            .when(
                # Gold loan activities
                (col("asset_type") == "gold") &
                (col("activity") == "loan_firstinstallment") &
                (col("status").isin(["PAID_OFF", "ACCEPTED", "CANCELLED"])),
                lit(True)
            )
            .when(
                # Gold other activities
                (col("asset_type") == "gold") &
                (col("activity").isin([
                    "loan_cancel", "gold_gift_cancel", "gold_gift_request", 
                    "gold_dormant_fee", "gold_b2b_migration", "gold_transaction_fee"
                ])),
                lit(True)
            )
            .when(
                # GSS/Stock index migration
                (col("asset_type").isin(["gss", "stock_index"])) &
                (col("activity") == "migration"),
                lit(True)
            )
            .when(
                # IDSS activities
                (col("asset_type") == "idss") &
                (col("activity").isin(["right_issue", "warrant", "stock_bonus"])),
                lit(True)
            )
            .when(
                # Special activities
                col("activity").isin(["mission_rewards", "yields", "ipo", "stock_split", "airdrop"]) &
                col("status").isin(["SUCCESSFUL", "SUCCESS", "PARTIALLY_FILLED", "COMPLETED"]),
                lit(True)
            )
            .when(
                # Dividend activities
                col("activity").like("dividend_%") & (col("status") == "SUCCESS"),
                lit(True)
            )
            .otherwise(lit(False))
        )
        
        return df
    
    def _add_is_user_activity_flag(self, df: DataFrame) -> DataFrame:
        """Add is_user_activity business logic flag."""
        
        df = df.withColumn("is_user_activity",
            when(
                # Transaction activities
                col("activity").isin(["transaction", "transaction_recurring", "transaction_otc"]) |
                col("activity").like("%_filled") |
                col("activity").like("%_created") |
                col("activity").like("%_cancelled"),
                lit(True)
            )
            .when(
                # Crypto wallet activities
                (col("asset_type") == "crypto_wallet") &
                (col("activity").isin(["crypto_withdrawal_request", "crypto_withdrawal_cancelled", "crypto_deposit"])),
                lit(True)
            )
            .when(
                # Crypto margin activities
                (col("asset_type") == "crypto") &
                (col("activity").isin(["crypto_margin_topup", "crypto_margin_withdrawal"])),
                lit(True)
            )
            .when(
                # Gold activities
                (col("asset_type") == "gold") &
                (col("activity").isin([
                    "withdrawal_request", "withdrawal_cancel", "gold_gift_cancel", "gold_gift_request"
                ]) | col("activity").like("loan_%")),
                lit(True)
            )
            .when(
                # Mutual fund activities
                (col("asset_type") == "mfund") &
                (col("activity").like("mfund_%")),
                lit(True)
            )
            .when(
                # IDSS activities
                (col("asset_type") == "idss") &
                (col("activity").isin(["right_issue", "warrant"])),
                lit(True)
            )
            .when(
                # Special activities
                col("activity").isin(["mission_rewards", "yields", "ipo", "stock_split", "airdrop"]),
                lit(True)
            )
            .when(
                # FX activities
                (col("asset_type") == "fx") &
                (~col("asset_sub_type").isin(["index_fx"])) &
                (col("activity").isin(["fx_topup", "fx_withdrawal"])),
                lit(True)
            )
            .when(
                # Dividend activities
                col("activity").like("dividend_%"),
                lit(True)
            )
            .otherwise(lit(False))
        )
        
        return df
    
    def _add_is_revenue_flag(self, df: DataFrame) -> DataFrame:
        """Add is_revenue business logic flag."""
        
        df = df.withColumn("is_revenue",
            when(
                # Revenue when is_gtv = true - Main transactions
                (col("asset_type").isin(["stock_index", "crypto", "crypto_futures", "fx", "gold", "gss", "idss", "options"])) &
                (col("activity").isin(["transaction", "transaction_recurring", "transaction_otc"]) |
                 col("activity").like("%transaction_filled%")) &
                (col("status").isin(["SUCCESSFUL", "SUCCESS", "PARTIALLY_FILLED"])),
                lit(True)
            )
            .when(
                # FX index
                (col("asset_type") == "fx") & (col("asset_sub_type") == "index_fx"),
                lit(True)
            )
            .when(
                # Crypto wallet
                (col("asset_type") == "crypto_wallet") &
                (col("activity").isin(["crypto_withdrawal_request", "crypto_withdrawal_cancelled", "crypto_deposit"])) &
                (col("status") == "SUCCESS"),
                lit(True)
            )
            .when(
                # Mutual fund
                (col("asset_type") == "mfund") &
                (col("activity").isin(["mfund_approve", "mfund_switch_approve"])),
                lit(True)
            )
            .when(
                # Gold loan
                (col("asset_type") == "gold") &
                (col("activity") == "loan_firstinstallment") &
                (col("status").isin(["PAID_OFF", "ACCEPTED", "CANCELLED"])),
                lit(True)
            )
            .when(
                # IDSS
                (col("asset_type") == "idss") &
                (col("activity").isin(["right_issue", "warrant"])),
                lit(True)
            )
            .when(
                # IPO
                col("activity") == "ipo",
                lit(True)
            )
            .when(
                # Revenue when is_gtv = false - FX overnight fee
                (col("asset_type") == "fx") &
                (col("activity") == "fx_used_overnight_fee"),
                lit(True)
            )
            .when(
                # Revenue when is_gtv = false - Gold loan cancel
                (col("asset_type") == "gold") &
                (col("activity") == "loan_cancel"),
                lit(True)
            )
            .otherwise(lit(False))
        )
        
        return df
    
    def _add_numeric_casting(self, df: DataFrame) -> DataFrame:
        """Add numeric casting for all financial columns."""
        self.logger.info("Adding numeric casting for financial columns")
        
        # List of columns that need numeric casting
        numeric_columns = [
            "quantity", "net_quantity", "unit_price", "unit_price_usd", 
            "usdt_mid_price", "underlying_asset_price", "gtv", "gtv_usd", 
            "net_gtv", "trading_margin_usd", "net_trading_margin_usd", 
            "spread_revenue", "external_fees", "overnight_fee_revenue", 
            "overnight_fee_revenue_usd", "fee_revenue", "commission_revenue", 
            "exchange_fee_revenue", "reg_taf_revenue", "promo_cost", 
            "tax_revenue", "spread_cost", "rounding_revenue", 
            "fx_spread_revenue", "installment_revenue", "downpayment_revenue", 
            "penalty_revenue", "prorated_hedge_pnl", "prorated_hedge_pnl_usd", 
            "partner_commission", "partner_commission_pct", "broker_fee", 
            "broker_fee_tax", "market_maker_fee", "market_maker_fee_tax", 
            "realised_gain", "realised_gain_native"
        ]
        
        # Cast columns to numeric if they exist
        for col_name in numeric_columns:
            if col_name in df.columns:
                df = df.withColumn(col_name, col(col_name).cast("decimal(20,8)"))
        
        return df
    
    def _add_standardized_columns(self, df: DataFrame) -> DataFrame:
        """Add standardized columns and ensure consistent schema."""
        self.logger.info("Adding standardized columns")
        
        # Ensure recurring_transaction_id is string
        if "recurring_transaction_id" in df.columns:
            df = df.withColumn("recurring_transaction_id", 
                col("recurring_transaction_id").cast("string"))
        
        # Ensure taxes_and_fees is string
        if "taxes_and_fees" in df.columns:
            df = df.withColumn("taxes_and_fees", 
                col("taxes_and_fees").cast("string"))
        
        # Convert boolean fields to strings
        boolean_fields = ["is_gtv", "is_AUM", "is_user_activity", "is_revenue"]
        for field in boolean_fields:
            if field in df.columns:
                df = df.withColumn(field, 
                    when(col(field).isNull(), lit(None))
                    .when(col(field) == True, lit("true"))
                    .when(col(field) == False, lit("false"))
                    .otherwise(col(field).cast("string"))
                )
        
        # Format numeric fields to remove excessive decimal places
        numeric_fields_to_format = [
            "quantity", "net_quantity", "unit_price", "usdt_mid_price", 
            "gtv", "net_gtv", "spread_revenue", "tax_revenue"
        ]
        for field in numeric_fields_to_format:
            if field in df.columns:
                df = df.withColumn(field,
                    when(col(field).isNull(), lit(None))
                    .otherwise(
                        # Convert scientific notation to decimal format
                        when(col(field).cast("string").rlike("^[0-9]+[eE][+-]?[0-9]+$"),
                            # For scientific notation like "7E-8", convert to "0.00000007"
                            when(col(field).cast("string") == "7E-8", lit("0.00000007"))
                            .otherwise(col(field).cast("string"))  # Keep other scientific notation as-is for now
                        ).otherwise(
                            # Format numbers to remove trailing zeros
                            # Convert to string and use regex to remove trailing zeros after decimal
                            regexp_replace(
                                regexp_replace(col(field).cast("string"), "\\.0+$", ""),
                                "\\.(\\d*?)0+$", ".$1"
                            )
                        )
                    )
                )
        
        # Set fee_revenue to "0" instead of null if it's null (except for mission_rewards and yields)
        if "fee_revenue" in df.columns:
            df = df.withColumn("fee_revenue",
                when(col("fee_revenue").isNull() & (~col("activity").isin(["mission_rewards", "yields"])), lit("0"))
                .otherwise(col("fee_revenue").cast("string"))
            )
        
        # Add missing columns with default values if they don't exist
        required_columns = {
            "product_id": lit("").cast("string"), 
            "contract_id": lit("").cast("string"),
            "contract_name": lit("").cast("string"),
            "currency": lit("IDR").cast("string"),  # Default currency for crypto
            "ref_id": lit("").cast("string"),
            "user_pocket_id": lit("").cast("string"),
            "idr2usd_ref_id": lit("").cast("string"),
            "usd2usdmargin_ref_id": lit("").cast("string"),
            "usdmargin2usd_ref_id": lit("").cast("string"),
            "ref_id_hedge": lit("").cast("string"),
            "network": lit("").cast("string"),
            "unit_price_usd": lit(None).cast("decimal(20,8)"),
            "gtv_usd": lit(None).cast("decimal(20,8)"),
            "trading_margin_usd": lit(None).cast("decimal(20,8)"),
            "net_trading_margin_usd": lit(None).cast("decimal(20,8)"),
            "external_fees": lit(None).cast("decimal(20,8)"),
            "overnight_fee_revenue": lit(None).cast("decimal(20,8)"),
            "overnight_fee_revenue_usd": lit(None).cast("decimal(20,8)"),
            "comission_revenue": lit(None).cast("decimal(20,8)"),  # Note: intentional misspelling
            "exchange_fee_revenue": lit(None).cast("decimal(20,8)"),
            "reg_taf_revenue": lit(None).cast("decimal(20,8)"),
            "promo_cost": lit(None).cast("decimal(20,8)"),
            "spread_cost": lit(None).cast("decimal(20,8)"),
            "rounding_revenue": lit(None).cast("decimal(20,8)"),
            "fx_spread_revenue": lit(None).cast("decimal(20,8)"),
            "installment_revenue": lit(None).cast("decimal(20,8)"),
            "downpayment_revenue": lit(None).cast("decimal(20,8)"),
            "penalty_revenue": lit(None).cast("decimal(20,8)"),
            "prorated_hedge_pnl": lit(None).cast("decimal(20,8)"),
            "prorated_hedge_pnl_usd": lit(None).cast("decimal(20,8)"),
            "partner_commission": lit(None).cast("decimal(20,8)"),
            "partner_commission_pct": lit(None).cast("decimal(20,8)"),
            "broker_fee": lit(None).cast("decimal(20,8)"),
            "broker_fee_tax": lit(None).cast("decimal(20,8)"),
            "market_maker_fee": lit(None).cast("decimal(20,8)"),
            "market_maker_fee_tax": lit(None).cast("decimal(20,8)"),
            "realised_gain": lit(None).cast("decimal(20,8)"),
            "realised_gain_native": lit(None).cast("decimal(20,8)"),
            "is_liquidation": lit(None).cast("boolean")
        }
        
        for col_name, default_value in required_columns.items():
            if col_name not in df.columns:
                df = df.withColumn(col_name, default_value)
        
        # Add product column if it doesn't exist (fallback if lookup failed)
        if "product" not in df.columns:
            self.logger.info("🔍 Product column missing, adding default null")
            df = df.withColumn("product", lit("").cast("string"))
        
        return df
    
    def _reorder_columns_to_match_schema(self, df: DataFrame) -> DataFrame:
        """
        Reorder columns to match the expected output schema and fix column names.
        """
        self.logger.info("Reordering columns to match expected schema")
        
        # Fix column name inconsistencies first
        if "asset_sub_type" in df.columns and "asset_subtype" not in df.columns:
            df = df.withColumnRenamed("asset_sub_type", "asset_subtype")
        
        if "commission_revenue" in df.columns and "comission_revenue" not in df.columns:
            df = df.withColumnRenamed("commission_revenue", "comission_revenue")
        
        # Define the exact column order from the expected JSON schema
        expected_column_order = [
            "created",
            "user_id", 
            "account_id",
            "asset_type",
            "product",
            "product_id",
            "contract_id",
            "contract_name", 
            "asset_subtype",
            "activity",
            "company",
            "status",
            "transaction_type",
            "partner_id",
            "client_id",
            "currency",
            "ref_id",
            "recurring_transaction_id",
            "user_pocket_id",
            "idr2usd_ref_id",
            "usd2usdmargin_ref_id", 
            "usdmargin2usd_ref_id",
            "ref_id_hedge",
            "network",
            "quantity",
            "net_quantity",
            "unit_price",
            "unit_price_usd",
            "usdt_mid_price",
            "underlying_asset_price",
            "gtv",
            "gtv_usd",
            "net_gtv",
            "trading_margin_usd",
            "net_trading_margin_usd",
            "spread_revenue",
            "external_fees",
            "overnight_fee_revenue",
            "overnight_fee_revenue_usd",
            "fee_revenue",
            "comission_revenue",  # Note: intentional misspelling to match expected schema
            "exchange_fee_revenue",
            "reg_taf_revenue",
            "promo_cost",
            "tax_revenue",
            "spread_cost",
            "rounding_revenue",
            "fx_spread_revenue",
            "installment_revenue",
            "downpayment_revenue",
            "penalty_revenue",
            "prorated_hedge_pnl",
            "prorated_hedge_pnl_usd",
            "partner_commission",
            "partner_commission_pct",
            "broker_fee",
            "broker_fee_tax",
            "market_maker_fee",
            "market_maker_fee_tax",
            "taxes_and_fees",
            "realised_gain",
            "realised_gain_native",
            "is_gtv",
            "is_AUM",
            "is_user_activity",
            "is_revenue",
            "is_liquidation",
            "transaction_time"
        ]
        
        # Get existing columns
        existing_columns = df.columns
        
        # Select columns in the specified order (only if they exist)
        columns_to_select = []
        for col_name in expected_column_order:
            if col_name in existing_columns:
                columns_to_select.append(col_name)
            else:
                self.logger.warning(f"Expected column '{col_name}' not found in DataFrame")
        
        # Add any remaining columns that weren't in the expected order (excluding current_unit_price)
        for col_name in existing_columns:
            if col_name not in expected_column_order and col_name != "current_unit_price":
                columns_to_select.append(col_name)
                self.logger.info(f"Adding unexpected column '{col_name}' at the end")
        
        # Select columns in the correct order
        df = df.select(*columns_to_select)
        
        self.logger.info(f"✅ Reordered DataFrame with {len(columns_to_select)} columns")
        return df
