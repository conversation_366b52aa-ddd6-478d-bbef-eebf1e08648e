"""
Crypto Main Transaction Processor
Handles regular crypto currency transactions (market orders).
Corresponds to the first UNION block in the SQL (lines 833-963).
"""

from typing import Dict, List, Any
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, lit, when, coalesce, get_json_object, cast, concat, lower
from .base_crypto_processor import BaseCryptoProcessor


class CryptoMainTransactionProcessor(BaseCryptoProcessor):
    """
    Processor for main crypto currency transactions (market orders).
    
    SQL Reference: Lines 833-963
    - WHERE a.advanced_order_info IS NULL (market orders only)
    - Includes price_info extraction for effectiveSpread
    - Handles USDT price patches and historical logic
    """
    
    def _get_source_name(self) -> str:
        return "crypto_main_transactions"
    
    def _get_base_transaction_data(self) -> DataFrame:
        """
        Read and prepare base transaction data for crypto main transactions.
        Based on SQL lines 1300-1546 (dual-query approach).
        """
        source_name = self._get_source_name()
        self.logger.info(f"=== Reading {source_name} ===")
        
        # Construct S3 path
        if self.h_1 == self.t_1:
            # Use format without hour partition
            s3_path = f"{self.transactions_snapshot_path}crypto_currency_transactions/dt={self.t_1}/"
        else:
            # Use format with hour partition
            s3_path = f"{self.transactions_snapshot_path}crypto_currency_transactions/dt={self.t_1}/hour={self.h_1}/"
        
        # Use error handling helper from base class
        crypto_txn = self._read_data_with_error_handling(s3_path, source_name, "crypto_currency_transactions")
        
        if crypto_txn is None:
            self.logger.warning(f"No data available for {source_name}")
            return None
        
        # Check if DataFrame is empty
        row_count = crypto_txn.count()
        if row_count == 0:
            self.logger.warning(f"Empty DataFrame for {source_name}")
            return None
        
        self.logger.info(f"Raw {source_name} count before filtering: {row_count}")
        
        # Apply dual-query approach as per SQL logic
        # Query 1: Advanced orders that were created (any status)
        created_orders = crypto_txn.filter(
            col("advanced_order_info").isNotNull()
        )
        
        # Query 2: Advanced orders that were cancelled/failed (exclude SUCCESS and PENDING)
        cancelled_failed_orders = crypto_txn.filter(
            col("advanced_order_info").isNotNull() &
            ~col("status").isin(["SUCCESS", "PENDING"])
        )
        
        # Remove cancelled/failed orders from created orders to avoid duplication
        created_orders = created_orders.filter(
            col("status").isin(["SUCCESS", "PENDING"])
        )
        
        # Non-advanced orders (regular market orders)
        non_advanced_orders = crypto_txn.filter(
            col("advanced_order_info").isNull()
        )
        
        self.logger.info(f"Advanced orders (created): {created_orders.count()}")
        self.logger.info(f"Advanced orders (cancelled/failed): {cancelled_failed_orders.count()}")
        self.logger.info(f"Non-advanced orders: {non_advanced_orders.count()}")
        
        # Process each type separately
        created_df = self._process_created_advanced_orders(created_orders)
        cancelled_failed_df = self._process_cancelled_failed_advanced_orders(cancelled_failed_orders)
        non_advanced_df = self._process_non_advanced_orders(non_advanced_orders)
        
        # Union all results
        result_df = created_df.union(cancelled_failed_df).union(non_advanced_df)
        
        self.logger.info(f"Total processed {source_name} count: {result_df.count()}")
        return result_df
    
    def _apply_processor_specific_transformations(self, df: DataFrame) -> DataFrame:
        """
        Apply transformations specific to main crypto transactions.
        Based on SQL lines 1300-1546 (dual-query approach).
        """
        self.logger.info(f"=== Applying {self._get_source_name()} specific transformations ===")
        
        # Activity column is now handled in individual processing methods
        # No need to add it here as it's already set by _process_*_orders methods
        
        # Add net_quantity calculation (from base processor's common transformations)
        # This is already handled in _apply_common_transformations
        
        # Use the created_final field that was set by individual processing methods
        df = df.withColumn("created", col("created_final")).drop("created_final")
        
        # Add usdt_mid_price extraction from price_info
        # SQL: CAST(CASE WHEN crypto_currency_id = 10005 THEN JSON_EXTRACT_SCALAR(price_info, '$.exchangeMidPriceByOrderBook')
        #           ELSE JSON_EXTRACT_SCALAR(price_info, '$.usdtMidPriceByOrderBook') END AS NUMERIC)
        df = df.withColumn("usdt_mid_price",
            when(col("asset_id") == 10005,  # USDT
                get_json_object(col("price_info"), "$.exchangeMidPriceByOrderBook").cast("double")
            ).otherwise(
                get_json_object(col("price_info"), "$.usdtMidPriceByOrderBook").cast("double")
            )
        )
        
        # Add GTV calculation based on SQL logic
        # Simplified version without historical date logic for now
        df = df.withColumn("gtv",
            when(col("transaction_type") == "AIRDROP", None)
            .when(col("transaction_type") == "BUY",
                col("executed_total_price") - coalesce(
                    get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                    col("taxation_fee")
                )
            )
            .when(col("transaction_type") == "SELL",
                col("executed_total_price") + coalesce(
                    get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                    col("taxation_fee")
                )
            )
            .otherwise(None)
        )
        
        # Get effective spread rate from config
        effective_spread_rate = self.config.get("crypto_financial_config", {}).get("effective_spread_rate", 
                                                self.asset_config.get("effective_spread_rate", 0.02))
        spread_divisor = self.config.get("crypto_financial_config", {}).get("spread_divisor", 
                                        self.asset_config.get("spread_divisor", 200.0))
        
        # Override spread_revenue calculation with main crypto specific logic
        # Extract effectiveSpread from price_info JSON field as per SQL logic
        df = df.withColumn("spread_revenue",
            when(col("transaction_type") == "AIRDROP", None)
            .when(col("transaction_type") == "BUY",
                # Extract effectiveSpread from price_info JSON field
                (col("executed_total_price") - coalesce(
                    get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                    col("taxation_fee")
                )) * get_json_object(col("price_info"), "$.effectiveSpread").cast("double") / lit(spread_divisor)
            )
            .when(col("transaction_type") == "SELL",
                # Extract effectiveSpread from price_info JSON field
                (col("executed_total_price") + coalesce(
                    get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                    col("taxation_fee")
                )) * get_json_object(col("price_info"), "$.effectiveSpread").cast("double") / lit(spread_divisor)
            )
            .otherwise(None)
        )
        
        # Add fee_revenue from taxes_and_fees JSON
        df = df.withColumn("fee_revenue",
            coalesce(
                get_json_object(col("taxes_and_fees"), "$.transactionFee").cast("double"),
                lit(None).cast("string")
            )
        )
        
        # Add tax_revenue calculation based on SQL logic
        # Simplified version without historical date logic
        crypto_financial_config = self.config.get("crypto_financial_config", {})
        buy_tax_rate = crypto_financial_config.get("buy_tax_rate", 
                                                  self.asset_config.get("buy_tax_rate", 0.0011))
        sell_tax_rate = crypto_financial_config.get("sell_tax_rate", 
                                                   self.asset_config.get("sell_tax_rate", 0.0010))
        
        
        # Set user_pocket_id to NULL for main transactions (as per SQL comment)
        df = df.withColumn("user_pocket_id", lit("").cast("string"))
        
        # Keep the taxes_and_fees JSON field
        df = df.withColumn("taxes_and_fees", col("taxes_and_fees"))
        
        return df
    
    def _process_created_advanced_orders(self, df: DataFrame) -> DataFrame:
        """
        Process advanced orders that were created (Query 1 logic).
        SQL: WHERE a.advanced_order_info IS NOT NULL
        """
        self.logger.info("=== Processing created advanced orders ===")
        
        # Select and rename columns to match SQL structure
        result_df = df.select(
            col("crypto_currency_id").alias("asset_id"),
            "account_id", "user_id", col("id").alias("transaction_id"),
            "created", "updated",
            col("quantity").alias("executed_quantity"),
            col("unit_price").alias("executed_unit_price"),
            col("total_price").alias("executed_total_price"),
            col("transaction_fee").alias("fees"),
            "transaction_type", "status",
            lit(1).alias("currency_to_idr"),
            "transaction_time", "partner_id", "price_info",
            "taxes_and_fees", "taxation_fee", "info",
            "advanced_order_info", "order_type"
        ).withColumn("asset_sub_type", lit("crypto"))
        
        # Add activity column based on SQL logic
        result_df = result_df.withColumn("activity",
            concat(lower(col("order_type")), lit("_transaction_created"))
        )
        
        # Add created_final field (simple for created orders)
        result_df = result_df.withColumn("created_final", col("created"))
        
        return result_df
    
    def _process_cancelled_failed_advanced_orders(self, df: DataFrame) -> DataFrame:
        """
        Process advanced orders that were cancelled/failed (Query 2 logic).
        SQL: WHERE a.advanced_order_info IS NOT NULL AND status NOT IN ('SUCCESS', 'PENDING')
        """
        self.logger.info("=== Processing cancelled/failed advanced orders ===")
        
        # Select and rename columns to match SQL structure
        result_df = df.select(
            col("crypto_currency_id").alias("asset_id"),
            "account_id", "user_id", col("id").alias("transaction_id"),
            "created", "updated",
            col("quantity").alias("executed_quantity"),
            col("unit_price").alias("executed_unit_price"),
            col("total_price").alias("executed_total_price"),
            col("transaction_fee").alias("fees"),
            "transaction_type", "status",
            lit(1).alias("currency_to_idr"),
            "transaction_time", "partner_id", "price_info",
            "taxes_and_fees", "taxation_fee", "info",
            "advanced_order_info", "order_type"
        ).withColumn("asset_sub_type", lit("crypto"))
        
        # Add activity column based on SQL logic
        result_df = result_df.withColumn("activity",
            when(col("status").isin(["PARTIALLY_FILLED", "CANCELLED", "EXPIRED"]),
                 concat(lower(col("order_type")), lit("_transaction_cancelled")))
            .when(col("status") == "FAILED",
                 concat(lower(col("order_type")), lit("_transaction_failed")))
            .otherwise(lit("").cast("string"))
        )
        
        # Add created_final field with migration logic (simplified without migration table)
        result_df = result_df.withColumn("created_final",
            when(col("transaction_time").isNull(), col("updated"))
            .otherwise(col("transaction_time"))
        )
        
        return result_df
    
    def _process_non_advanced_orders(self, df: DataFrame) -> DataFrame:
        """
        Process non-advanced orders (regular market orders).
        SQL: WHERE a.advanced_order_info IS NULL
        """
        self.logger.info("=== Processing non-advanced orders ===")
        
        # Select and rename columns to match SQL structure
        result_df = df.select(
            col("crypto_currency_id").alias("asset_id"),
            "account_id", "user_id", col("id").alias("transaction_id"),
            "created", "updated",
            col("quantity").alias("executed_quantity"),
            col("unit_price").alias("executed_unit_price"),
            col("total_price").alias("executed_total_price"),
            col("transaction_fee").alias("fees"),
            "transaction_type", "status",
            lit(1).alias("currency_to_idr"),
            "transaction_time", "partner_id", "price_info",
            "taxes_and_fees", "taxation_fee", "info",
            "advanced_order_info", "order_type"
        ).withColumn("asset_sub_type", lit("crypto"))
        
        # Add activity column for non-advanced orders
        # Note: Main crypto transactions don't have recurring_transaction_id column
        result_df = result_df.withColumn("activity",
            when(col("transaction_type") == "AIRDROP", lit("airdrop"))
            .otherwise(lit("transaction"))
        )
        
        # Add created_final field (simple for non-advanced orders)
        result_df = result_df.withColumn("created_final",
            when(col("transaction_time").isNotNull(), col("transaction_time"))
            .otherwise(col("created"))
        )
        
        return result_df
