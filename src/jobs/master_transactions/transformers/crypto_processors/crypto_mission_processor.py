"""
Crypto Mission Rewards Processor
Handles crypto currency mission rewards.
Corresponds to the third UNION block in the SQL (lines 1062-1128).
"""

from typing import Dict, List, Any
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, lit, when, coalesce, get_json_object, cast
from .base_crypto_processor import BaseCryptoProcessor


class CryptoMissionProcessor(BaseCryptoProcessor):
    """
    Processor for crypto currency mission rewards.
    
    SQL Reference: Lines 1062-1128
    - FROM crypto_currency_mission_rewards
    - WHERE STATUS in ('CLAIMED', 'UNLOCKED')
    - Uses claim_date as transaction_time
    - Activity = 'mission_rewards'
    - Most revenue fields are NULL
    """
    
    def _get_source_name(self) -> str:
        return "crypto_mission_rewards"
    
    def _get_base_transaction_data(self) -> DataFrame:
        """
        Get crypto currency mission rewards.
        SQL: FROM crypto_currency_mission_rewards WHERE STATUS in ('CLAIMED', 'UNLOCKED')
        """
        source_name = self._get_source_name()
        self.logger.info(f"=== Reading {source_name} ===")
        
        # Construct S3 path
        if self.h_1 == self.t_1:
            # Use format without hour partition
            s3_path = f"{self.transactions_snapshot_path}crypto_currency_mission_rewards/dt={self.t_1}/"
        else:
            # Use format with hour partition
            s3_path = f"{self.transactions_snapshot_path}crypto_currency_mission_rewards/dt={self.t_1}/hour={self.h_1}/"
        
        # Use error handling helper from base class
        crypto_mission_txn = self._read_data_with_error_handling(s3_path, source_name, "crypto_currency_mission_rewards")
        
        if crypto_mission_txn is None:
            self.logger.info(f"No {source_name} data found for date {self.t_1}")
            return None
        
        # Check if DataFrame is empty
        row_count = crypto_mission_txn.count()
        if row_count == 0:
            self.logger.warning(f"Empty DataFrame for {source_name}")
            return None
        
        self.logger.info(f"Raw {source_name} count before filtering: {row_count}")
        
        # Filter for CLAIMED/UNLOCKED status as per SQL
        crypto_mission_txn = crypto_mission_txn.filter(
            col("status").isin(["CLAIMED", "UNLOCKED"])
        )
        self.logger.info(f"{source_name} count after status filter: {crypto_mission_txn.count()}")
        
        if crypto_mission_txn.count() == 0:
            self.logger.info(f"No {source_name} data after filtering")
            return None
        
        # Select and rename columns to match SQL structure
        crypto_mission_txn = crypto_mission_txn.select(
            col("crypto_currency_id").alias("asset_id"),
            "account_id", "user_id", col("id").alias("transaction_id"),
            # Use claim_date if available, otherwise use created (as per SQL logic)
            when(col("claim_date").isNotNull(), col("claim_date")).otherwise(col("created")).alias("created"),
            "updated", 
            col("quantity").alias("executed_quantity"), 
            col("unit_price").alias("executed_unit_price"),
            (col("quantity") * col("unit_price")).alias("executed_total_price"),
            lit(0).alias("fees"),  # Mission rewards typically have no fees
            lit("BUY").alias("transaction_type"),  # As per SQL: mission rewards are 'BUY'
            "status", 
            lit(1).alias("currency_to_idr"),
            # Use claim_date as transaction_time (as per SQL comment)
            col("claim_date").alias("transaction_time"), 
            "partner_id",
            col("client_id").cast("string")  # Ensure client_id is string
        ).withColumn("asset_sub_type", lit("crypto")) \
         .withColumn("price_info", lit(None)) \
         .withColumn("taxes_and_fees", lit(None)) \
         .withColumn("taxation_fee", lit(None))  # Mission rewards don't have these fields
        
        return crypto_mission_txn
    
    def _apply_processor_specific_transformations(self, df: DataFrame) -> DataFrame:
        """
        Apply transformations specific to crypto mission rewards.
        Based on SQL lines 1062-1128.
        """
        if df is None:
            return None
            
        self.logger.info(f"=== Applying {self._get_source_name()} specific transformations ===")
        
        # Add activity column - always 'mission_rewards' for this source
        df = df.withColumn("activity", lit("mission_rewards"))
        
        # Add net_quantity - always equals quantity for mission rewards (always positive)
        df = df.withColumn("net_quantity", col("executed_quantity"))
        
        # created field already handled in base data selection using claim_date logic
        
        # Mission rewards don't have usdt_mid_price in SQL
        df = df.withColumn("usdt_mid_price", lit(None))
        
        # Mission rewards have NULL GTV as per SQL
        df = df.withColumn("gtv", lit(None))
        df = df.withColumn("net_gtv", lit(None))
        
        # Override spread_revenue - NULL for mission rewards as per SQL
        df = df.withColumn("spread_revenue", lit(None))
        
        # Mission rewards have NULL fee_revenue as per SQL
        df = df.withColumn("fee_revenue", lit(None))
        
        # Mission rewards have NULL tax_revenue as per SQL
        df = df.withColumn("tax_revenue", lit(None))
        
        # Mission rewards don't have user_pocket_id
        df = df.withColumn("user_pocket_id", lit(None))
        
        # Mission rewards don't have recurring_transaction_id
        df = df.withColumn("recurring_transaction_id", lit(None))
        
        # taxes_and_fees and taxation_fee already added in base data selection
        
        # transaction_time already set to claim_date in base data selection
        
        return df
