"""
Crypto Processor Factory
Creates and manages all crypto transaction processors.
"""

from typing import List, Dict, Any
from .base_crypto_processor import BaseCryptoProcessor
from .crypto_main_processor import CryptoMainTransactionProcessor
from .crypto_pocket_processor import CryptoPocketProcessor
from .crypto_mission_processor import CryptoMissionProcessor
from .crypto_yields_processor import CryptoYieldsProcessor
from .crypto_margin_wallet_transfers_processor import CryptoMarginWalletTransfersProcessor


class CryptoProcessorFactory:
    """
    Factory class to create and manage crypto transaction processors.
    Each processor handles a specific crypto transaction source from the SQL.
    """
    
    @staticmethod
    def create_all_processors(spark, config, t_1, h_1) -> List[BaseCryptoProcessor]:
        """
        Create all crypto processors with required parameters.
        Returns a list of processor instances ready for processing.
        """
        processors = [
            CryptoMainTransactionProcessor(spark, config, t_1, h_1),    # Main crypto transactions (market orders)
            CryptoPocketProcessor(spark, config, t_1, h_1),             # Crypto pocket transactions
            CryptoMissionProcessor(spark, config, t_1, h_1),            # Crypto mission rewards
            CryptoYieldsProcessor(spark, config, t_1, h_1),             # Crypto PluangCuan yields
            CryptoMarginWalletTransfersProcessor(spark, config, t_1, h_1), # Crypto margin wallet transfers
            # TODO: Add more processors as they are implemented:
            # CryptoAdvancedOrderProcessor(spark, config, t_1, h_1),    # Limit/stop orders
            # CryptoWalletProcessor(spark, config, t_1, h_1),           # Wallet deposits/withdrawals
            # CryptoFuturesProcessor(spark, config, t_1, h_1),          # Crypto futures
        ]
        
        return processors
    
    @staticmethod
    def create_processor_by_name(processor_name: str, spark, config, t_1, h_1) -> BaseCryptoProcessor:
        """
        Create a specific processor by name.
        Useful for testing individual processors.
        """
        processor_map = {
            "crypto_main": CryptoMainTransactionProcessor,
            "crypto_pocket": CryptoPocketProcessor,
            "crypto_mission": CryptoMissionProcessor,
            "crypto_yields": CryptoYieldsProcessor,
            "crypto_margin_wallet": CryptoMarginWalletTransfersProcessor,
            # TODO: Add more as implemented
        }
        
        if processor_name not in processor_map:
            raise ValueError(f"Unknown processor name: {processor_name}. Available: {list(processor_map.keys())}")
        
        return processor_map[processor_name](spark, config, t_1, h_1)
    
    @staticmethod
    def get_available_processors() -> List[str]:
        """
        Get list of available processor names.
        """
        return [
            "crypto_main",
            "crypto_pocket", 
            "crypto_mission",
            "crypto_yields",
            "crypto_margin_wallet",
            # TODO: Add more as implemented
        ]
    
    @staticmethod
    def get_processor_info() -> Dict[str, Dict[str, Any]]:
        """
        Get information about all available processors.
        Useful for debugging and documentation.
        """
        return {
            "crypto_main": {
                "class": "CryptoMainTransactionProcessor",
                "description": "Main crypto transactions (market orders)",
                "sql_lines": "833-963",
                "source_table": "crypto_currency_transactions",
                "filter": "advanced_order_info IS NULL"
            },
            "crypto_pocket": {
                "class": "CryptoPocketProcessor", 
                "description": "Crypto pocket transactions",
                "sql_lines": "965-1060",
                "source_table": "crypto_currency_pocket_transactions",
                "filter": "None"
            },
            "crypto_mission": {
                "class": "CryptoMissionProcessor",
                "description": "Crypto mission rewards", 
                "sql_lines": "1062-1128",
                "source_table": "crypto_currency_mission_rewards",
                "filter": "STATUS in ('CLAIMED', 'UNLOCKED')"
            },
            "crypto_yields": {
                "class": "CryptoYieldsProcessor",
                "description": "Crypto PluangCuan yields",
                "sql_lines": "1130-1198", 
                "source_table": "crypto_currency_pluangcuan_yields",
                "filter": "STATUS in ('COMPLETED')"
            },
            "crypto_margin_wallet": {
                "class": "CryptoMarginWalletTransfersProcessor",
                "description": "Crypto margin wallet transfers (dual-query approach)",
                "sql_lines": "2428-2560",
                "source_table": "pluang_crypto_futures__crypto_margin_wallet_transfers",
                "filter": "None (dual-query: crypto side + margin wallet side)"
            }
            # TODO: Add more as implemented
        }
