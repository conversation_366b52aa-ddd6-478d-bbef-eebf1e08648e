"""
Crypto Margin Wallet Transfers Processor
Handles crypto margin wallet transfers with dual-query approach.
Corresponds to SQL lines 2428-2560 (two UNIONed queries).
"""

from typing import Dict, List, Any
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, lit, when, concat, lower
from .base_crypto_processor import BaseCryptoProcessor


class CryptoMarginWalletTransfersProcessor(BaseCryptoProcessor):
    """
    Processor for crypto margin wallet transfers.
    Implements dual-query approach: crypto side and margin wallet side.
    """
    
    def _get_source_name(self) -> str:
        return "crypto_margin_wallet_transfers"
    
    def _get_base_transaction_data(self) -> DataFrame:
        """
        Read and prepare base transaction data for crypto margin wallet transfers.
        Based on SQL lines 2428-2560 (dual-query approach).
        """
        source_name = self._get_source_name()
        self.logger.info(f"=== Reading {source_name} ===")
        
        # Construct S3 path
        if self.h_1 == self.t_1:
            # Use format without hour partition
            s3_path = f"{self.transactions_snapshot_path}crypto_margin_wallet_transfers/dt={self.t_1}/"
        else:
            # Use format with hour partition
            s3_path = f"{self.transactions_snapshot_path}crypto_margin_wallet_transfers/dt={self.t_1}/hour={self.h_1}/"
        
        # Use error handling helper from base class
        margin_transfers = self._read_data_with_error_handling(s3_path, source_name, "crypto_margin_wallet_transfers")
        
        if margin_transfers is None:
            self.logger.warning(f"No {source_name} data available")
            return None
        
        # Check if DataFrame is empty
        row_count = margin_transfers.count()
        if row_count == 0:
            self.logger.warning(f"Empty DataFrame for {source_name}")
            return None
        
        self.logger.info(f"Raw {source_name} count before filtering: {row_count}")
        
        # Extract data from "value" key if present
        if "value" in margin_transfers.columns:
            margin_transfers = margin_transfers.select(col("value.*"))
            self.logger.info("Extracted data from 'value' key")
        
        # Apply dual-query approach as per SQL logic
        # Query 1: Crypto side perspective
        crypto_side_df = self._process_crypto_side(margin_transfers)
        
        # Query 2: Margin wallet side perspective  
        margin_wallet_side_df = self._process_margin_wallet_side(margin_transfers)
        
        # Union both perspectives
        result_df = crypto_side_df.union(margin_wallet_side_df)
        
        self.logger.info(f"Total processed {source_name} count: {result_df.count()}")
        return result_df
    
    def _process_crypto_side(self, df: DataFrame) -> DataFrame:
        """
        Process crypto side perspective (Query 1 logic).
        SQL: CRYPTO MARGIN TRANSFERS - CRYPTO
        """
        self.logger.info("=== Processing crypto side perspective ===")
        
        # Select and rename columns to match SQL structure
        result_df = df.select(
            "user_id", "account_id", "id", "transaction_type", "transaction_amount",
            "transaction_time", "internal_status", "partner_id", "client_id",
            "settle_asset_mid_price"
        ).withColumn("asset_type", lit("crypto")) \
         .withColumn("product", lit("USDT")) \
         .withColumn("product_id", lit(10005)) \
         .withColumn("contract_id", lit(None)) \
         .withColumn("contract_name", lit(None)) \
         .withColumn("asset_subtype", lit("crypto")) \
         .withColumn("currency", lit("USDT")) \
         .withColumn("asset_id", lit(10005))  # USDT asset ID
        
        # Add activity column based on SQL logic
        result_df = result_df.withColumn("activity",
            concat(lit("crypto_margin"), 
                   when(col("transaction_type") == "DEPOSIT", lit("_topup"))
                   .otherwise(lit("_withdrawal")))
        )
        
        # Add quantity (always positive)
        result_df = result_df.withColumn("executed_quantity", col("transaction_amount"))
        
        # Add net_quantity based on SQL logic
        result_df = result_df.withColumn("net_quantity",
            when(col("transaction_type") == "DEPOSIT", -col("transaction_amount"))
            .otherwise(col("transaction_amount"))
        )
        
        # Add created field
        result_df = result_df.withColumn("created", col("transaction_time"))
        
        # Add status
        result_df = result_df.withColumn("status", col("internal_status"))
        
        # Add transaction_type based on SQL logic
        result_df = result_df.withColumn("transaction_type_final",
            when(col("transaction_type") == "DEPOSIT", lit("SELL"))
            .otherwise(lit("BUY"))
        )
        
        # Add unit_price and usdt_mid_price
        result_df = result_df.withColumn("executed_unit_price", col("settle_asset_mid_price")) \
                            .withColumn("usdt_mid_price", col("settle_asset_mid_price").cast("decimal(20,8)"))
        
        # Add gtv calculation
        result_df = result_df.withColumn("executed_total_price", 
                                       col("transaction_amount") * col("settle_asset_mid_price"))
        
        # Add net_gtv based on SQL logic
        result_df = result_df.withColumn("net_gtv",
            when(col("transaction_type") == "DEPOSIT", 
                 -col("transaction_amount") * col("settle_asset_mid_price"))
            .otherwise(col("transaction_amount") * col("settle_asset_mid_price"))
        )
        
        # Add ref_id
        result_df = result_df.withColumn("ref_id", col("id"))
        
        # Add transaction_time
        result_df = result_df.withColumn("transaction_time", col("transaction_time"))
        
        # Add null fields as per SQL
        result_df = result_df.withColumn("fees", lit(0)) \
                            .withColumn("currency_to_idr", lit(1)) \
                            .withColumn("price_info", lit(None)) \
                            .withColumn("taxes_and_fees", lit(None)) \
                            .withColumn("taxation_fee", lit(None)) \
                            .withColumn("info", lit(None)) \
                            .withColumn("advanced_order_info", lit(None)) \
                            .withColumn("order_type", lit(None)) \
                            .withColumn("recurring_transaction_id", lit(None)) \
                            .withColumn("user_pocket_id", lit(None))
        
        # Rename transaction_type_final to transaction_type
        result_df = result_df.withColumn("transaction_type", col("transaction_type_final")) \
                            .drop("transaction_type_final")
        
        return result_df
    
    def _process_margin_wallet_side(self, df: DataFrame) -> DataFrame:
        """
        Process margin wallet side perspective (Query 2 logic).
        SQL: CRYPTO MARGIN TRANSFERS - MARGIN WALLET
        """
        self.logger.info("=== Processing margin wallet side perspective ===")
        
        # Select and rename columns to match SQL structure
        result_df = df.select(
            "user_id", "account_id", "id", "transaction_type", "transaction_amount",
            "transaction_time", "internal_status", "partner_id", "client_id",
            "settle_asset_mid_price"
        ).withColumn("asset_type", lit("crypto")) \
         .withColumn("product", lit("USDT")) \
         .withColumn("product_id", lit(10005)) \
         .withColumn("contract_id", lit(None)) \
         .withColumn("contract_name", lit(None)) \
         .withColumn("asset_subtype", lit("crypto_margin_wallet")) \
         .withColumn("currency", lit("USDT")) \
         .withColumn("asset_id", lit(10005))  # USDT asset ID
        
        # Add activity column based on SQL logic
        result_df = result_df.withColumn("activity",
            concat(lit("crypto_margin"), 
                   when(col("transaction_type") == "DEPOSIT", lit("_topup"))
                   .otherwise(lit("_withdrawal")))
        )
        
        # Add quantity (always positive)
        result_df = result_df.withColumn("executed_quantity", col("transaction_amount"))
        
        # Add net_quantity based on SQL logic (opposite of crypto side)
        result_df = result_df.withColumn("net_quantity",
            when(col("transaction_type") == "DEPOSIT", col("transaction_amount"))
            .otherwise(-col("transaction_amount"))
        )
        
        # Add created field
        result_df = result_df.withColumn("created", col("transaction_time"))
        
        # Add status
        result_df = result_df.withColumn("status", col("internal_status"))
        
        # Add transaction_type based on SQL logic (opposite of crypto side)
        result_df = result_df.withColumn("transaction_type_final",
            when(col("transaction_type") == "DEPOSIT", lit("BUY"))
            .otherwise(lit("SELL"))
        )
        
        # Add unit_price and usdt_mid_price
        result_df = result_df.withColumn("executed_unit_price", col("settle_asset_mid_price")) \
                            .withColumn("usdt_mid_price", col("settle_asset_mid_price").cast("decimal(20,8)"))
        
        # Add gtv calculation
        result_df = result_df.withColumn("executed_total_price", 
                                       col("transaction_amount") * col("settle_asset_mid_price"))
        
        # Add net_gtv based on SQL logic (opposite of crypto side)
        result_df = result_df.withColumn("net_gtv",
            when(col("transaction_type") == "DEPOSIT", 
                 col("transaction_amount") * col("settle_asset_mid_price"))
            .otherwise(-col("transaction_amount") * col("settle_asset_mid_price"))
        )
        
        # Add ref_id
        result_df = result_df.withColumn("ref_id", col("id"))
        
        # Add transaction_time
        result_df = result_df.withColumn("transaction_time", col("transaction_time"))
        
        # Add null fields as per SQL
        result_df = result_df.withColumn("fees", lit(0)) \
                            .withColumn("currency_to_idr", lit(1)) \
                            .withColumn("price_info", lit(None)) \
                            .withColumn("taxes_and_fees", lit(None)) \
                            .withColumn("taxation_fee", lit(None)) \
                            .withColumn("info", lit(None)) \
                            .withColumn("advanced_order_info", lit(None)) \
                            .withColumn("order_type", lit(None)) \
                            .withColumn("recurring_transaction_id", lit(None)) \
                            .withColumn("user_pocket_id", lit(None))
        
        # Rename transaction_type_final to transaction_type
        result_df = result_df.withColumn("transaction_type", col("transaction_type_final")) \
                            .drop("transaction_type_final")
        
        return result_df
    
    def _apply_processor_specific_transformations(self, df: DataFrame) -> DataFrame:
        """
        Apply transformations specific to margin wallet transfers.
        Based on SQL lines 2428-2560.
        """
        self.logger.info(f"=== Applying {self._get_source_name()} specific transformations ===")
        
        # Activity column is already set by individual processing methods
        # No additional transformations needed as per SQL logic
        
        return df
