#!/usr/bin/env python3
"""
Crypto Currency PluangCuan Yields Processor

Processes crypto currency yields data according to the SQL logic:
- Activity: 'yields'
- Transaction type: 'BUY'
- Uses yield_quantity as both quantity and net_quantity
- Uses effective_date or transaction_time for created field
- Status filter: 'COMPLETED' only
"""

from pyspark.sql import DataFrame
from pyspark.sql.functions import col, lit, when, coalesce

from .base_crypto_processor import BaseCryptoProcessor


class CryptoYieldsProcessor(BaseCryptoProcessor):
    """Processor for crypto currency PluangCuan yields."""
    
    def __init__(self, spark, config, t_1, h_1):
        super().__init__(spark, config, t_1, h_1)
        self.asset_type = "crypto_currency"
        self.source_name = "crypto_currency_pluangcuan_yields"
    
    def _get_source_name(self) -> str:
        """Return the source name for this processor."""
        return self.source_name
    
    def _get_base_transaction_data(self) -> DataFrame:
        """Read and process crypto yields data."""
        self.logger.info(f"=== Reading {self.source_name} ===")
        
        # Build S3 path
        s3_path = f"{self.transactions_snapshot_path}crypto_currency_pluangcuan_yields/dt={self.t_1}/"
        
        # Use error handling helper from base class
        crypto_yields_txn = self._read_data_with_error_handling(s3_path, self.source_name, "crypto_currency_pluangcuan_yields")
        
        if crypto_yields_txn is None:
            self.logger.info(f"No {self.source_name} data found for date {self.t_1}")
            return None
        
        # Check if DataFrame is empty
        row_count = crypto_yields_txn.count()
        if row_count == 0:
            self.logger.warning(f"Empty DataFrame for {self.source_name}")
            return None
        
        # Extract nested data from 'value' key if present
        if "value" in crypto_yields_txn.columns:
            crypto_yields_txn = crypto_yields_txn.select(col("value.*"))
        
        self.logger.info(f"Raw {self.source_name} count before filtering: {crypto_yields_txn.count()}")
        
        # Apply status filter - only COMPLETED yields as per SQL
        if self.asset_config.get("status_filter"):
            crypto_yields_txn = crypto_yields_txn.filter(
                col("status").isin(self.asset_config["status_filter"])
            )
        else:
            # Default filter for yields: only COMPLETED
            crypto_yields_txn = crypto_yields_txn.filter(col("status") == "COMPLETED")
        
        self.logger.info(f"{self.source_name} count after status filter: {crypto_yields_txn.count()}")
        
        if crypto_yields_txn.count() == 0:
            self.logger.info(f"No {self.source_name} data after filtering")
            return None
        
        # Select and rename columns to match SQL structure
        crypto_yields_txn = crypto_yields_txn.select(
            col("crypto_currency_id").alias("asset_id"),
            "account_id", "user_id", col("id").alias("transaction_id"),
            # Use effective_date or transaction_time for created (as per SQL logic)
            coalesce(
                when(col("transaction_time").isNotNull(), col("transaction_time")),
                col("effective_date")
            ).alias("created"),
            "updated", 
            col("yield_quantity").alias("executed_quantity"),  # yield_quantity as quantity
            col("unit_price").alias("executed_unit_price"),
            (col("yield_quantity") * col("unit_price")).alias("executed_total_price"),
            lit(0).alias("fees"),  # Yields typically have no fees
            lit("BUY").alias("transaction_type"),  # As per SQL: yields are 'BUY'
            "status", 
            lit(1).alias("currency_to_idr"),
            # Use transaction_time as transaction_time (as per SQL comment)
            col("transaction_time").alias("transaction_time"), 
            "partner_id",
            col("client_id").cast("string")  # Ensure client_id is string
        ).withColumn("asset_sub_type", lit("crypto")) \
         .withColumn("price_info", lit(None)) \
         .withColumn("taxes_and_fees", lit(None)) \
         .withColumn("taxation_fee", lit(None))  # Yields don't have these fields
        
        return crypto_yields_txn
    
    def _apply_processor_specific_transformations(self, df: DataFrame) -> DataFrame:
        """Apply yields-specific transformations."""
        self.logger.info("=== Applying crypto_yields specific transformations ===")
        
        # Override net_quantity to be same as executed_quantity (yield_quantity)
        # As per SQL: yield_quantity AS net_quantity
        df = df.withColumn("net_quantity", col("executed_quantity"))
        
        # Override gtv to be NULL for yields as per SQL
        df = df.withColumn("gtv", lit(None))
        
        # Override net_gtv to be NULL for yields as per SQL
        df = df.withColumn("net_gtv", lit(None))
        
        # Override spread_revenue - NULL for yields as per SQL
        df = df.withColumn("spread_revenue", lit(None))
        
        # Yields have NULL fee_revenue as per SQL
        df = df.withColumn("fee_revenue", lit(None))
        
        # Yields have NULL currency as per SQL
        df = df.withColumn("currency", lit(None))
        
        # Yields have NULL tax_revenue as per SQL
        df = df.withColumn("tax_revenue", lit(None))
        
        # Yields don't have user_pocket_id
        df = df.withColumn("user_pocket_id", lit(None))
        
        # Yields don't have recurring_transaction_id
        df = df.withColumn("recurring_transaction_id", lit(None))
        
        # Set activity to 'yields' as per SQL
        df = df.withColumn("activity", lit("yields"))
        
        # taxes_and_fees and taxation_fee already added in base data selection
        
        # transaction_time already set in base data selection
        
        return df
