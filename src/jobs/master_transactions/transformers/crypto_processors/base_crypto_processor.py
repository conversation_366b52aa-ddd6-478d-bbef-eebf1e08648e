"""
Base Crypto Transaction Processor
Contains shared logic for all crypto transaction types.
"""

from typing import Dict, List, Any, Optional
from abc import abstractmethod
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, lit, when, coalesce, round as spark_round, get_json_object, row_number, desc, asc, abs, from_unixtime, date_format
from pyspark.sql.window import Window
from ..base_transaction_processor import BaseTransactionProcessor
from ..asset_config import AssetConfigManager, AssetType


class BaseCryptoProcessor(BaseTransactionProcessor):
    """
    Base processor for all crypto transaction types.
    Contains shared crypto logic like price joining, common transformations, etc.
    """
    
    def __init__(self, spark, config, t_1, h_1):
        # Initialize io_utils and ops
        from src.utils.io_utils import IOUtils
        from src.utils.operations import Operations
        
        io_utils = IOUtils(spark, config)
        ops = Operations(spark)
        
        # Pass t_1 and h_1 as keyword arguments to match base class signature
        super().__init__(config, spark, io_utils, ops, t_1=t_1, h_1=h_1)
        self.asset_type = "crypto_currency"
        self.asset_config = self._get_asset_config()
        self.logger.info(f"Initialized {self.__class__.__name__} for asset type: {self.asset_config['asset_type']}")
    
    def _get_asset_config(self) -> Dict[str, Any]:
        """Get crypto-specific configuration from centralized asset config."""
        config_manager = AssetConfigManager()
        asset_config = config_manager.get_config(AssetType.CRYPTO_CURRENCY.value)
        
        # Convert AssetConfig dataclass to dictionary
        return {
            "asset_type": asset_config.asset_type,
            "asset_sub_type": asset_config.asset_sub_type,
            "leverage": asset_config.leverage,
            "status_filter": asset_config.status_filter,
            "transaction_type_filter": asset_config.transaction_type_filter,
            "partner_id_filter": asset_config.partner_id_filter,
            "transaction_sources": asset_config.transaction_sources,
            "price_join_key": asset_config.price_join_key,
            "currency_conversion_rate": asset_config.currency_conversion_rate,
            "special_transformations": asset_config.special_transformations,
            # Deduplication configuration
            "dedup_keys": asset_config.dedup_keys,
            "dedup_order_by": asset_config.dedup_order_by,
            "dedup_order_desc": asset_config.dedup_order_desc,
            "source_dedup_config": asset_config.source_dedup_config,
            # Financial calculation constants
            "effective_spread_rate": asset_config.effective_spread_rate,
            "spread_divisor": asset_config.spread_divisor,
            "buy_tax_rate": asset_config.buy_tax_rate,
            "sell_tax_rate": asset_config.sell_tax_rate
        }
    
    def _join_with_prices(self, df: DataFrame, price_df: Optional[DataFrame] = None, advanced_price_df: Optional[DataFrame] = None) -> DataFrame:
        """
        Crypto-specific price joining logic.
        Splits transactions into two groups:
        1. Non-advanced orders: Left join with price_df (CSV prices)
        2. Advanced orders: Right join with advanced_price_df (JSON prices) using orderId + assetId
        3. Union both results back together
        """
        self.logger.info(f"=== {self.__class__.__name__} Crypto Price Joining ===")
        
        # Derive is_advanced_order flag if advanced_order_info exists
        if "advanced_order_info" in df.columns:
            df = df.withColumn("is_advanced_order", col("advanced_order_info").isNotNull())
        else:
            df = df.withColumn("is_advanced_order", lit(False))
        
        # Split transactions into two groups
        non_advanced_df = df.filter(col("is_advanced_order") == False)
        advanced_df = df.filter(col("is_advanced_order") == True)
        
        self.logger.info(f"Non-advanced transactions: {non_advanced_df.count()}")
        self.logger.info(f"Advanced transactions: {advanced_df.count()}")
        
        # Process non-advanced orders with left join to price_df
        if price_df is not None and non_advanced_df.count() > 0:
            try:
                self.logger.info("Processing non-advanced orders with left join to price_df...")
                qnc_count = price_df.count()
                self.logger.info(f"Price_df records: {qnc_count}")
                
                if qnc_count > 0:
                    # Select required columns and alias to avoid collisions
                    qnc_cols = price_df.columns
                    if "asset_id" in qnc_cols and "current_unit_price" in qnc_cols:
                        qnc_sel = price_df.select(
                            col("asset_id").alias("qnc_asset_id"),
                            col("current_unit_price").alias("current_unit_price_qnc")
                        )
                        non_advanced_df = non_advanced_df.join(qnc_sel, non_advanced_df["asset_id"] == qnc_sel["qnc_asset_id"], "left")
                        non_advanced_df = non_advanced_df.drop("qnc_asset_id")
                        
                        # Set current_unit_price for non-advanced orders
                        non_advanced_df = non_advanced_df.withColumn(
                            "current_unit_price",
                            coalesce(col("current_unit_price_qnc"), col("executed_unit_price"))
                        )
                        self.logger.info("✅ Successfully processed non-advanced orders")
                    else:
                        self.logger.warning("Price_df missing required columns")
                        non_advanced_df = non_advanced_df.withColumn("current_unit_price", col("executed_unit_price"))
                else:
                    non_advanced_df = non_advanced_df.withColumn("current_unit_price", col("executed_unit_price"))
            except Exception as e:
                self.logger.warning(f"Non-advanced order processing failed: {str(e)}")
                non_advanced_df = non_advanced_df.withColumn("current_unit_price", col("executed_unit_price"))
        else:
            non_advanced_df = non_advanced_df.withColumn("current_unit_price", col("executed_unit_price"))
        
        # Process advanced orders with right join to advanced_price_df
        if advanced_price_df is not None and advanced_df.count() > 0:
            try:
                self.logger.info("Processing advanced orders with right join to advanced_price_df...")
                adv_count = advanced_price_df.count()
                self.logger.info(f"Advanced_price_df records: {adv_count}")
                
                if adv_count > 0:
                    adv_cols = advanced_price_df.columns
                    # Check for required join keys and price columns
                    has_order_id = "orderId" in adv_cols
                    has_asset_id = "assetId" in adv_cols
                    
                    if has_order_id and has_asset_id:
                        # Prepare advanced price data with required columns
                        adv_select_cols = []
                        if "orderId" in adv_cols:
                            adv_select_cols.append(col("orderId"))
                        if "assetId" in adv_cols:
                            adv_select_cols.append(col("assetId").alias("adv_asset_id"))
                        
                        
                        # Add price column
                        if "price" in adv_cols:
                            adv_select_cols.append(col("price").alias("current_unit_price_adv"))
                        else:
                            self.logger.warning("Advanced prices missing price column")
                            adv_select_cols.append(lit(None).cast("decimal(20,8)").alias("current_unit_price_adv"))
                        
                        # Add quantity and updated_at if available
                        if "quantity" in adv_cols:
                            adv_select_cols.append(col("quantity").alias("adv_quantity"))
                        if "updatedAt" in adv_cols:
                            # Convert Unix timestamp (milliseconds) to datetime with microseconds
                            adv_select_cols.append(
                                date_format(
                                    from_unixtime(col("updatedAt") / 1000),
                                    "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
                                ).alias("adv_updated_at")
                            )
                        
                        adv_sel = advanced_price_df.select(*adv_select_cols)
                        
                        # Right join on orderId (ignoring negative sign) and assetId
                        # Use abs() to ignore negative signs in orderId
                        join_condition = (abs(advanced_df["transaction_id"]) == abs(adv_sel["orderId"])) & (advanced_df["asset_id"] == adv_sel["adv_asset_id"])
                        advanced_df = advanced_df.join(adv_sel, join_condition, "right")
                        advanced_df = advanced_df.drop("adv_asset_id")
                        
                        # Set current_unit_price for advanced orders
                        advanced_df = advanced_df.withColumn(
                            "current_unit_price",
                            coalesce(col("current_unit_price_adv"), col("executed_unit_price"))
                        )
                        
                        # Update executed_unit_price with price from advanced price data
                        advanced_df = advanced_df.withColumn(
                            "executed_unit_price",
                            coalesce(col("current_unit_price_adv"), col("executed_unit_price"))
                        )
                        
                        # Update executed_quantity with adv_quantity from advanced price data
                        if "adv_quantity" in advanced_df.columns:
                            advanced_df = advanced_df.withColumn("executed_quantity", col("adv_quantity"))
                        
                        # Replace created and updated with adv_updated_at from advanced price data
                        if "adv_updated_at" in advanced_df.columns:
                            advanced_df = advanced_df.withColumn("created", col("adv_updated_at"))
                            advanced_df = advanced_df.withColumn("updated", col("adv_updated_at"))
                        self.logger.info("✅ Successfully processed advanced orders")
                    else:
                        self.logger.warning("Advanced prices missing required join keys (orderId, assetId)")
                        advanced_df = advanced_df.withColumn("current_unit_price", col("executed_unit_price"))
                else:
                    advanced_df = advanced_df.withColumn("current_unit_price", col("executed_unit_price"))
            except Exception as e:
                self.logger.warning(f"Advanced order processing failed: {str(e)}")
                advanced_df = advanced_df.withColumn("current_unit_price", col("executed_unit_price"))
        else:
            advanced_df = advanced_df.withColumn("current_unit_price", col("executed_unit_price"))
        
        # Union both results back together
        if non_advanced_df.count() > 0 and advanced_df.count() > 0:
            # Ensure both DataFrames have the same schema before union
            all_columns = set(non_advanced_df.columns) | set(advanced_df.columns)
            for col_name in all_columns:
                if col_name not in non_advanced_df.columns:
                    # Add with appropriate type based on column name
                    if col_name in ["current_unit_price_adv", "adv_updated_at"]:
                        non_advanced_df = non_advanced_df.withColumn(col_name, lit(None).cast("decimal(20,8)"))
                    else:
                        non_advanced_df = non_advanced_df.withColumn(col_name, lit("").cast("string"))
                if col_name not in advanced_df.columns:
                    # Add with appropriate type based on column name
                    if col_name in ["current_unit_price_adv", "adv_updated_at"]:
                        advanced_df = advanced_df.withColumn(col_name, lit(None).cast("decimal(20,8)"))
                    else:
                        advanced_df = advanced_df.withColumn(col_name, lit("").cast("string"))
            
            df = non_advanced_df.union(advanced_df)
            self.logger.info(f"✅ Successfully unioned {non_advanced_df.count()} non-advanced + {advanced_df.count()} advanced transactions")
        elif non_advanced_df.count() > 0:
            df = non_advanced_df
            self.logger.info("✅ Using only non-advanced transactions")
        elif advanced_df.count() > 0:
            df = advanced_df
            self.logger.info("✅ Using only advanced transactions")
        else:
            df = df.withColumn("current_unit_price", col("executed_unit_price"))
            self.logger.warning("No transactions to process")
        
        return df
    
    def _apply_common_transformations(self, df: DataFrame) -> DataFrame:
        """
        Apply common transformations for crypto assets.
        This method is called by the base processor's process_transactions method.
        """
        # First apply the base common transformations (adds asset_type, leverage, net_quantity, etc.)
        df = super()._apply_common_transformations(df)
        
        # Add activity column early in the pipeline (will be overridden by specific processors)
        df = df.withColumn("activity", lit("transaction"))  # Default activity
        
        # Note: crypto-specific transformations are applied later in _apply_asset_specific_transformations
        # to ensure crypto_currencies_df is available for product lookup
        return df
    
    def _apply_crypto_common_transformations(self, df: DataFrame, crypto_currencies_df: DataFrame = None) -> DataFrame:
        """
        Apply transformations common to all crypto sources.
        This includes spread revenue, tax revenue, and other shared calculations.
        """
        # Get financial config with overrides from main config
        crypto_financial_config = self.config.get("crypto_financial_config", {})
        fallback_spread_rate = crypto_financial_config.get("effective_spread_rate", 
                                                          self.asset_config.get("effective_spread_rate", 0.02))
        spread_divisor = crypto_financial_config.get("spread_divisor", 
                                                   self.asset_config.get("spread_divisor", 200.0))
        
        # Add product lookup from crypto currencies
        try:
            if crypto_currencies_df is not None:
                currencies_count = crypto_currencies_df.count()
                self.logger.info(f"Crypto currencies DataFrame provided with {currencies_count} records")
                
                if currencies_count > 0:
                    self.logger.info(f"Joining with crypto currencies to get product symbols (count: {currencies_count})")
                    
                    df = df.join(
                        crypto_currencies_df,
                        df.asset_id == crypto_currencies_df.crypto_currency_id,
                        "left"
                    ).drop("crypto_currency_id")  # Remove duplicate column after join
                    
                    self.logger.info("✅ Successfully joined with crypto currencies")
                else:
                    self.logger.warning("Crypto currencies DataFrame is empty, adding null product column")
                    df = df.withColumn("product", lit("").cast("string"))
            else:
                self.logger.warning("No crypto currencies DataFrame provided, adding null product column")
                df = df.withColumn("product", lit("").cast("string"))
        except Exception as e:
            self.logger.error(f"Error in product lookup: {e}")
            # Add fallback product column
            df = df.withColumn("product", lit("").cast("string"))
        
        # Extract effectiveSpread from price_info JSON field if available
        df = df.withColumn("effective_spread_from_json", 
            when(col("price_info").isNotNull(), 
                get_json_object(col("price_info"), "$.effectiveSpread").cast("double")
            ).otherwise(None)
        )
        
        # Use extracted effectiveSpread or fallback to config value
        df = df.withColumn("effective_spread_rate",
            when(col("effective_spread_from_json").isNotNull(), 
                col("effective_spread_from_json")
            ).otherwise(lit(fallback_spread_rate))
        )
        
        # Add gtv calculation as per SQL: total_price + COALESCE(totalTax, taxation_fee)
        df = df.withColumn("gtv", 
            col("executed_total_price") + coalesce(
                get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                col("taxation_fee")
            )
        )
        
        # Add tax_revenue calculation from taxes_and_fees JSON
        df = df.withColumn("tax_revenue",
            coalesce(
                get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                col("taxation_fee")
            )
        )
        
        # Add spread_revenue calculation (will be overridden by specific processors if needed)
        df = df.withColumn("spread_revenue",
            when(col("transaction_type") == "AIRDROP", None)
            .otherwise(
                col("gtv") * col("effective_spread_rate") / lit(spread_divisor)
            )
        )
        
        # Clean up temporary columns
        df = df.drop("effective_spread_from_json", "effective_spread_rate")
        
        # Add common crypto columns
        df = df.withColumn("current_currency_to_idr", lit(1))
        df = df.withColumn("product_id", col("asset_id"))  # crypto_currency_id as product_id
        df = df.withColumn("currency", lit("IDR"))
        
        # Add contract fields (NULL for crypto)
        df = df.withColumn("contract_id", lit("").cast("string"))
        df = df.withColumn("contract_name", lit("").cast("string"))
        
        # Add USD fields (NULL for now)
        df = df.withColumn("unit_price_usd", lit(None).cast("decimal(20,8)"))
        df = df.withColumn("gtv_usd", lit(None).cast("decimal(20,8)"))
        
        # Add various revenue/cost columns that are typically NULL for crypto
        decimal_null_columns = [
            "trading_margin_usd", "net_trading_margin_usd", "external_fees",
            "overnight_fee_revenue", "overnight_fee_revenue_usd", "commission_revenue",
            "exchange_fee_revenue", "reg_taf_revenue", "promo_cost", "spread_cost",
            "rounding_revenue", "fx_spread_revenue", "installment_revenue",
            "downpayment_revenue", "penalty_revenue", "prorated_hedge_pnl", 
            "prorated_hedge_pnl_usd", "partner_commission", "partner_commission_pct", 
            "broker_fee", "broker_fee_tax", "market_maker_fee", "market_maker_fee_tax",
            "realised_gain", "realised_gain_native"
        ]
        
        string_null_columns = [
            "recurring_transaction_id", "user_pocket_id", "idr2usd_ref_id", 
            "usd2usdmargin_ref_id", "usdmargin2usd_ref_id", "ref_id_hedge", "network"
        ]
        
        for col_name in decimal_null_columns:
            df = df.withColumn(col_name, lit(None).cast("decimal(20,8)"))
            
        for col_name in string_null_columns:
            df = df.withColumn(col_name, lit("").cast("string"))
        
        # is_liquidation will be set by common post-processor based on activity
        
        # Add ref_id (transaction_id as ref_id)
        df = df.withColumn("ref_id", col("transaction_id"))
        
        # Add underlying_asset_price and usdt_mid_price
        df = df.withColumn("underlying_asset_price", lit(None).cast("decimal(20,8)"))
        df = df.withColumn("usdt_mid_price", lit(None).cast("decimal(20,8)"))  # Will be populated by specific processors
        
        # taxes_and_fees field is already available from source data - don't overwrite it
        
        return df
    
    def _apply_asset_specific_transformations(self, df: DataFrame, crypto_currencies_df: Optional[DataFrame] = None) -> DataFrame:
        """
        Apply crypto-specific transformations.
        This calls the shared crypto transformations and then processor-specific ones.
        """
        # Apply shared crypto transformations
        df = self._apply_crypto_common_transformations(df, crypto_currencies_df)
        
        # Apply processor-specific transformations
        df = self._apply_processor_specific_transformations(df)
        
        return df
    
    @abstractmethod
    def _apply_processor_specific_transformations(self, df: DataFrame) -> DataFrame:
        """
        Apply transformations specific to this crypto processor.
        Must be implemented by each specific processor.
        """
        pass
    
    @abstractmethod
    def _get_source_name(self) -> str:
        """
        Get the name of this crypto source for logging and identification.
        Must be implemented by each specific processor.
        """
        pass
    
    def _read_data_with_error_handling(self, s3_path: str, source_name: str, schema_function_name: str) -> Optional[DataFrame]:
        """
        Helper method to read S3 data with graceful error handling for missing paths.
        
        Args:
            s3_path: Full S3 path to read from
            source_name: Name of the source (e.g., 'crypto_currency_transactions')
            schema_function_name: Name of the schema function from master_transactions_schema
        
        Returns:
            DataFrame with data, or empty DataFrame with schema if path not found, or None on other errors
        """
        try:
            self.logger.info(f"Reading from S3 path: {s3_path}")
            df = self.io_utils.read_json_data(s3_path, True, None, False)
            
            if df is not None:
                self.logger.info(f"✅ Successfully read {df.count()} records from {source_name}")
            else:
                self.logger.warning(f"⚠️  No data returned from {source_name}")
            
            return df
            
        except Exception as e:
            error_msg = str(e)
            if "PATH_NOT_FOUND" in error_msg or "Path does not exist" in error_msg:
                self.logger.warning(f"⚠️  Path not found for {source_name}: {error_msg}")
                self.logger.info(f"Creating empty DataFrame with schema for {source_name}")
                
                try:
                    # Import schema for this specific source
                    from src.schema.master_transactions_schema import (
                        get_crypto_currency_transactions_schema,
                        get_crypto_currency_pocket_transactions_schema,
                        get_crypto_currency_mission_rewards_schema,
                        get_crypto_currency_pluangcuan_yields_schema,
                        get_crypto_margin_wallet_transfers_schema
                    )
                    
                    # Map source names to schema functions
                    schema_map = {
                        "crypto_currency_transactions": get_crypto_currency_transactions_schema,
                        "crypto_currency_pocket_transactions": get_crypto_currency_pocket_transactions_schema,
                        "crypto_currency_mission_rewards": get_crypto_currency_mission_rewards_schema,
                        "crypto_currency_pluangcuan_yields": get_crypto_currency_pluangcuan_yields_schema,
                        "crypto_margin_wallet_transfers": get_crypto_margin_wallet_transfers_schema
                    }
                    
                    # Get appropriate schema function
                    if schema_function_name in schema_map:
                        schema_func = schema_map[schema_function_name]
                        empty_df = self.spark.createDataFrame([], schema_func())
                        self.logger.info(f"✅ Created empty DataFrame with schema for {source_name}")
                        return empty_df
                    else:
                        self.logger.error(f"No schema mapping found for {schema_function_name}")
                        return None
                        
                except Exception as schema_error:
                    self.logger.error(f"Failed to create empty DataFrame with schema: {str(schema_error)}")
                    return None
            else:
                # Re-raise if it's a different error
                self.logger.error(f"❌ Unexpected error reading {source_name}: {error_msg}")
                raise
    
    def _get_base_transaction_data(self) -> DataFrame:
        """
        Default implementation - most processors will override this.
        """
        source_name = self._get_source_name()
        self.logger.info(f"=== Processing {source_name} ===")
        
        # Most processors will override this method
        raise NotImplementedError(f"_get_base_transaction_data must be implemented by {self.__class__.__name__}")
    
    def _apply_deduplication(self, df: DataFrame) -> DataFrame:
        """
        Apply source-specific deduplication based on asset configuration.
        Overrides base implementation to use source-specific dedup config.
        """
        source_name = self._get_source_name()
        
        # Try to get source-specific deduplication config first
        source_dedup_config = self.asset_config.get("source_dedup_config", {})
        if source_name in source_dedup_config:
            dedup_config = source_dedup_config[source_name]
            dedup_keys = dedup_config.get("dedup_keys")
            dedup_order_by = dedup_config.get("dedup_order_by")
            dedup_order_desc = dedup_config.get("dedup_order_desc", True)
            self.logger.info(f"Using source-specific deduplication config for {source_name}")
        else:
            # Fall back to general asset-level deduplication config
            dedup_keys = self.asset_config.get("dedup_keys")
            dedup_order_by = self.asset_config.get("dedup_order_by")
            dedup_order_desc = self.asset_config.get("dedup_order_desc", True)
            self.logger.info(f"Using general deduplication config for {source_name}")
        
        # Skip deduplication if not configured
        if not dedup_keys or not dedup_order_by:
            self.logger.info(f"Deduplication not configured for {source_name}, skipping...")
            return df
        
        # Check if required columns exist
        df_columns = df.columns
        missing_keys = [key for key in dedup_keys if key not in df_columns]
        if missing_keys:
            self.logger.warning(f"Deduplication keys {missing_keys} not found in {source_name} DataFrame columns. Skipping deduplication.")
            return df
        
        if dedup_order_by not in df_columns:
            self.logger.warning(f"Deduplication order column '{dedup_order_by}' not found in {source_name} DataFrame. Skipping deduplication.")
            return df
        
        original_count = df.count()
        self.logger.info(f"=== Applying Deduplication for {source_name} ===")
        self.logger.info(f"Original record count: {original_count}")
        self.logger.info(f"Dedup keys: {dedup_keys}")
        self.logger.info(f"Order by: {dedup_order_by} ({'DESC' if dedup_order_desc else 'ASC'})")
        
        # Create window specification
        window_spec = Window.partitionBy(*dedup_keys)
        if dedup_order_desc:
            window_spec = window_spec.orderBy(desc(dedup_order_by))
        else:
            window_spec = window_spec.orderBy(asc(dedup_order_by))
        
        # Add row number and filter to keep only the first (latest) record
        df_with_row_num = df.withColumn("row_num", row_number().over(window_spec))
        df_deduped = df_with_row_num.filter(col("row_num") == 1).drop("row_num")
        
        final_count = df_deduped.count()
        removed_count = original_count - final_count
        self.logger.info(f"Deduplication complete for {source_name}:")
        self.logger.info(f"  - Original records: {original_count}")
        self.logger.info(f"  - Final records: {final_count}")
        self.logger.info(f"  - Removed duplicates: {removed_count}")
        
        return df_deduped
    
    def _get_additional_transaction_sources(self) -> List[DataFrame]:
        """
        Most crypto processors will only have one source, so return empty list by default.
        """
        return []
