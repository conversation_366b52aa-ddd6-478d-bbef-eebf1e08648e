"""
Crypto Pocket Transaction Processor

Processes crypto_currency_pocket_transactions data according to the SQL logic:
# CRYPTO POCKETS section from DA_aggregate_published__detail_all_transactions_daily.sql
"""

from typing import Optional
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, lit, when, coalesce, get_json_object, cast

from .base_crypto_processor import BaseCryptoProcessor


class CryptoPocketProcessor(BaseCryptoProcessor):
    """
    Processes crypto currency pocket transactions.
    
    Based on SQL logic from DA_aggregate_published__detail_all_transactions_daily.sql
    # CRYPTO POCKETS section (lines 965-1060)
    """
    
    def _get_source_name(self) -> str:
        """Return the source name for this processor."""
        return "crypto_currency_pocket_transactions"
    
    def _get_base_transaction_data(self) -> DataFrame:
        """
        Read and process crypto currency pocket transactions data.
        
        Based on SQL:
        FROM {{ ref('pluang_crypto_currency__crypto_currency_pocket_transactions') }} as a
        LEFT JOIN {{ ref('pluang_crypto_currency__crypto_currencies') }} b
        ON a.crypto_currency_id = b.id
        """
        source_name = self._get_source_name()
        self.logger.info(f"=== Reading {source_name} ===")
        
        # Construct S3 path (pocket transactions don't have hour partitions)
        s3_path = f"{self.transactions_snapshot_path}crypto_currency_pocket_transactions/dt={self.t_1}/"
        
        # Use error handling helper from base class
        crypto_pocket_txn = self._read_data_with_error_handling(s3_path, source_name, "crypto_currency_pocket_transactions")
        
        if crypto_pocket_txn is None:
            self.logger.warning(f"No {source_name} data found")
            return None
        
        # Check if DataFrame is empty
        row_count = crypto_pocket_txn.count()
        if row_count == 0:
            self.logger.warning(f"Empty DataFrame for {source_name}")
            return None
        
        # Extract nested data from 'value' key if present
        if "value" in crypto_pocket_txn.columns:
            crypto_pocket_txn = crypto_pocket_txn.select(col("value.*"))
        
        self.logger.info(f"Raw {source_name} count: {crypto_pocket_txn.count()}")
        
        # Apply default filter (no specific status filter in SQL)
        # The SQL doesn't have a WHERE clause, so we process all records
        
        # Select and rename columns to match SQL structure
        crypto_pocket_txn = crypto_pocket_txn.select(
            col("crypto_currency_id").alias("asset_id"),
            "account_id", "user_id", col("id").alias("transaction_id"),
            # Use transaction_time if available, otherwise use created (as per SQL logic)
            when(col("transaction_time").isNotNull(), col("transaction_time")).otherwise(col("created")).alias("created"),
            "updated", 
            col("quantity").alias("executed_quantity"),
            col("unit_price").alias("executed_unit_price"),
            col("total_price").alias("executed_total_price"),
            col("transaction_fee").alias("fees"),
            "transaction_type", "status",
            lit(1).alias("currency_to_idr"),
            col("transaction_time").alias("transaction_time"), 
            "partner_id",
            col("client_id").cast("string"),  # Ensure client_id is string
            # Additional fields needed for calculations
            "taxes_and_fees", "taxation_fee", "price_info",
            # Pocket-specific fields
            "recurring_transaction_id", "user_pocket_id"
        ).withColumn("asset_sub_type", lit("crypto"))
        
        return crypto_pocket_txn
    
    def _apply_processor_specific_transformations(self, df: DataFrame) -> DataFrame:
        """
        Apply pocket-specific transformations.
        
        Based on SQL logic from lines 975-1057:
        - activity: CASE WHEN recurring_transaction_id IS NOT NULL THEN 'transaction_recurring' ELSE 'transaction' END
        - net_quantity: CASE WHEN status IN ('SUCCESS', 'PARTIALLY_FILLED') THEN CASE WHEN transaction_type = "BUY" THEN quantity ELSE -quantity END ELSE NULL END
        - gtv: CASE WHEN transaction_type = "BUY" THEN total_price - COALESCE(totalTax, taxation_fee) WHEN transaction_type = "SELL" THEN total_price + COALESCE(totalTax, taxation_fee) ELSE NULL END
        - net_gtv: Similar logic with status check
        - spread_revenue: Based on effectiveSpread from price_info
        - fee_revenue: transactionFee from taxes_and_fees
        - tax_revenue: totalTax from taxes_and_fees
        - usdt_mid_price: Based on crypto_currency_id and price_info
        """
        self.logger.info("=== Applying crypto_pocket specific transformations ===")
        
        # Set activity based on recurring_transaction_id
        # CASE WHEN recurring_transaction_id IS NOT NULL THEN 'transaction_recurring' ELSE 'transaction' END
        df = df.withColumn("activity",
            when(col("recurring_transaction_id").isNotNull(), lit("transaction_recurring"))
            .otherwise(lit("transaction"))
        )
        
        # Calculate net_quantity
        # CASE WHEN status IN ('SUCCESS', 'PARTIALLY_FILLED') THEN 
        # CASE WHEN transaction_type = "BUY" THEN quantity ELSE -quantity END ELSE NULL END
        df = df.withColumn("net_quantity",
            when(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"]),
                when(col("transaction_type") == "BUY", col("executed_quantity"))
                .otherwise(-col("executed_quantity"))
            ).otherwise(lit(None))
        )
        
        # Calculate gtv
        # CASE WHEN transaction_type = "BUY" THEN total_price - COALESCE(totalTax, taxation_fee)
        # WHEN transaction_type = "SELL" THEN total_price + COALESCE(totalTax, taxation_fee) ELSE NULL END
        df = df.withColumn("gtv",
            when(col("transaction_type") == "BUY",
                col("executed_total_price") - coalesce(
                    get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                    col("taxation_fee").cast("double")
                )
            ).when(col("transaction_type") == "SELL",
                col("executed_total_price") + coalesce(
                    get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                    col("taxation_fee").cast("double")
                )
            ).otherwise(lit(None))
        )
        
        # Calculate net_gtv
        # CASE WHEN status in ('SUCCESS', 'PARTIALLY_FILLED') THEN 
        # CASE WHEN transaction_type = "BUY" THEN total_price - COALESCE(totalTax, taxation_fee)
        # WHEN transaction_type = "SELL" THEN -1*(total_price + COALESCE(totalTax, taxation_fee)) ELSE NULL END END
        df = df.withColumn("net_gtv",
            when(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"]),
                when(col("transaction_type") == "BUY",
                    col("executed_total_price") - coalesce(
                        get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                        col("taxation_fee").cast("double")
                    )
                ).when(col("transaction_type") == "SELL",
                    -1 * (col("executed_total_price") + coalesce(
                        get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                        col("taxation_fee").cast("double")
                    ))
                ).otherwise(lit(None))
            ).otherwise(lit(None))
        )
        
        # Calculate spread_revenue
        # CASE WHEN transaction_type = "BUY" THEN (total_price - COALESCE(totalTax, taxation_fee)) * effectiveSpread / 200
        # WHEN transaction_type = "SELL" THEN (total_price + COALESCE(totalTax, taxation_fee)) * effectiveSpread / 200 ELSE NULL END
        df = df.withColumn("spread_revenue",
            when(col("transaction_type") == "BUY",
                (col("executed_total_price") - coalesce(
                    get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                    col("taxation_fee").cast("double")
                )) * get_json_object(col("price_info"), "$.effectiveSpread").cast("double") / lit(200)
            ).when(col("transaction_type") == "SELL",
                (col("executed_total_price") + coalesce(
                    get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                    col("taxation_fee").cast("double")
                )) * get_json_object(col("price_info"), "$.effectiveSpread").cast("double") / lit(200)
            ).otherwise(lit(None))
        )
        
        # Set fee_revenue from taxes_and_fees
        # COALESCE(CAST(JSON_QUERY(taxes_and_fees, '$.transactionFee') AS NUMERIC), null)
        df = df.withColumn("fee_revenue",
            coalesce(
                get_json_object(col("taxes_and_fees"), "$.transactionFee").cast("double"),
                lit(None)
            )
        )
        
        # Set tax_revenue from taxes_and_fees
        # COALESCE(CAST(JSON_QUERY(taxes_and_fees, '$.totalTax') as NUMERIC), taxation_fee)
        df = df.withColumn("tax_revenue",
            coalesce(
                get_json_object(col("taxes_and_fees"), "$.totalTax").cast("double"),
                col("taxation_fee").cast("double")
            )
        )
        
        # Calculate usdt_mid_price
        # CAST(CASE WHEN crypto_currency_id = 10005 THEN JSON_EXTRACT_SCALAR(price_info, '$.exchangeMidPriceByOrderBook') 
        # ELSE JSON_EXTRACT_SCALAR(price_info, '$.usdtMidPriceByOrderBook') END AS NUMERIC)
        df = df.withColumn("usdt_mid_price",
            when(col("asset_id") == "10005",
                get_json_object(col("price_info"), "$.exchangeMidPriceByOrderBook").cast("double")
            ).otherwise(
                get_json_object(col("price_info"), "$.usdtMidPriceByOrderBook").cast("double")
            )
        )
        
        # Set currency to IDR as per SQL
        df = df.withColumn("currency", lit("IDR"))
        
        # Set user_pocket_id as string (CAST(user_pocket_id AS STRING))
        df = df.withColumn("user_pocket_id", col("user_pocket_id").cast("string"))
        
        return df