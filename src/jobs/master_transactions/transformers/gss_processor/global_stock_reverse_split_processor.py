from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit

from src.utils.custom_logger import get_logger
from src.utils.io_utils import IOUtils


class GlobalStockReverseSplitProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.spark = spark
        self.io_utils = IOUtils(self.spark, self.config)
        self.transactions_snapshot_path = self.config.get(
            "transactions_snapshot_path",
            "s3a://pluang-datalake-calculated-staging/raw_data/",
        )

    def _read(self, table: str) -> Optional[DataFrame]:
        path = f"{self.transactions_snapshot_path}{table}/dt={self.t_1}/"
        try:
            df = self.io_utils.read_json_data(path, True, None, False)
            return df
        except Exception:
            self.logger.info(f"No {table} path found for dt={self.t_1}; skipping")
            return None

    def _read_stocks(self) -> Optional[DataFrame]:
        return self._read("global_stocks")

    def process(self) -> Optional[DataFrame]:
        a = self._read("global_stock_reverse_stock_split_transactions")
        if a is None:
            return None
        b = self._read_stocks()

        joined = a.alias("a")
        if b is not None:
            joined = joined.join(b.alias("b"), col("a.global_stock_id") == col("b.id"), "left")

        cols = set(joined.columns)
        user_id = col("a.user_id") if "user_id" in cols else lit(None)
        account_id = col("a.account_id") if "account_id" in cols else lit(None)
        quantity = col("a.quantity") if "quantity" in cols else lit(None)
        tx_date = col("a.transaction_date") if "transaction_date" in cols else col("a.created") if "created" in cols else lit(None)

        df = joined.select(
            user_id.alias("user_id"),
            account_id.alias("account_id"),
            lit("gss").alias("asset_type"),
            (col("b.company_code") if b is not None else lit(None)).alias("product"),
            col("a.global_stock_id").alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("gss").alias("asset_subtype"),
            lit("reverse_stock_split").alias("activity"),
            quantity.cast("string").alias("quantity"),
            quantity.cast("string").alias("net_quantity"),
            tx_date.alias("created"),
            lit("SUCCESS").alias("status"),
            lit("reverse_stock_split").alias("transaction_type"),
            lit(None).alias("partner_id"),
            lit(None).alias("client_id"),
            lit(None).alias("currency"),
            lit(None).alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            lit(None).alias("gtv"),
            lit(None).alias("gtv_usd"),
            lit(None).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("a.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            lit(None).cast("string").alias("transaction_time"),
        )
        return df


