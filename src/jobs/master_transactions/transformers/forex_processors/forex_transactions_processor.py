from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, when, coalesce, get_json_object

from src.utils.custom_logger import get_logger
from .common import ForexCommonUtils


class ForexTransactionsProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.utils = ForexCommonUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        ft = self.utils.read("forex_transactions", self.t_1)
        if ft is None:
            return None
        fpp = self.utils.read_partner_prices(self.t_1)
        mp = self.utils.read_manual_patch(self.t_1)

        # Join partner prices (on id)
        ft = self.utils.join(ft.alias("ft"), fpp.alias("fpp"), col("ft.partner_price_id") == col("fpp.ID"))
        # Join manual patch on date(updated) ~ day if present (approximate by string prefix)
        if mp is not None:
            # Expect mp.day format 'YYYY-MM-DD', ft.updated ISO string; match prefix
            ft = ft.join(mp.alias("mp"), ft["updated"].substr(1, 10) == col("mp.day"), "left")

        # Compute partner mid price
        fpp_mid = coalesce(col("fpp.mid_price"), (col("fpp.sell_price") + col("fpp.buy_back_price")) / lit(2))
        # Mid price for spread using manual patch threshold when available
        mid_ref = when(coalesce(fpp_mid, lit(0)) < col("mp.overwrite_threshold"), col("mp.mid_price_idr")).otherwise(coalesce(fpp_mid, (col("fpp.sell_price") + col("fpp.buy_back_price")) / lit(2))) if mp is not None else coalesce(fpp_mid, (col("fpp.sell_price") + col("fpp.buy_back_price")) / lit(2))

        vat = get_json_object(col("ft.info"), "$.vat").cast("double")

        df = ft.select(
            col("ft.user_id").alias("user_id"),
            col("ft.account_id").alias("account_id"),
            lit("fx").alias("asset_type"),
            lit("USD").alias("product"),
            col("ft.forex_id").alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("fx").alias("asset_subtype"),
            lit("transaction").alias("activity"),
            col("ft.quantity").cast("string").alias("quantity"),
            when(col("ft.transaction_type") == "BUY", col("ft.quantity")).otherwise(-(col("ft.quantity"))).cast("string").alias("net_quantity"),
            coalesce(col("ft.transaction_time"), col("ft.created")).alias("created"),
            col("ft.status").alias("status"),
            col("ft.transaction_type").alias("transaction_type"),
            col("ft.partner_id").alias("partner_id"),
            col("ft.client_id").alias("client_id"),
            lit("IDR").alias("currency"),
            col("ft.unit_price").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            when(col("ft.transaction_type") == "BUY", col("ft.total_price") - coalesce(col("ft.fee"), lit(0))).otherwise(col("ft.total_price") + coalesce(col("ft.fee"), lit(0))).alias("gtv"),
            lit(None).alias("gtv_usd"),
            when(col("ft.status") == "SUCCESS", when(col("ft.transaction_type") == "BUY", col("ft.total_price") - coalesce(col("ft.fee"), lit(0))).otherwise(-(col("ft.total_price") + coalesce(col("ft.fee"), lit(0))))).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            when(col("ft.status") == "SUCCESS",
                 when(col("ft.transaction_type") == "BUY", (col("ft.unit_price") - mid_ref) * col("ft.quantity"))
                 .otherwise((-col("ft.unit_price") + mid_ref) * col("ft.quantity"))
            ).alias("spread_revenue"),
            (coalesce(col("ft.fee"), lit(0)) - coalesce(vat, lit(0))).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            vat.alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            when(col("ft.status") == "SUCCESS", col("ft.total_price") - (col("ft.unit_price") * col("ft.quantity"))).alias("rounding_revenue"),
            when(col("ft.status") == "SUCCESS",
                 when(col("ft.transaction_type") == "BUY", (col("ft.unit_price") - mid_ref) * col("ft.quantity"))
                 .otherwise((-col("ft.unit_price") + mid_ref) * col("ft.quantity"))
            ).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("ft.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            col("ft.info").alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("ft.transaction_time").alias("transaction_time"),
        )
        return df


