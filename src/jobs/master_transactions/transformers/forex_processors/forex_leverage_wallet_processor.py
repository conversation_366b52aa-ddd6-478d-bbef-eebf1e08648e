from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, when, concat

from src.utils.custom_logger import get_logger
from .common import ForexCommonUtils


class ForexLeverageWalletProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.utils = ForexCommonUtils(spark, config)

    def _build_transactions(self, a: DataFrame) -> DataFrame:
        activity = concat(lit("fx_used_leverage_wallet"), when(col("wallet_transaction_type") == "BUY", lit("_topup")).otherwise(lit("_cashout")))
        df = a.select(
            col("user_id").alias("user_id"),
            col("account_id").alias("account_id"),
            lit("fx").alias("asset_type"),
            lit("USD").alias("product"),
            lit(10000).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("fx").alias("asset_subtype"),
            activity.alias("activity"),
            col("quantity").alias("quantity"),
            when(col("wallet_transaction_type") == "SELL", col("quantity")).otherwise(-col("quantity")).alias("net_quantity"),
            when(col("transaction_time").isNull(), col("created")).otherwise(col("transaction_time")).alias("created"),
            col("status").alias("status"),
            col("wallet_transaction_type").alias("transaction_type"),
            col("partner_id").alias("partner_id"),
            col("client_id").alias("client_id"),
            lit("USD").alias("currency"),
            col("unit_price_in_idr").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            col("total_price_in_idr").alias("gtv"),
            lit(None).alias("gtv_usd"),
            when(col("wallet_transaction_type") == "BUY", -col("total_price_in_idr")).otherwise(col("total_price_in_idr")).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            when(col("transaction_time").isNull(), col("created")).otherwise(col("transaction_time")).alias("transaction_time"),
        )
        return df

    def _build_balance(self, a: DataFrame) -> DataFrame:
        activity = concat(lit("fx_used_leverage_wallet"), when(col("wallet_transaction_type") == "BUY", lit("_topup")).otherwise(lit("_cashout")))
        df = a.select(
            col("user_id").alias("user_id"),
            col("account_id").alias("account_id"),
            lit("fx").alias("asset_type"),
            lit("USD").alias("product"),
            lit(10000).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("fx_leverage").alias("asset_subtype"),
            activity.alias("activity"),
            col("quantity").alias("quantity"),
            when(col("wallet_transaction_type") == "SELL", col("quantity")).otherwise(-col("quantity")).alias("net_quantity"),
            col("created").alias("created"),
            col("status").alias("status"),
            col("wallet_transaction_type").alias("transaction_type"),
            col("partner_id").alias("partner_id"),
            col("client_id").alias("client_id"),
            lit("USD").alias("currency"),
            col("unit_price_in_idr").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            col("total_price_in_idr").alias("gtv"),
            lit(None).alias("gtv_usd"),
            when(col("wallet_transaction_type") == "BUY", -col("total_price_in_idr")).otherwise(col("total_price_in_idr")).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("created").alias("transaction_time"),
        )
        return df

    def process(self) -> Optional[DataFrame]:
        a = self.utils.read("leverage_wallet_transactions", self.t_1)
        if a is None:
            return None
        tx_df = self._build_transactions(a)
        # Emit only the transactions view to match BigQuery representation
        return tx_df


