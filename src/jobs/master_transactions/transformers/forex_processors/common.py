from typing import Any, Dict, Optional

from pyspark.sql import DataFrame
from pyspark.sql.functions import col

from src.utils.io_utils import IOUtils
from src.utils.custom_logger import get_logger


class ForexCommonUtils:
    def __init__(self, spark, config: Dict[str, Any]):
        self.spark = spark
        self.config = config
        self.logger = get_logger()
        self.io_utils = IOUtils(self.spark, self.config)
        self.base = self.config.get(
            "transactions_snapshot_path",
            "s3a://pluang-datalake-calculated-staging/raw_data/",
        )

    def read(self, name: str, t_1: str) -> Optional[DataFrame]:
        path = f"{self.base}{name}/dt={t_1}/"
        try:
            return self.io_utils.read_json_data(path, True, None, False)
        except Exception:
            self.logger.info(f"Skipping missing path: {path}")
            return None

    def read_partner_prices(self, t_1: str) -> Optional[DataFrame]:
        return self.read("forex_partner_prices", t_1)

    def read_manual_patch(self, t_1: str) -> Optional[DataFrame]:
        # treasury_da__detail_usd_idr_manual_patch
        return self.read("treasury_da_detail_usd_idr_manual_patch", t_1)

    @staticmethod
    def join(df: Optional[DataFrame], other: Optional[DataFrame], on: str, how: str = "left") -> Optional[DataFrame]:
        if df is None:
            return None
        if other is None:
            return df
        return df.join(other, on=on, how=how)


