from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, coalesce, when

from src.utils.custom_logger import get_logger
from .common import ForexCommonUtils


class ForexOptionsProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.utils = ForexCommonUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        a = self.utils.read("options_contract_transactions", self.t_1)
        if a is None:
            return None
        oc = self.utils.read("options_contracts", self.t_1)
        gs = self.utils.read("global_stocks", self.t_1)
        # subset of global_stocks stock_type = 'PALN'
        gs_subset = gs.select("id", "stock_type", "company_code").where(col("stock_type") == "PALN") if gs is not None else None
        joined = a.alias("a")
        if oc is not None:
            joined = joined.join(oc.alias("b"), col("a.options_contract_id") == col("b.id"), "left")
        if gs_subset is not None:
            joined = joined.join(gs_subset.alias("c"), col("a.global_stock_id") == col("c.id"), "left")

        activity = lit("fx_used_options_") + col("c.company_code") if gs_subset is not None else lit("fx_used_options")
        buy_sell = when(col("a.transaction_type").isin("LONG_OPEN", "SHORT_CLOSE"), lit("BUY")).otherwise(lit("SELL"))

        qty = when(col("a.transaction_type").isin("LONG_OPEN", "SHORT_CLOSE"), coalesce(col("a.executed_total_price"), lit(0)) + coalesce(col("a.transaction_fee"), lit(0))) \
            .when(col("a.transaction_type").isin("LONG_CLOSE", "SHORT_OPEN"), coalesce(col("a.executed_total_price"), lit(0)) - coalesce(col("a.transaction_fee"), lit(0)))

        net_qty = when(col("a.transaction_type").isin("LONG_OPEN", "SHORT_CLOSE"), -(coalesce(col("a.executed_total_price"), lit(0)) + coalesce(col("a.transaction_fee"), lit(0)))) \
            .when(col("a.transaction_type").isin("LONG_CLOSE", "SHORT_OPEN"), coalesce(col("a.executed_total_price"), lit(0)) - coalesce(col("a.transaction_fee"), lit(0)))

        df = joined.select(
            col("a.user_id").alias("user_id"),
            col("a.account_id").alias("account_id"),
            lit("fx").alias("asset_type"),
            lit("USD").alias("product"),
            lit(10000).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("fx").alias("asset_subtype"),
            activity.alias("activity"),
            qty.alias("quantity"),
            net_qty.alias("net_quantity"),
            coalesce(col("a.transaction_time"), col("a.updated")).alias("created"),
            col("a.status").alias("status"),
            buy_sell.alias("transaction_type"),
            col("a.partner_id").alias("partner_id"),
            col("a.client_id").alias("client_id"),
            lit("USD").alias("currency"),
            col("a.usd_to_idr").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            lit(None).alias("gtv"),
            lit(None).alias("gtv_usd"),
            when(col("a.transaction_type").isin("LONG_OPEN", "SHORT_CLOSE"), -(coalesce(col("a.executed_total_price"), lit(0)) + coalesce(col("a.transaction_fee"), lit(0))) * col("a.usd_to_idr")) \
            .when(col("a.transaction_type").isin("LONG_CLOSE", "SHORT_OPEN"), (coalesce(col("a.executed_total_price"), lit(0)) - coalesce(col("a.transaction_fee"), lit(0))) * col("a.usd_to_idr")).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("a.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("a.transaction_time").alias("transaction_time"),
        )
        return df


