from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, coalesce

from src.utils.custom_logger import get_logger
from .common import ForexCommonUtils


class ForexLeverageWalletOvernightFeeProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.utils = ForexCommonUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        trx = self.utils.read("leverage_wallet_overnight_fee_transactions", self.t_1)
        if trx is None:
            return None
        rate = self.utils.read("treasury_da_JISDOR_rate_clean", self.t_1)
        joined = trx.alias("trx")
        if rate is not None:
            joined = joined.join(rate.alias("fx"), col("fx.day") == col("trx.updated").substr(1, 10), "left")

        df = joined.select(
            col("trx.user_id").alias("user_id"),
            col("trx.account_id").alias("account_id"),
            lit("fx").alias("asset_type"),
            lit("USD").alias("product"),
            lit(10000).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("fx_leverage").alias("asset_subtype"),
            lit("fx_used_overnight_fee").alias("activity"),
            col("trx.overnight_fee").alias("quantity"),
            (-col("trx.overnight_fee")).alias("net_quantity"),
            coalesce(col("trx.transaction_time"), col("trx.updated")).alias("created"),
            col("trx.status").alias("status"),
            lit("overnight_fee").alias("transaction_type"),
            col("trx.partner_id").alias("partner_id"),
            col("trx.client_id").alias("client_id"),
            lit("USD").alias("currency"),
            lit(None).alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            lit(None).alias("gtv"),
            lit(None).alias("gtv_usd"),
            lit(None).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            (col("trx.overnight_fee") * col("fx.jisdor_rate")).alias("overnight_fee_revenue") if rate is not None else lit(None).alias("overnight_fee_revenue"),
            col("trx.overnight_fee").alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("trx.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("trx.transaction_time").alias("transaction_time"),
        )
        return df


