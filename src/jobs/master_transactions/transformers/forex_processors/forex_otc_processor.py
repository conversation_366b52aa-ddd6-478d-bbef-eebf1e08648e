from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, abs as spark_abs, to_timestamp

from src.utils.custom_logger import get_logger
from .common import ForexCommonUtils


class ForexOTCProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.utils = ForexCommonUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        fx = self.utils.read("fx_otc_treasury_tbl", self.t_1)
        if fx is None:
            return None

        df = fx.select(
            col("user_id").alias("user_id"),
            col("account_id").alias("account_id"),
            lit("fx").alias("asset_type"),
            lit("USD").alias("product"),
            lit(10000).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("fx").alias("asset_subtype"),
            lit("transaction_otc").alias("activity"),
            col("amount_usd").alias("quantity"),
            col("amount_usd").alias("net_quantity").when(col("transaction_type") == "BUY", col("amount_usd")).otherwise(-col("amount_usd")),
        )

        df = fx.select(
            col("user_id").alias("user_id"),
            col("account_id").alias("account_id"),
            lit("fx").alias("asset_type"),
            lit("USD").alias("product"),
            lit(10000).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("fx").alias("asset_subtype"),
            lit("transaction_otc").alias("activity"),
            col("amount_usd").alias("quantity"),
            (col("amount_usd").when(col("transaction_type") == "BUY", col("amount_usd")).otherwise(-col("amount_usd"))).alias("net_quantity"),
            to_timestamp(col("transaction_date")).alias("created"),
            lit("SUCCESSFUL").alias("status"),
            col("transaction_type").alias("transaction_type"),
            lit("1000002").alias("partner_id"),
            lit("3").alias("client_id"),
            lit("USD").alias("currency"),
            col("usd_to_idr").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            col("amount_idr").alias("gtv"),
            lit(None).alias("gtv_usd"),
            (col("amount_idr").when(col("transaction_type") == "BUY", col("amount_idr")).otherwise(-col("amount_idr"))).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            (spark_abs(col("usd_to_idr") - col("mid_price_usd_to_idr")) * col("amount_usd")).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            (spark_abs(col("usd_to_idr") - col("mid_price_usd_to_idr")) * col("amount_usd")).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("dummy_tx_id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            to_timestamp(col("transaction_date")).alias("transaction_time"),
        )
        return df


