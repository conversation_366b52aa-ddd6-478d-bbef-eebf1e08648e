from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, coalesce, concat, when

from src.utils.custom_logger import get_logger
from .common import ForexCommonUtils


class ForexGlobalStockDividendsProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.spark = spark
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.utils = ForexCommonUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        a = self.utils.read("global_stock_dividend_transactions", self.t_1)
        if a is None:
            return None
        gs = self.utils.read("global_stocks", self.t_1)
        b = gs.select(col("id").alias("global_stock_id"), col("company_code")).alias("b") if gs is not None else None
        joined = a.alias("a")
        if b is not None:
            joined = joined.join(b, "global_stock_id", "left")

        activity = concat(lit("dividend_gss_"), col("company_code")) if b is not None else lit("dividend_gss")

        df = joined.select(
            col("a.user_id").alias("user_id"),
            col("a.account_id").alias("account_id"),
            lit("fx").alias("asset_type"),
            lit("USD").alias("product"),
            lit(10000).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            (when(col("a.wallet_type") == "LEVERAGE_WALLET", lit("fx_leverage")).otherwise(lit("fx"))).alias("asset_subtype") if "wallet_type" in a.columns else lit("fx").alias("asset_subtype"),
            activity.alias("activity"),
            col("a.net_amount").alias("quantity"),
            col("a.net_amount").alias("net_quantity"),
            coalesce(col("a.transaction_time"), col("a.created")).alias("created"),
            col("a.dividend_transaction_status").alias("status"),
            lit("BUY").alias("transaction_type"),
            lit("1000002").alias("partner_id"),
            lit("3").alias("client_id"),
            lit("USD").alias("currency"),
            lit(None).alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            lit(None).alias("gtv"),
            lit(None).alias("gtv_usd"),
            lit(None).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("a.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("a.transaction_time").alias("transaction_time"),
        )
        return df


