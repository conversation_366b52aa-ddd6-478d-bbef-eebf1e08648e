"""
Asset Configuration Management
Centralized configuration for all asset types to enable easy addition of new assets.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class AssetType(Enum):
    """Enumeration of supported asset types."""
    CRYPTO_CURRENCY = "crypto_currency"
    GLOBAL_STOCKS = "global_stocks"
    GOLD = "gold"
    FOREX = "forex"
    FUND = "fund"
    STOCK_INDEX = "stock_index"
    OPTIONS = "options"
    CRYPTO_FUTURES = "crypto_futures"


@dataclass
class AssetConfig:
    """Configuration for a specific asset type."""
    asset_type: str
    asset_sub_type: str
    leverage: int = 0
    status_filter: List[str] = None
    transaction_type_filter: List[str] = None
    partner_id_filter: bool = True
    transaction_sources: List[str] = None
    price_join_key: str = "asset_id"
    currency_conversion_rate: Optional[float] = None
    special_transformations: List[str] = None
    custom_columns: List[str] = None
    
    # Deduplication configuration
    dedup_keys: List[str] = None  # Keys to use for deduplication (e.g., ["id"])
    dedup_order_by: str = None  # Column to order by for deduplication (e.g., "updated")
    dedup_order_desc: bool = True  # Order direction (True = DESC for latest, False = ASC for earliest)
    
    # Source-specific deduplication configuration
    source_dedup_config: Dict[str, Dict[str, Any]] = None  # Per-source deduplication settings
    
    # Financial calculation constants
    effective_spread_rate: float = 0.02  # 2% default effective spread
    spread_divisor: float = 200.0  # Divisor for spread calculation (200 basis points)
    buy_tax_rate: float = 0.0011  # 0.11% tax rate for BUY transactions
    sell_tax_rate: float = 0.0010  # 0.10% tax rate for SELL transactions


class AssetConfigManager:
    """Manages configuration for all asset types."""
    
    def __init__(self):
        self._configs = self._initialize_default_configs()
    
    def _initialize_default_configs(self) -> Dict[str, AssetConfig]:
        """Initialize default configurations for all asset types."""
        configs = {}
        
        # Crypto Currency Configuration
        configs[AssetType.CRYPTO_CURRENCY.value] = AssetConfig(
            asset_type="crypto_currency",
            asset_sub_type="crypto_currency_transactions",
            leverage=0,
            status_filter=None, #["SUCCESS", "PARTIALLY_FILLED"],
            transaction_type_filter=None, #["BUY", "SELL", "AIRDROP"],
            partner_id_filter=None, #True,
            transaction_sources=[
                "crypto_currency_transactions",
                "crypto_currency_pocket_transactions",
                "crypto_currency_wallet_transfers",
                "crypto_currency_mission_rewards",
                "crypto_currency_pluangcuan_yields",
                "crypto_margin_wallet_transfers",
            ],
            price_join_key="asset_id",
            currency_conversion_rate=1.0,
            special_transformations=["airdrop_handling", "pocket_transactions", "mission_rewards", "yields", "margin_wallet_transfers"],
            
            # Deduplication configuration for crypto transactions
            dedup_keys=["transaction_id"],  # Deduplicate based on transaction_id column
            dedup_order_by="updated",  # Order by updated timestamp
            dedup_order_desc=True,  # Latest first (DESC order)
            
            # Source-specific deduplication configuration
            source_dedup_config={
                "crypto_currency_transactions": {
                    "dedup_keys": ["transaction_id"],
                    "dedup_order_by": "updated",
                    "dedup_order_desc": True
                },
                "crypto_currency_pocket_transactions": {
                    "dedup_keys": ["transaction_id"],
                    "dedup_order_by": "updated", 
                    "dedup_order_desc": True
                },
                "crypto_currency_wallet_transfers": {
                    "dedup_keys": ["transaction_id"],
                    "dedup_order_by": "updated",
                    "dedup_order_desc": True
                },
                "crypto_currency_mission_rewards": {
                    "dedup_keys": ["transaction_id"],
                    "dedup_order_by": "updated",
                    "dedup_order_desc": True
                },
                "crypto_currency_pluangcuan_yields": {
                    "dedup_keys": ["transaction_id"],
                    "dedup_order_by": "updated",
                    "dedup_order_desc": True
                },
                "crypto_margin_wallet_transfers": {
                    "dedup_keys": ["ref_id"],
                    "dedup_order_by": "transaction_time",
                    "dedup_order_desc": True
                }
            },
            
            # Crypto-specific financial calculation constants
            effective_spread_rate=0.02,  # 2% effective spread for crypto
            spread_divisor=200.0,  # Standard 200 basis points divisor
            buy_tax_rate=0.0011,  # 0.11% tax rate for crypto BUY transactions
            sell_tax_rate=0.0010  # 0.10% tax rate for crypto SELL transactions
        )
        
        # Global Stocks Configuration
        configs[AssetType.GLOBAL_STOCKS.value] = AssetConfig(
            asset_type="global_stocks",
            asset_sub_type="global_stock_transactions",
            leverage=0,  # Will be calculated based on stock_type
            status_filter=["SUCCESS", "PARTIALLY_FILLED"],
            transaction_type_filter=["BUY", "SELL"],
            partner_id_filter=True,
            transaction_sources=[
                "global_stock_transactions",
                "global_stock_merger_transactions"
            ],
            price_join_key="asset_id",
            currency_conversion_rate=None,  # Will be set dynamically
            special_transformations=["leverage_calculation", "merger_handling", "split_adjustment"],
            
            # Deduplication configuration
            dedup_keys=["id"],  # Deduplicate based on transaction id
            dedup_order_by="updated",  # Order by updated timestamp
            dedup_order_desc=True,  # Latest first (DESC order)
            
            # Source-specific deduplication configuration
            source_dedup_config={
                "global_stock_transactions": {
                    "dedup_keys": ["id"],
                    "dedup_order_by": "updated",
                    "dedup_order_desc": True
                },
                "global_stock_merger_transactions": {
                    "dedup_keys": ["id"],
                    "dedup_order_by": "updated",
                    "dedup_order_desc": True
                }
            }
        )
        
        # Gold Configuration
        configs[AssetType.GOLD.value] = AssetConfig(
            asset_type="gold",
            asset_sub_type="gold_transactions",
            leverage=0,
            status_filter=["SUCCESS"],
            transaction_type_filter=["BUY", "SELL"],
            partner_id_filter=True,
            transaction_sources=[
                "gold_transactions",
                "gold_withdrawals"
            ],
            price_join_key="asset_id",
            currency_conversion_rate=1.0,
            special_transformations=["withdrawal_handling"]
        )
        
        # Forex Configuration
        configs[AssetType.FOREX.value] = AssetConfig(
            asset_type="forex",
            asset_sub_type="forex_transactions",
            leverage=0,
            status_filter=["SUCCESS"],
            transaction_type_filter=["BUY", "SELL"],
            partner_id_filter=True,
            transaction_sources=[
                "forex_transactions",
                "forex_top_ups",
                "forex_cash_outs"
            ],
            price_join_key="asset_id",
            currency_conversion_rate=1.0,
            special_transformations=["topup_handling", "cashout_handling"]
        )
        
        # Fund Configuration
        configs[AssetType.FUND.value] = AssetConfig(
            asset_type="fund",
            asset_sub_type="fund_transactions",
            leverage=0,
            status_filter=["APPROVED"],
            transaction_type_filter=["BUY", "SELL"],
            partner_id_filter=True,
            transaction_sources=["fund_transactions"],
            price_join_key="asset_id",
            currency_conversion_rate=1.0,
            special_transformations=[]
        )
        
        # Stock Index Configuration
        configs[AssetType.STOCK_INDEX.value] = AssetConfig(
            asset_type="stock_index",
            asset_sub_type="stock_index_transactions",
            leverage=0,
            status_filter=["SUCCESS"],
            transaction_type_filter=["BUY", "SELL"],
            partner_id_filter=False,  # May have different partner requirements
            transaction_sources=["stock_index_transactions"],
            price_join_key="asset_id",
            currency_conversion_rate=None,  # Will be set dynamically
            special_transformations=["migration_handling"]
        )
        
        # Options Configuration
        configs[AssetType.OPTIONS.value] = AssetConfig(
            asset_type="global_stock_options",
            asset_sub_type="options_contract_transactions",
            leverage=0,
            status_filter=["SUCCESS", "PARTIALLY_FILLED"],
            transaction_type_filter=["LONG_OPEN", "LONG_CLOSE"],
            partner_id_filter=True,
            transaction_sources=["options_contract_transactions"],
            price_join_key="asset_id",
            currency_conversion_rate=None,  # Will be set dynamically
            special_transformations=["shares_per_contract_calculation"]
        )
        
        # Crypto Futures Configuration
        configs[AssetType.CRYPTO_FUTURES.value] = AssetConfig(
            asset_type="crypto_futures",
            asset_sub_type="crypto_future_trades",
            leverage=25,
            status_filter=["SUCCESS"],
            transaction_type_filter=["BUY", "SELL"],
            partner_id_filter=False,
            transaction_sources=[
                "crypto_future_trades",
                "crypto_future_funding_transactions"
            ],
            price_join_key="asset_id",
            currency_conversion_rate=None,  # Will be set dynamically
            special_transformations=["funding_handling", "leverage_calculation"]
        )
        
        return configs
    
    def get_config(self, asset_type: str) -> AssetConfig:
        """Get configuration for a specific asset type."""
        if asset_type not in self._configs:
            raise ValueError(f"No configuration found for asset type: {asset_type}")
        return self._configs[asset_type]
    
    def add_config(self, asset_type: str, config: AssetConfig):
        """Add or update configuration for an asset type."""
        self._configs[asset_type] = config
    
    def get_all_asset_types(self) -> List[str]:
        """Get list of all configured asset types."""
        return list(self._configs.keys())
    
    def get_asset_types_by_category(self, category: str) -> List[str]:
        """Get asset types by category (e.g., 'crypto', 'stocks', 'commodities')."""
        category_mapping = {
            "crypto": [AssetType.CRYPTO_CURRENCY.value, AssetType.CRYPTO_FUTURES.value],
            "stocks": [AssetType.GLOBAL_STOCKS.value, AssetType.STOCK_INDEX.value, AssetType.OPTIONS.value],
            "commodities": [AssetType.GOLD.value],
            "forex": [AssetType.FOREX.value],
            "funds": [AssetType.FUND.value]
        }
        return category_mapping.get(category, [])
    
    def validate_config(self, asset_type: str) -> bool:
        """Validate configuration for an asset type."""
        try:
            config = self.get_config(asset_type)
            required_fields = ["asset_type", "asset_sub_type", "transaction_sources"]
            return all(hasattr(config, field) and getattr(config, field) is not None 
                      for field in required_fields)
        except ValueError:
            return False


class AssetProcessorFactory:
    """Factory for creating asset processors based on configuration."""
    
    def __init__(self, config_manager: AssetConfigManager):
        self.config_manager = config_manager
        self._processor_classes = self._load_processor_classes()
    
    def _load_processor_classes(self) -> Dict[str, type]:
        """Load processor classes dynamically."""
        # Note: crypto_currency processor is now handled by CryptoProcessor
        # which uses the modular crypto processors (crypto_main_processor, crypto_pocket_processor, etc.)
        
        # TODO: Implement other processors
        # from .global_stock_processor import GlobalStockTransactionProcessor
        # from .gold_processor import GoldTransactionProcessor
        # from .forex_processor import ForexTransactionProcessor
        # from .fund_processor import FundTransactionProcessor
        # from .stock_index_processor import StockIndexTransactionProcessor
        # from .options_processor import OptionsTransactionProcessor
        # from .crypto_futures_processor import CryptoFuturesTransactionProcessor
        
        return {
            # AssetType.CRYPTO_CURRENCY.value: CryptoTransactionProcessor,  # Now handled by CryptoProcessor
            # AssetType.GLOBAL_STOCKS.value: GlobalStockTransactionProcessor,
            # AssetType.GOLD.value: GoldTransactionProcessor,
            # AssetType.FOREX.value: ForexTransactionProcessor,
            # AssetType.FUND.value: FundTransactionProcessor,
            # AssetType.STOCK_INDEX.value: StockIndexTransactionProcessor,
            # AssetType.OPTIONS.value: OptionsTransactionProcessor,
            # AssetType.CRYPTO_FUTURES.value: CryptoFuturesTransactionProcessor,
        }
    
    def create_processor(self, asset_type: str, config: dict, spark, io_utils, ops, **kwargs):
        """Create a processor instance for the specified asset type."""
        if asset_type not in self._processor_classes:
            raise ValueError(f"No processor class found for asset type: {asset_type}")
        
        processor_class = self._processor_classes[asset_type]
        return processor_class(config, spark, io_utils, ops, **kwargs)
    
    def get_processor_class(self, asset_type: str) -> type:
        """Get the processor class for an asset type."""
        return self._processor_classes.get(asset_type)
    
    def register_processor(self, asset_type: str, processor_class: type):
        """Register a new processor class for an asset type."""
        self._processor_classes[asset_type] = processor_class
