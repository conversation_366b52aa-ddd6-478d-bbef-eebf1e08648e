from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col

from src.utils.custom_logger import get_logger
from src.utils.io_utils import IOUtils


class CommonMFundUtils:
    def __init__(self, spark: SparkSession, config: Dict[str, Any]):
        self.logger = get_logger()
        self.spark = spark
        self.config = config
        self.io_utils = IOUtils(self.spark, self.config)
        self.transactions_snapshot_path = self.config.get(
            "transactions_snapshot_path",
            "s3a://pluang-datalake-calculated-staging/raw_data/",
        )

    def read(self, table_name: str, dt: str) -> Optional[DataFrame]:
        path = f"{self.transactions_snapshot_path}{table_name}/dt={dt}/"
        try:
            df = self.io_utils.read_json_data(path, True, None, False)
            if df is None:
                self.logger.warning(f"⚠️  No {table_name} found for dt={dt}")
            return df
        except Exception:
            self.logger.info(f"No {table_name} path found for dt={dt}; skipping")
            return None

    def read_funds(self, dt: str) -> Optional[DataFrame]:
        return self.read("funds", dt)

    def read_idr_mid_price_by_day(self, dt: str) -> Optional[DataFrame]:
        """Read forex_master_prices and aggregate avg(mid_price) by day as idr."""
        table = "forex_master_prices"
        path = f"{self.transactions_snapshot_path}{table}/dt={dt}/"
        try:
            df = self.io_utils.read_json_data(path, True, None, False)
            if df is None:
                self.logger.warning(f"⚠️  No {table} found for dt={dt}")
                return None
            # Expect columns: created, mid_price
            from pyspark.sql.functions import to_date, avg

            out = (
                df.select(to_date(col("created")).alias("day"), col("mid_price").cast("double").alias("mid_price"))
                .groupBy("day")
                .agg(avg(col("mid_price")).alias("idr"))
            )
            return out
        except Exception:
            self.logger.info(f"No {table} path found for dt={dt}; skipping idr join")
            return None


