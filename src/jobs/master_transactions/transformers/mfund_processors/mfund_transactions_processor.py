from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, when, to_date, coalesce, get_json_object

from src.utils.custom_logger import get_logger
from .common import CommonMFundUtils


class MFundTransactionsProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.spark = spark
        self.utils = CommonMFundUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        a = self.utils.read("fund_transactions", self.t_1)
        if a is None:
            return None
        b = self.utils.read_funds(self.t_1)
        joined = a.alias("a")
        if b is not None:
            joined = joined.join(b.alias("b"), col("a.fund_id") == col("b.id"), "left")

        # Join IDR mid price by transaction day for USD conversions
        idr = self.utils.read_idr_mid_price_by_day(self.t_1)
        has_idr = idr is not None
        if has_idr:
            joined = joined.join(idr.alias("c"), to_date(col("a.created")) == col("c.day"), "left")

        product_col = col("b.code") if b is not None else lit(None)

        # Compute unit_price and unit_price_usd with/without IDR join
        if has_idr:
            unit_price_expr = when(col("a.currency") == lit("USD"), col("a.unit_price").cast("double") * coalesce(col("c.idr"), lit(0.0))) \
                .otherwise(col("a.unit_price").cast("double"))
            unit_price_usd_expr = when(col("a.currency") == lit("USD"), col("a.unit_price").cast("double")) \
                .otherwise(when(coalesce(col("c.idr"), lit(0.0)) != lit(0.0), (col("a.unit_price").cast("double")/col("c.idr")).cast("double")).otherwise(lit(None)))
        else:
            # Fallback: no IDR conversion available
            unit_price_expr = col("a.unit_price").cast("double")
            unit_price_usd_expr = when(col("a.currency") == lit("USD"), col("a.unit_price").cast("double")).otherwise(lit(None))

        df = joined.select(
            col("a.user_id").alias("user_id"),
            col("a.account_id").alias("account_id"),
            lit("mfund").alias("asset_type"),
            product_col.alias("product"),
            col("a.fund_id").alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("mfund").alias("asset_subtype"),
            lit("mfund_apply").alias("activity"),
            col("a.quantity").cast("string").alias("quantity"),
            lit(None).cast("string").alias("net_quantity"),
            col("a.created").alias("created"),
            col("a.status").alias("status"),
            col("a.transaction_type").alias("transaction_type"),
            col("a.partner_id").alias("partner_id"),
            col("a.client_id").alias("client_id"),
            col("a.currency").alias("currency"),
            unit_price_expr.alias("unit_price"),
            unit_price_usd_expr.alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            lit(None).alias("gtv"),
            lit(None).alias("gtv_usd"),
            lit(None).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            # fee_revenue: fallback to fee if JSON array not handled
            coalesce(get_json_object(col("a.transaction_fee_info"), "$[0].amount").cast("double"), col("a.fee").cast("double")).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            get_json_object(col("a.transaction_fee_info"), "$[1].amount").cast("double").alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("a.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            col("a.transaction_fee_info").alias("taxes_and_fees"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("a.transaction_time").alias("transaction_time"),
        )
        return df


