from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, when, to_date, coalesce

from src.utils.custom_logger import get_logger
from .common import CommonMFundUtils


class MFundSwitchingProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.spark = spark
        self.utils = CommonMFundUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        a = self.utils.read("fund_switching_transactions", self.t_1)
        if a is None:
            return None
        b = self.utils.read_funds(self.t_1)
        joined = a.alias("a")
        if b is not None:
            joined = joined.join(b.alias("b"), col("a.fund_id") == col("b.id"), "left")

        idr = self.utils.read_idr_mid_price_by_day(self.t_1)
        has_idr = idr is not None
        if has_idr:
            joined = joined.join(idr.alias("c"), to_date(col("a.created")) == col("c.day"), "left")

        # Map activity based on status
        activity_col = (
            when(col("a.status") == "APPROVED", lit("mfund_switch_approve"))
            .when(col("a.status") == "FAILED", lit("mfund_switch_fail"))
            .otherwise(lit("mfund_switch_pending_") + col("a.status"))
        )

        created_col = coalesce(col("a.transaction_time"), col("a.updated"))

        # net_quantity only for APPROVED
        net_qty = when(
            col("a.status") == "APPROVED",
            when(col("a.transaction_type") == "SWITCH_IN", col("a.quantity")).otherwise(-col("a.quantity")),
        )

        # GTV/net_gtv only for APPROVED
        if b is not None and has_idr:
            final_price_idr = when(col("b.currency") == lit("USD"), col("a.final_price").cast("double") * coalesce(col("c.idr"), lit(0.0))) \
                .otherwise(col("a.final_price").cast("double"))
        else:
            # Fallback: assume IDR if funds lookup missing
            final_price_idr = col("a.final_price").cast("double")

        gtv = when(col("a.status") == "APPROVED", final_price_idr)
        gtv_usd = when(col("a.status") == "APPROVED",
                       when(col("b.currency") == lit("USD"), col("a.final_price").cast("double"))
                       .otherwise(when(coalesce(col("c.idr"), lit(0.0)) != lit(0.0), (col("a.final_price").cast("double")/col("c.idr")).cast("double")).otherwise(lit(None))))

        net_gtv = when(
            col("a.status") == "APPROVED",
            when(col("a.transaction_type") == "SWITCH_IN", final_price_idr).otherwise(-final_price_idr),
        )

        product_col = col("b.code") if b is not None else lit(None)
        currency_col = (col("b.currency") if b is not None else lit("IDR"))

        df = joined.select(
            col("a.user_id").alias("user_id"),
            col("a.account_id").alias("account_id"),
            lit("mfund").alias("asset_type"),
            product_col.alias("product"),
            col("a.fund_id").alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("mfund").alias("asset_subtype"),
            activity_col.alias("activity"),
            col("a.quantity").cast("string").alias("quantity"),
            net_qty.cast("string").alias("net_quantity"),
            created_col.alias("created"),
            col("a.status").alias("status"),
            when(col("a.transaction_type") == "SWITCH_IN", lit("BUY")).otherwise(lit("SELL")).alias("transaction_type"),
            col("a.partner_id").alias("partner_id"),
            col("a.client_id").alias("client_id"),
            currency_col.alias("currency"),
            (when((currency_col == lit("USD")) & lit(has_idr), col("a.unit_price").cast("double") * coalesce(col("c.idr"), lit(0.0)))
             .otherwise(col("a.unit_price").cast("double"))).alias("unit_price"),
            (when(currency_col == lit("USD"), col("a.unit_price").cast("double"))
             .otherwise(when(lit(has_idr) & (coalesce(col("c.idr"), lit(0.0)) != lit(0.0)), (col("a.unit_price").cast("double")/col("c.idr")).cast("double")).otherwise(lit(None)))).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            gtv.alias("gtv"),
            gtv_usd.alias("gtv_usd"),
            net_gtv.alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("a.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("a.transaction_time").alias("transaction_time"),
        )
        return df


