from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, when, coalesce, get_json_object, row_number
from pyspark.sql.window import Window

from src.utils.custom_logger import get_logger
from .common import CommonIndoStocksUtils


class IndoStocksLimitHistoryProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.spark = spark
        self.utils = CommonIndoStocksUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        hist = self.utils.read("indo_stock_transaction_history", self.t_1)
        if hist is None:
            return None
        stocks = self.utils.read_stocks(self.t_1)

        a = hist.select(
            col("user_id"),
            col("account_id"),
            col("stock_id"),
            get_json_object(col("to_state"), "$.orderType").alias("order_type"),
            get_json_object(col("to_state"), "$.executedQuantity").cast("double").alias("executed_quantity"),
            get_json_object(col("to_state"), "$.orderedQuantity").cast("double").alias("ordered_quantity"),
            col("created").alias("updated"),
            get_json_object(col("to_state"), "$.userStatus").alias("user_status"),
            get_json_object(col("to_state"), "$.transactionType").alias("transaction_type"),
            get_json_object(col("to_state"), "$.totalPrice").cast("double").alias("total_price"),
            get_json_object(col("to_state"), "$.averagePrice").cast("double").alias("average_price"),
            get_json_object(col("to_state"), "$.id").cast("string").alias("id"),
        )

        w = Window.partitionBy("user_id", "account_id", "stock_id", "id", "order_type", "user_status", "transaction_type").orderBy(col("updated").desc())
        a = a.withColumn("rn", row_number().over(w)).filter(col("rn") == 1).drop("rn")

        joined = self.utils.join_stocks(a, stocks)
        if joined is None:
            return None

        df = joined.select(
            col("a.user_id").alias("user_id"),
            col("a.account_id").alias("account_id"),
            lit("idss").alias("asset_type"),
            col("b.code").alias("product"),
            col("a.stock_id").alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("idss").alias("asset_subtype"),
            when(col("a.user_status").isin("SUCCESSFUL", "PARTIALLY_FILLED"), lit("limit_transaction_filled")).otherwise(lit("limit_transaction_cancelled")).alias("activity"),
            when(coalesce(col("a.executed_quantity"), lit(0)) == lit(0), col("a.ordered_quantity") * lit(100)).otherwise(col("a.executed_quantity") * lit(100)).cast("string").alias("quantity"),
            when(col("a.transaction_type") == "BUY", col("a.executed_quantity") * lit(100)).otherwise(-(col("a.executed_quantity") * lit(100))).cast("string").alias("net_quantity"),
            col("a.updated").alias("created"),
            col("a.user_status").alias("status"),
            col("a.transaction_type").alias("transaction_type"),
            lit(None).alias("partner_id"),
            lit(None).alias("client_id"),
            lit("IDR").alias("currency"),
            col("a.average_price").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            when(col("a.user_status").isin("SUCCESSFUL", "PARTIALLY_FILLED"), col("a.total_price")).alias("gtv"),
            lit(None).alias("gtv_usd"),
            when(col("a.user_status").isin("SUCCESSFUL", "PARTIALLY_FILLED"), when(col("a.transaction_type") == "BUY", col("a.total_price")).otherwise(-col("a.total_price"))).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            when(coalesce(col("a.executed_quantity"), lit(0)) == lit(0), col("a.total_price") - col("a.ordered_quantity") * lit(100)).otherwise(col("a.total_price") - col("a.executed_quantity") * lit(100)).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("a.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            lit(None).cast("string").alias("transaction_time"),
        )
        return df


