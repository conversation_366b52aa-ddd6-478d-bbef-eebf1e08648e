from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, when

from src.utils.custom_logger import get_logger
from .common import CommonIndoStocksUtils


class IndoStocksWarrantProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.spark = spark
        self.utils = CommonIndoStocksUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        a = self.utils.read("indo_stock_warrant_transactions", self.t_1)
        if a is None:
            return None
        stocks = self.utils.read_stocks(self.t_1)
        joined = self.utils.join_stocks(a, stocks)
        if joined is None:
            return None

        df = joined.select(
            col("user_id").alias("user_id"),
            col("account_id").alias("account_id"),
            lit("idss").alias("asset_type"),
            col("b.code").alias("product"),
            col("stock_id").alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("idss").alias("asset_subtype"),
            lit("warrant").alias("activity"),
            (col("quantity") * lit(100)).cast("string").alias("quantity"),
            when(col("transaction_type") == "EXERCISE_IN", col("quantity") * lit(100)).otherwise(-(col("quantity") * lit(100))).cast("string").alias("net_quantity"),
            col("transaction_date").alias("created"),
            lit("SUCCESS").alias("status"),
            col("transaction_type").alias("transaction_type"),
            lit("1000002").alias("partner_id"),
            lit("21").alias("client_id"),
            lit(None).alias("currency"),
            col("unit_price").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            (col("quantity") * lit(100) * col("unit_price")).alias("gtv"),
            lit(None).alias("gtv_usd"),
            when(col("transaction_type") == "EXERCISE_IN", col("quantity") * lit(100) * col("unit_price")).otherwise(-(col("quantity") * lit(100) * col("unit_price"))).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            lit(None).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            col("transaction_date").cast("string").alias("transaction_time"),
        )
        return df


