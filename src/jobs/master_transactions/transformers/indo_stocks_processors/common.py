from typing import Any, Dict, Optional

from pyspark.sql import DataFrame
from pyspark.sql.functions import col

from src.utils.io_utils import IOUtils
from src.utils.custom_logger import get_logger


class CommonIndoStocksUtils:
    def __init__(self, spark, config: Dict[str, Any]):
        self.spark = spark
        self.config = config
        self.logger = get_logger()
        self.io_utils = IOUtils(self.spark, self.config)
        self.base = self.config.get(
            "transactions_snapshot_path",
            "s3a://pluang-datalake-calculated-staging/raw_data/",
        )

    def read(self, name: str, t_1: str) -> Optional[DataFrame]:
        path = f"{self.base}{name}/dt={t_1}/"
        try:
            return self.io_utils.read_json_data(path, True, None, False)
        except Exception:
            self.logger.info(f"Skipping missing path: {path}")
            return None

    def read_stocks(self, t_1: str) -> Optional[DataFrame]:
        return self.read("indo_stocks", t_1)

    @staticmethod
    def join_stocks(df: Optional[DataFrame], stocks: Optional[DataFrame]) -> Optional[DataFrame]:
        if df is None:
            return None
        if stocks is None:
            return df
        return df.alias("a").join(stocks.alias("b"), col("a.stock_id") == col("b.id"), "left")


