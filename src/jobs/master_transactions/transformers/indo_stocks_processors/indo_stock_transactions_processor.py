from typing import Any, Dict, Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, when, coalesce, get_json_object

from src.utils.custom_logger import get_logger
from .common import CommonIndoStocksUtils


class IndoStocksTransactionsProcessor:
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str, spark: SparkSession):
        self.logger = get_logger()
        self.config = config
        self.t_1 = t_1
        self.h_1 = h_1 or t_1
        self.spark = spark
        self.utils = CommonIndoStocksUtils(spark, config)

    def process(self) -> Optional[DataFrame]:
        a = self.utils.read("indo_stock_transactions", self.t_1)
        if a is None:
            return None
        stocks = self.utils.read_stocks(self.t_1)

        order_type = coalesce(col("order_type"), get_json_object(col("to_state"), "$.orderType"))
        executed_qty = coalesce(col("executed_quantity"), get_json_object(col("to_state"), "$.executedQuantity")).cast("double")
        ordered_qty = coalesce(col("ordered_quantity"), get_json_object(col("to_state"), "$.orderedQuantity")).cast("double")
        user_status = coalesce(col("user_status"), get_json_object(col("to_state"), "$.userStatus"))
        txn_type = coalesce(col("transaction_type"), get_json_object(col("to_state"), "$.transactionType"))
        total_price = coalesce(col("total_price"), get_json_object(col("to_state"), "$.totalPrice")).cast("double")
        average_price = coalesce(col("average_price"), get_json_object(col("to_state"), "$.averagePrice")).cast("double")

        a = (
            a.withColumn("order_type_x", order_type)
            .withColumn("executed_quantity_x", executed_qty)
            .withColumn("ordered_quantity_x", ordered_qty)
            .withColumn("user_status_x", user_status)
            .withColumn("transaction_type_x", txn_type)
            .withColumn("total_price_x", total_price)
            .withColumn("average_price_x", average_price)
        )

        joined = self.utils.join_stocks(a, stocks)
        if joined is None:
            return None

        df = joined.select(
            col("a.user_id").alias("user_id"),
            col("a.account_id").alias("account_id"),
            lit("idss").alias("asset_type"),
            col("b.code").alias("product"),
            col("a.stock_id").alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("idss").alias("asset_subtype"),
            when(col("order_type_x") == "MARKET", lit("transaction")).otherwise(lit("limit_transaction_created")).alias("activity"),
            when(col("order_type_x") == "MARKET", col("executed_quantity_x") * lit(100)).otherwise(col("ordered_quantity_x") * lit(100)).cast("string").alias("quantity"),
            when(
                col("order_type_x") == "MARKET",
                when(col("transaction_type_x") == "BUY", col("executed_quantity_x") * lit(100)).otherwise(-(col("executed_quantity_x") * lit(100))),
            ).cast("string").alias("net_quantity"),
            col("a.created").alias("created"),
            col("user_status_x").alias("status"),
            col("transaction_type_x").alias("transaction_type"),
            lit(None).alias("partner_id"),
            col("a.client_id").alias("client_id"),
            lit("IDR").alias("currency"),
            col("average_price_x").alias("unit_price"),
            lit(None).alias("unit_price_usd"),
            lit(None).alias("usdt_mid_price"),
            lit(None).alias("underlying_asset_price"),
            when((col("user_status_x").isin("SUCCESSFUL")) & (col("order_type_x") == "MARKET"), col("total_price_x")).alias("gtv"),
            lit(None).alias("gtv_usd"),
            when((col("user_status_x").isin("SUCCESSFUL")) & (col("order_type_x") == "MARKET"),
                 when(col("transaction_type_x") == "BUY", col("total_price_x")).otherwise(-col("total_price_x")),
            ).alias("net_gtv"),
            lit(None).alias("trading_margin_usd"),
            lit(None).alias("net_trading_margin_usd"),
            when(coalesce(col("ordered_quantity_x"), lit(0)) == lit(0), col("total_price_x") - (col("ordered_quantity_x") * lit(100))).otherwise(col("total_price_x") - (col("executed_quantity_x") * lit(100))).alias("external_fees"),
            lit(None).alias("overnight_fee_revenue"),
            lit(None).alias("overnight_fee_revenue_usd"),
            lit(None).alias("spread_revenue"),
            lit(None).alias("fee_revenue"),
            lit(None).alias("commission_revenue"),
            lit(None).alias("exchange_fee_revenue"),
            lit(None).alias("reg_taf_revenue"),
            lit(None).alias("promo_cost"),
            lit(None).alias("tax_revenue"),
            lit(None).alias("spread_cost"),
            lit(None).alias("rounding_revenue"),
            lit(None).alias("fx_spread_revenue"),
            lit(None).alias("installment_revenue"),
            lit(None).alias("downpayment_revenue"),
            lit(None).alias("penalty_revenue"),
            col("a.id").alias("ref_id"),
            lit(None).cast("string").alias("recurring_transaction_id"),
            lit(None).cast("string").alias("user_pocket_id"),
            lit(None).cast("string").alias("idr2usd_ref_id"),
            lit(None).cast("string").alias("usd2usdmargin_ref_id"),
            lit(None).cast("string").alias("usdmargin2usd_ref_id"),
            lit(None).alias("prorated_hedge_pnl"),
            lit(None).alias("prorated_hedge_pnl_usd"),
            lit(None).alias("partner_commission"),
            lit(None).alias("partner_commission_pct"),
            lit(None).alias("broker_fee"),
            lit(None).alias("broker_fee_tax"),
            lit(None).alias("market_maker_fee"),
            lit(None).alias("market_maker_fee_tax"),
            lit(None).cast("string").alias("ref_id_hedge"),
            lit(None).cast("string").alias("network"),
            lit(None).alias("taxes_and_fees"),
            lit(None).alias("realised_gain"),
            lit(None).alias("realised_gain_native"),
            lit(None).cast("boolean").alias("is_liquidation"),
            lit(None).cast("string").alias("transaction_time"),
        )
        return df


