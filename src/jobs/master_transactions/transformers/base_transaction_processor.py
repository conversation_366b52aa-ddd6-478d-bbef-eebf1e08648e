"""
Base Transaction Processor - Abstract base class for all asset transaction processors.
This follows the Template Method pattern to ensure consistent processing across all asset types.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, lit, when, coalesce, round as spark_round, row_number, desc, asc
from pyspark.sql.window import Window
from src.utils.spark_utils import *
from src.utils.io_utils import IOUtils
from src.utils.operations import Operations
from src.utils.custom_logger import init_logger, get_logger


class BaseTransactionProcessor(ABC):
    """
    Abstract base class for processing transactions of different asset types.
    Implements the Template Method pattern to ensure consistent processing flow.
    """
    
    def __init__(self, config: dict, spark: SparkSession, io_utils: IOUtils, ops: Operations, **kwargs):
        self.config = config
        self.spark = spark
        self.io_utils = io_utils
        self.ops = ops
        
        # Initialize logger if not already initialized
        try:
            self.logger = get_logger()
        except RuntimeError:
            init_logger("dev")
            self.logger = get_logger()
        
        # Common configuration
        self.partner_id = config.get("pluang_partner_id")
        self.t_1 = kwargs.get("t_1")
        self.h_1 = kwargs.get("h_1")
        self.transactions_snapshot_path = config.get("transactions_snapshot_path", "").replace("s3://", "s3a://")
        
        # Handle h_1 parameter - use t_1 only if h_1 is not provided
        if self.h_1 is None:
            self.h_1 = self.t_1
        
        # Asset-specific configuration
        self.asset_config = self._get_asset_config()
        
    @abstractmethod
    def _get_asset_config(self) -> Dict[str, Any]:
        """
        Get asset-specific configuration.
        Must be implemented by each asset processor.
        """
        pass
    
    @abstractmethod
    def _get_base_transaction_data(self) -> DataFrame:
        """
        Get the base transaction data for this asset type.
        Must be implemented by each asset processor.
        """
        pass
    
    @abstractmethod
    def _get_additional_transaction_sources(self) -> List[DataFrame]:
        """
        Get additional transaction sources (e.g., wallet transfers, pocket transactions).
        Must be implemented by each asset processor.
        Returns a list of DataFrames to be unioned.
        """
        pass
    
    @abstractmethod
    def _apply_asset_specific_transformations(self, df: DataFrame, crypto_currencies_df: Optional[DataFrame] = None) -> DataFrame:
        """
        Apply asset-specific transformations to the transaction data.
        Must be implemented by each asset processor.
        """
        pass
    
    def _get_common_columns(self) -> List[str]:
        """
        Get the common column structure for all transactions.
        Based on SQL schema from DA_aggregate_published__detail_all_transactions_daily.sql
        Can be overridden by asset processors if needed.
        """
        return [
            # Core transaction fields
            "user_id", "account_id", "asset_type", "product", "product_id", "asset_id",
            "contract_id", "contract_name", "asset_sub_type", "activity",
            "executed_quantity", "net_quantity", "created", "status", "transaction_type",
            "partner_id", "currency", "executed_unit_price", "unit_price_usd",
            "usdt_mid_price", "underlying_asset_price", "current_unit_price",
            
            # Financial calculations
            "gtv", "gtv_usd", "net_gtv", "trading_margin_usd", "net_trading_margin_usd",
            "external_fees", "overnight_fee_revenue", "overnight_fee_revenue_usd",
            "spread_revenue", "fee_revenue", "commission_revenue", "exchange_fee_revenue",
            "reg_taf_revenue", "promo_cost", "tax_revenue", "spread_cost",
            "rounding_revenue", "fx_spread_revenue", "installment_revenue",
            "downpayment_revenue", "penalty_revenue",
            
            # Reference and metadata fields
            "ref_id", "recurring_transaction_id", "user_pocket_id", "idr2usd_ref_id",
            "usd2usdmargin_ref_id", "usdmargin2usd_ref_id", "prorated_hedge_pnl",
            "prorated_hedge_pnl_usd", "partner_commission", "partner_commission_pct",
            "broker_fee", "broker_fee_tax", "market_maker_fee", "market_maker_fee_tax",
            "ref_id_hedge", "network", "taxes_and_fees", "realised_gain",
            "realised_gain_native", "transaction_time",
            
            # System fields
            "leverage", "currency_to_idr", "current_currency_to_idr"
        ]
    
    def _apply_common_transformations(self, df: DataFrame) -> DataFrame:
        """
        Apply common transformations that are shared across all asset types.
        Based on SQL logic from DA_aggregate_published__detail_all_transactions_daily.sql
        """
        
        # Add common columns
        # Map asset_type to the expected values for common post processing
        asset_type_mapping = {
            "crypto_currency": "crypto",
            "stock_index": "stock_index",
            "fx": "fx",
            "gold": "gold"
        }
        expected_asset_type = asset_type_mapping.get(self.asset_config["asset_type"], self.asset_config["asset_type"])
        df = df.withColumn("asset_type", lit(expected_asset_type))
        df = df.withColumn("leverage", lit(self.asset_config.get("leverage", 0)))
        
        # Add net_quantity calculation based on SQL logic
        # CASE WHEN status in ('SUCCESS', 'PARTIALLY_FILLED') THEN 
        #   CASE WHEN transaction_type in ('BUY','AIRDROP') then executed_quantity 
        #        ELSE -executed_quantity END
        # ELSE NULL END
        df = df.withColumn("net_quantity", 
            when(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"]),
                when(col("transaction_type").isin(["BUY", "AIRDROP"]), col("executed_quantity"))
                .otherwise(-col("executed_quantity"))
            ).otherwise(None)
        )
        
        # Add GTV calculation (Gross Transaction Value)
        # Simplified calculation: executed_unit_price * executed_quantity
        # In SQL, this has complex logic with taxes and historical dates, but we'll keep it simple
        df = df.withColumn("gtv", 
            when(col("transaction_type") == "AIRDROP", None)
            .otherwise(col("executed_unit_price") * col("executed_quantity"))
        )
        
        # Add net_gtv calculation based on SQL logic
        # CASE WHEN status in ('SUCCESS', 'PARTIALLY_FILLED') THEN 
        #   CASE WHEN transaction_type in ('BUY','AIRDROP') THEN gtv
        #        ELSE -gtv END
        # ELSE NULL END
        df = df.withColumn("net_gtv",
            when(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"]),
                when(col("transaction_type").isin(["BUY", "AIRDROP"]), col("gtv"))
                .otherwise(-col("gtv"))
            ).otherwise(None)
        )
        
        # Add currency column (typically 'IDR' for crypto)
        df = df.withColumn("currency", lit("IDR"))
        
        # Apply common filters only if values are not None/blank/empty
        status_filter = self.asset_config.get("status_filter")
        self.logger.info(f"DEBUG: status_filter value = {status_filter}, type = {type(status_filter)}")
        if status_filter is not None and isinstance(status_filter, list) and len(status_filter) > 0:
            self.logger.info(f"Applying status filter: {status_filter}")
            df = df.filter(col("status").isin(status_filter))
            self.logger.info(f"After status filter: {df.count()} records")
        else:
            self.logger.info(f"Skipping status filter - no values configured (value: {status_filter})")
        
        transaction_type_filter = self.asset_config.get("transaction_type_filter")
        if transaction_type_filter is not None and isinstance(transaction_type_filter, list) and len(transaction_type_filter) > 0:
            self.logger.info(f"Applying transaction_type filter: {transaction_type_filter}")
            df = df.filter(col("transaction_type").isin(transaction_type_filter))
            self.logger.info(f"After transaction_type filter: {df.count()} records")
        else:
            self.logger.info("Skipping transaction_type filter - no values configured")
        
        partner_id_filter = self.asset_config.get("partner_id_filter")
        if partner_id_filter is True and self.partner_id is not None and self.partner_id != "":
            self.logger.info(f"Applying partner_id filter: {self.partner_id}")
            df = df.filter(col("partner_id") == self.partner_id)
            self.logger.info(f"After partner_id filter: {df.count()} records")
        else:
            self.logger.info("Skipping partner_id filter - not configured or no value")
        
        df.show(5, truncate=False)
        return df
    
    # Price joining hooks - override in subclasses that need specific price data
    def _needs_quote_and_cover_prices(self) -> bool:
        """Override in subclasses that need quote & cover prices."""
        return False

    def _needs_advanced_prices(self) -> bool:
        """Override in subclasses that need advanced order prices."""
        return False

    def _join_with_prices(self,
                          df: DataFrame,
                          price_df: Optional[DataFrame] = None,
                          advanced_price_df: Optional[DataFrame] = None) -> DataFrame:
        """
        Generic price joining logic for all asset types.
        Most asset types will override this method with their specific price joining logic.
        This provides a simple fallback that uses executed_unit_price as current_unit_price.
        """
        self.logger.info("Using generic price joining logic (fallback)")
        
        # Simple fallback: use executed_unit_price as current_unit_price
        # Asset-specific processors should override this method for their price joining needs
        df = df.withColumn("current_unit_price", col("executed_unit_price"))
        
        return df
    
    def _apply_deduplication(self, df: DataFrame) -> DataFrame:
        """
        Apply deduplication based on asset configuration.
        Keeps only the latest record for each unique combination of dedup_keys.
        """
        dedup_keys = self.asset_config.get("dedup_keys")
        dedup_order_by = self.asset_config.get("dedup_order_by")
        dedup_order_desc = self.asset_config.get("dedup_order_desc", True)
        
        # Skip deduplication if not configured
        if not dedup_keys or not dedup_order_by:
            self.logger.info("Deduplication not configured, skipping...")
            return df
        
        # Check if required columns exist
        df_columns = df.columns
        missing_keys = [key for key in dedup_keys if key not in df_columns]
        if missing_keys:
            self.logger.warning(f"Deduplication keys {missing_keys} not found in DataFrame columns. Skipping deduplication.")
            return df
        
        if dedup_order_by not in df_columns:
            self.logger.warning(f"Deduplication order column '{dedup_order_by}' not found in DataFrame. Skipping deduplication.")
            return df
        
        original_count = df.count()
        self.logger.info(f"=== Applying Deduplication ===")
        self.logger.info(f"Original record count: {original_count}")
        self.logger.info(f"Dedup keys: {dedup_keys}")
        self.logger.info(f"Order by: {dedup_order_by} ({'DESC' if dedup_order_desc else 'ASC'})")
        # Create window specification
        order_func = desc(dedup_order_by) if dedup_order_desc else asc(dedup_order_by)
        window_spec = Window.partitionBy(*dedup_keys).orderBy(order_func)
        
        # Add row number and filter to keep only the first row (latest/earliest based on config)
        df_with_row_num = df.withColumn("_row_num", row_number().over(window_spec))
        df_deduped = df_with_row_num.filter(col("_row_num") == 1).drop("_row_num")
        
        final_count = df_deduped.count()
        duplicates_removed = original_count - final_count
        
        self.logger.info(f"Final record count: {final_count}")
        self.logger.info(f"Duplicates removed: {duplicates_removed}")
        
        if duplicates_removed > 0:
            self.logger.info(f"✅ Deduplication completed: removed {duplicates_removed} duplicate records")
        else:
            self.logger.info("ℹ️  No duplicates found")
        
        return df_deduped
    
    def _add_current_currency_conversion(self, df: DataFrame, conversion_rate: float) -> DataFrame:
        """
        Add current currency conversion rate.
        """
        df = df.withColumn("current_currency_to_idr", lit(conversion_rate))
        return df
    
    def _validate_data(self, df: DataFrame) -> DataFrame:
        """
        Validate transaction data and handle null values.
        """
        # Check for null prices
        null_price_assets = self.ops.check_null_values(df, "asset_id", "current_unit_price")
        if len(null_price_assets) > 0:
            self.logger.warning(f"Asset IDs with null current_unit_price: {null_price_assets}")
            df = df.withColumn(
                "current_unit_price",
                when(col("current_unit_price").isNull(), col("executed_unit_price"))
                .otherwise(col("current_unit_price"))
            )
        
        # Check for null quantities
        null_quantity_assets = self.ops.check_null_values(df, "asset_id", "executed_quantity")
        if len(null_quantity_assets) > 0:
            self.logger.warning(f"Asset IDs with null executed_quantity: {null_quantity_assets}")
        
        return df
    
    def process_transactions(self, price_df: Optional[DataFrame] = None, advanced_price_df: Optional[DataFrame] = None, conversion_rate: Optional[float] = None, crypto_currencies_df: Optional[DataFrame] = None) -> DataFrame:
        """
        Main processing method that follows the Template Method pattern.
        This method orchestrates the entire processing flow.
        """
        self.logger.info(f"Processing {self.asset_config['asset_type']} transactions")
        
        # Step 1: Get base transaction data
        self.logger.info("=== Configuration Values ===")
        self.logger.info(f"transactions_snapshot_path: {self.transactions_snapshot_path}")
        self.logger.info(f"partner_id: {self.partner_id}")
        self.logger.info(f"t_1: {self.t_1}")
        self.logger.info(f"h_1: {self.h_1}")
        self.logger.info(f"asset_type: {self.asset_config['asset_type']}")
        self.logger.info(f"DEBUG: Full asset_config = {self.asset_config}")
        
        df = self._get_base_transaction_data()
        if df is not None:
            # Apply deduplication before processing
            df = self._apply_deduplication(df)
            df.show(5, truncate=False)
            self.logger.info(f"Final processed {df.count()} {self.asset_config['asset_type']} transactions")
        else:
            self.logger.warning(f"No base transaction data found for {self.asset_config['asset_type']}")
            return None
        
        # Step 2: Get additional transaction sources and union them
        additional_sources = self._get_additional_transaction_sources()
        for source_df in additional_sources:
            df = self.ops.get_union(df, source_df)
        
        # Step 3: Apply common transformations
        df = self._apply_common_transformations(df)
        
        # Step 4: Apply asset-specific transformations
        df = self._apply_asset_specific_transformations(df, crypto_currencies_df)
        
        # Step 5: Join with prices (conditional based on processor needs)
        df = self._join_with_prices(df, price_df=price_df, advanced_price_df=advanced_price_df)
        
        # Step 6: Add currency conversion if provided
        if conversion_rate is not None:
            df = self._add_current_currency_conversion(df, conversion_rate)
        
        # Step 7: Validate data
        df = self._validate_data(df)
        
        # Step 8: Select final columns
        df = df.select(self._get_common_columns())
        
        self.logger.info(f"Processed {df.count()} {self.asset_config['asset_type']} transactions")
        return df
