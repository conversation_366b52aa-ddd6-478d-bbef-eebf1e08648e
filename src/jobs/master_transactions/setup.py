#!/usr/bin/env python3
"""
Setup script for Master Transactions Processor
Run this from the master_transactions directory to set up the environment.
"""

import sys
import os
import subprocess

def setup_environment():
    """Set up the Python environment for master transactions processing."""
    print("🔧 Setting up Master Transactions Processor environment...")
    
    # Add project root to Python path
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
        print(f"✅ Added project root to Python path: {project_root}")
    
    # Check if requirements.txt exists
    requirements_file = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_file):
        print("📦 Installing requirements...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', requirements_file])
            print("✅ Requirements installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Error installing requirements: {e}")
            return False
    else:
        print("⚠️  requirements.txt not found!")
        return False
    
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Set up AWS credentials (aws configure or environment variables)")
    print("2. Set JAVA_HOME environment variable")
    print("3. Run: python local_transaction_processor.py")
    print("   OR from project root: python run_master_transactions.py")
    
    return True

if __name__ == "__main__":
    setup_environment()
