"""
EMR Job Wrapper for Crypto Transaction Processing
Provides a job wrapper class that can be instantiated by master_transaction_main.py dispatcher.
Uses CryptoProcessor from transformers for processing logic.
"""

from typing import Dict, Any
from pyspark.sql.functions import lit, col
from src.utils.config_loader import ConfigLoader
from src.utils.spark_utils import SparkUtils
from src.utils.custom_logger import get_logger
from src.jobs.master_transactions.transformers.crypto_processors.crypto_processor_factory import CryptoProcessorFactory
from src.utils.io_utils import IOUtils
from src.utils.asset_utils import AssetUtils


class CryptoTransactionJob:
    """
    EMR-compatible job wrapper for crypto transaction processing.
    This class follows the expected pattern for the job dispatcher.
    Instantiated by master_transaction_main.py with config and kwargs.
    """
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        """Initialize the crypto transaction job with config and kwargs."""
        self.logger = get_logger()
        
        # Load master transaction specific configuration using ConfigLoader
        config_loader = ConfigLoader("aws")
        master_config = config_loader.load_master_transaction_config()

        # Merge with the generic config, giving priority to master transaction config
        self.config = {**config, **master_config}
        self.kwargs = kwargs
        
        # Extract common parameters from config (set by main.py) or kwargs (for direct calls)
        self.t_1 = self.config.get('t_1') or kwargs.get('t_1')
        self.h_1 = self.config.get('h_1') or kwargs.get('h_1') or self.t_1
        
        # Create Spark session using SparkUtils pattern
        self.spark_utils = SparkUtils("CryptoTransactionJob")
        self.spark = self.spark_utils.create_spark_session()
        self.logger.info("✅ Spark session created for CryptoTransactionJob")
        self.logger.info(f"✅ Configuration loaded with keys: {list(master_config.keys())}")
    
    def execute(self):
        """Main execution method called by EMR framework."""
        self.logger.info("🚀 Starting Crypto Transaction Processing")
        self.logger.info(f"Processing date: {self.t_1}")
        self.logger.info(f"Processing hour: {self.h_1}")
        
        try:
            # Initialize helpers
            io_utils = IOUtils(self.spark, self.config)
            asset_utils = AssetUtils(self.spark, self.config)

            # Load price data and crypto currencies (aligned with previous CryptoProcessor logic)
            price_df = None
            advanced_price_df = None
            crypto_currencies_df = None

            # Load quote & cover prices
            try:
                price_config = None
                if "crypto_currency_prices" in self.config:
                    price_config = self.config["crypto_currency_prices"]
                elif "prices" in self.config and "crypto_currency" in self.config["prices"]:
                    price_config = self.config["prices"]["crypto_currency"]
                if price_config and "quote_and_cover_orders_price_path" in price_config:
                    price_path = price_config["quote_and_cover_orders_price_path"]
                    if price_path.startswith("s3a://") or price_path.startswith("s3://"):
                        full_price_path = f"{price_path}dt={self.t_1}/"
                    elif price_path.startswith("file://"):
                        full_price_path = f"{price_path}/dt={self.t_1}/"
                    else:
                        bucket_path = self.config.get("bucket_path", "s3a://pluang-datalake-calculated-staging/")
                        full_price_path = f"{bucket_path}{price_path}/dt={self.t_1}/"
                    self.logger.info(f"Loading crypto quote and cover prices from: {full_price_path}")
                    try:
                        price_df = io_utils.read_csv_file(full_price_path)
                        if price_df is not None:
                            if "_id" in price_df.columns:
                                price_df = price_df.withColumnRenamed("_id", "asset_id")
                            if "mid_price" in price_df.columns:
                                price_df = price_df.withColumnRenamed("mid_price", "current_unit_price")
                    except Exception as e:
                        self.logger.warning(f"Could not load quote and cover price data: {str(e)}")
            except Exception as e:
                self.logger.warning(f"Price config error: {e}")

            # Load advanced prices
            try:
                price_config = None
                if "crypto_currency_prices" in self.config:
                    price_config = self.config["crypto_currency_prices"]
                elif "prices" in self.config and "crypto_currency" in self.config["prices"]:
                    price_config = self.config["prices"]["crypto_currency"]
                if price_config and "advanced_orders_price_path" in price_config:
                    adv_path = price_config["advanced_orders_price_path"]
                    if adv_path.startswith("s3a://") or adv_path.startswith("s3://"):
                        full_adv_path = f"{adv_path}dt={self.t_1}/"
                    elif adv_path.startswith("file://"):
                        full_adv_path = f"{adv_path}/dt={self.t_1}/"
                    else:
                        bucket_path = self.config.get("bucket_path", "s3a://pluang-datalake-calculated-staging/")
                        full_adv_path = f"{bucket_path}{adv_path}/dt={self.t_1}/"
                    self.logger.info(f"Loading crypto advanced prices from: {full_adv_path}")
                    try:
                        advanced_price_df = io_utils.read_json_data(full_adv_path, True, None, False)
                        if advanced_price_df is not None and "value" in advanced_price_df.columns:
                            advanced_price_df = advanced_price_df.select(col("value.*"))
                    except Exception as e:
                        self.logger.warning(f"Could not load advanced price data: {str(e)}")
            except Exception as e:
                self.logger.warning(f"Advanced price config error: {e}")

            # Load crypto currencies from Kafka via AssetUtils
            try:
                self.logger.info("Loading crypto currencies via AssetUtils (Kafka)")
                crypto_currencies_df = asset_utils.get_crypto_assets()
                if crypto_currencies_df is not None and crypto_currencies_df.count() > 0:
                    crypto_currencies_df = crypto_currencies_df.select(
                        col("id").alias("id"),
                        col("symbol").alias("symbol"),
                        col("symbol").alias("product"),
                        col("active").alias("active"),
                        col("updated").alias("updated"),
                    )
                else:
                    self.logger.warning("crypto_currencies_df is empty or None")
            except Exception as e:
                self.logger.warning(f"Crypto currencies load failed: {e}")

            # Create processors and process all crypto sources
            processors = CryptoProcessorFactory.create_all_processors(self.spark, self.config, self.t_1, self.h_1)
            result_df = None
            for p in processors:
                part = p.process_transactions(
                    price_df=price_df,
                    advanced_price_df=advanced_price_df,
                    crypto_currencies_df=crypto_currencies_df,
                )
                if part is not None:
                    result_df = part if result_df is None else result_df.unionByName(part, allowMissingColumns=True)
            
            if result_df is not None:
                # Write results to S3
                bucket_name = self.config.get("bucket", "pluang-datalake-calculated-staging")
                output_base = self.config.get("master_transaction_path", f"s3a://{bucket_name}/processed_data/master_transactions/")
                output_path = f"{output_base}dt={self.t_1}/"
                if self.h_1 and self.h_1 != self.t_1:
                    output_path += f"hour={self.h_1}/"
                
                self.logger.info(f"Writing crypto results to: {output_path}")
                
                # Debug: Print schema to identify void columns
                self.logger.info("🔍 DataFrame schema before writing:")
                result_df.printSchema()
                
                # Check for void columns and fix them
                void_columns = []
                for field in result_df.schema.fields:
                    field_type_str = str(field.dataType)
                    if field_type_str in ["void", "NullType()", "NullType"] or "NullType" in field_type_str:
                        void_columns.append(field.name)
                        self.logger.warning(f"⚠️  Found void column: {field.name} (type: {field.dataType})")
                
                if void_columns:
                    self.logger.info(f"🔧 Fixing {len(void_columns)} void columns...")
                    for col_name in void_columns:
                        if col_name in ["contract_id", "contract_name", "product", "product_id", 
                                       "recurring_transaction_id", "user_pocket_id", "idr2usd_ref_id", 
                                       "usd2usdmargin_ref_id", "usdmargin2usd_ref_id", "ref_id_hedge", "network"]:
                            # String columns - use empty string
                            result_df = result_df.withColumn(col_name, lit("").cast("string"))
                        else:
                            # Numeric columns - use null with appropriate type
                            result_df = result_df.withColumn(col_name, lit(None).cast("decimal(20,8)"))
                    self.logger.info("✅ Fixed all void columns")
                
                result_df.coalesce(2).write.mode("overwrite").parquet(output_path)
                
                self.logger.info(f"✅ Successfully processed {result_df.count()} crypto transactions")
            else:
                self.logger.warning("⚠️  No crypto data to write")
                
        except Exception as e:
            self.logger.error(f"❌ Crypto processing failed: {str(e)}")
            import traceback
            traceback.print_exc()
            raise e
    
    def run(self):
        """Run method for compatibility with job dispatcher."""
        try:
            self.execute()
        finally:
            if hasattr(self, 'spark_utils'):
                self.spark_utils.stop_spark(self.spark)
        
        self.logger.info("🏁 Crypto transaction processing completed")

