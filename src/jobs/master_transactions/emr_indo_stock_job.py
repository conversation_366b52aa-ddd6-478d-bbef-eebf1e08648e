"""
EMR Job Wrapper for Indonesian Stocks (IDSS)
Runs multiple processors (by source table) and unions the results.
"""

from typing import Dict, Any
from pyspark.sql.functions import lit

from src.utils.config_loader import ConfigLoader
from src.utils.spark_utils import SparkUtils
from src.utils.custom_logger import get_logger

from src.jobs.master_transactions.transformers.indo_stocks_processors.indo_stock_transactions_processor import (
    IndoStocksTransactionsProcessor,
)
from src.jobs.master_transactions.transformers.indo_stocks_processors.indo_stock_transaction_history_processor import (
    IndoStocksLimitHistoryProcessor,
)
from src.jobs.master_transactions.transformers.indo_stocks_processors.indo_stock_e_ipo_transactions_processor import (
    IndoStocksEIPOProcessor,
)
from src.jobs.master_transactions.transformers.indo_stocks_processors.indo_stock_split_transactions_processor import (
    IndoStocksSplitProcessor,
)
from src.jobs.master_transactions.transformers.indo_stocks_processors.indo_stock_bonus_transactions_processor import (
    IndoStocksBonusProcessor,
)
from src.jobs.master_transactions.transformers.indo_stocks_processors.indo_stock_right_issue_transactions_processor import (
    IndoStocksRightIssueProcessor,
)
from src.jobs.master_transactions.transformers.indo_stocks_processors.indo_stock_warrant_transactions_processor import (
    IndoStocksWarrantProcessor,
)


class IndoStocksTransactionJob:
    def __init__(self, config: Dict[str, Any], **kwargs):
        self.logger = get_logger()

        config_loader = ConfigLoader("aws")
        master_config = config_loader.load_master_transaction_config()
        self.config = {**config, **master_config}
        self.kwargs = kwargs

        self.t_1 = self.config.get("t_1") or kwargs.get("t_1")
        self.h_1 = self.config.get("h_1") or kwargs.get("h_1") or self.t_1

        self.spark_utils = SparkUtils("IndoStocksTransactionJob")
        self.spark = self.spark_utils.create_spark_session()
        self.logger.info("✅ Spark session created for IndoStocksTransactionJob")
        self.logger.info("✅ Configuration loaded")

    def execute(self):
        self.logger.info("🚀 Starting Indo Stocks Processing (IDSS)")
        self.logger.info(f"Processing date: {self.t_1}")
        self.logger.info(f"Processing hour: {self.h_1}")

        try:
            processors = [
                IndoStocksTransactionsProcessor(self.config, self.t_1, self.h_1, self.spark),
                IndoStocksLimitHistoryProcessor(self.config, self.t_1, self.h_1, self.spark),
                IndoStocksEIPOProcessor(self.config, self.t_1, self.h_1, self.spark),
                IndoStocksSplitProcessor(self.config, self.t_1, self.h_1, self.spark),
                IndoStocksBonusProcessor(self.config, self.t_1, self.h_1, self.spark),
                IndoStocksRightIssueProcessor(self.config, self.t_1, self.h_1, self.spark),
                IndoStocksWarrantProcessor(self.config, self.t_1, self.h_1, self.spark),
            ]

            result_df = None
            for p in processors:
                part = p.process()
                if part is not None:
                    result_df = part if result_df is None else result_df.unionByName(part, allowMissingColumns=True)

            if result_df is not None:
                bucket_name = self.config.get("bucket_name", "pluang-datalake-calculated-staging")
                output_base = self.config.get("master_transaction_path", f"s3a://{bucket_name}/processed_data/master_transactions/")
                output_path = f"{output_base}asset=idss/dt={self.t_1}/"
                if self.h_1 and self.h_1 != self.t_1:
                    output_path += f"hour={self.h_1}/"

                self.logger.info(f"Writing idss results to: {output_path}")

                self.logger.info("🔍 DataFrame schema before writing:")
                result_df.printSchema()

                # Fix void columns for parquet write, similar to gold job
                void_columns = []
                for field in result_df.schema.fields:
                    field_type_str = str(field.dataType)
                    if field_type_str in ["void", "NullType()", "NullType"] or "NullType" in field_type_str:
                        void_columns.append(field.name)
                        self.logger.warning(f"⚠️  Found void column: {field.name} (type: {field.dataType})")

                if void_columns:
                    self.logger.info(f"🔧 Fixing {len(void_columns)} void columns...")
                    for col_name in void_columns:
                        if col_name in [
                            "contract_id",
                            "contract_name",
                            "product",
                            "product_id",
                            "recurring_transaction_id",
                            "user_pocket_id",
                            "idr2usd_ref_id",
                            "usd2usdmargin_ref_id",
                            "usdmargin2usd_ref_id",
                            "ref_id_hedge",
                            "network",
                        ]:
                            result_df = result_df.withColumn(col_name, lit("").cast("string"))
                        else:
                            result_df = result_df.withColumn(col_name, lit(None).cast("decimal(20,8)"))
                    self.logger.info("✅ Fixed all void columns")

                result_df.coalesce(2).write.mode("overwrite").parquet(output_path)
                self.logger.info(f"✅ Successfully processed {result_df.count()} idss transactions")
            else:
                self.logger.warning("⚠️  No idss data to write")

        except Exception as e:
            self.logger.error(f"❌ Indo stocks processing failed: {str(e)}")
            import traceback
            traceback.print_exc()
            raise e

    def run(self):
        try:
            self.execute()
        finally:
            if hasattr(self, "spark_utils"):
                self.spark_utils.stop_spark(self.spark)
        self.logger.info("🏁 Indo stocks processing completed")


