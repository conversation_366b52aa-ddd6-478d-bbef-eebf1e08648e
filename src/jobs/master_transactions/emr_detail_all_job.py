"""
EMR Job Wrapper for Detail-All Aggregation
Unions per-asset outputs, optionally enriches with last_price table, and applies common post-processing.
"""

from typing import Dict, Any, List, Optional

from pyspark.sql import DataFrame
from pyspark.sql import functions as F

from src.utils.config_loader import ConfigLoader
from src.utils.spark_utils import SparkUtils
from src.utils.custom_logger import get_logger
from src.utils.io_utils import IOUtils
from src.jobs.master_transactions.transformers.common_post_processor import CommonPostProcessor


class DetailAllTransactionsDailyJob:
    def __init__(self, config: Dict[str, Any], **kwargs):
        self.logger = get_logger()
        config_loader = ConfigLoader("aws")
        master_config = config_loader.load_master_transaction_config()
        self.config = {**config, **master_config}
        self.kwargs = kwargs

        self.t_1 = self.config.get("t_1") or kwargs.get("t_1")
        self.h_1 = self.config.get("h_1") or kwargs.get("h_1") or self.t_1

        self.spark_utils = SparkUtils("DetailAllTransactionsDailyJob")
        self.spark = self.spark_utils.create_spark_session()
        self.io = IOUtils(self.spark, self.config)
        self.logger.info("✅ Spark session created for DetailAllTransactionsDailyJob")

    def _candidate_paths_for_assets(self) -> List[str]:
        bucket_name = self.config.get("bucket_name", "pluang-datalake-calculated-staging")
        base_default = f"s3a://{bucket_name}/processed_data/master_transactions/"
        base = self.config.get("master_transaction_path", base_default)

        paths = [
            f"{base}asset=fx/dt={self.t_1}/",
            f"{base}asset=gold/dt={self.t_1}/",
            f"{base}asset=idss/dt={self.t_1}/",
            f"{base}asset=mfund/dt={self.t_1}/",
            f"{base}dt={self.t_1}/",  # generic fallback (e.g., crypto/gss if written without asset=)
        ]
        return paths

    def _read_many(self, paths: List[str]) -> List[DataFrame]:
        dfs: List[DataFrame] = []
        for p in paths:
            try:
                df = self.io.read_parquet(p)
                if df is not None and df.count() > 0:  # lightweight existence check
                    self.logger.info(f"Loaded {df.count()} rows from {p}")
                    dfs.append(df)
            except Exception as e:
                self.logger.warning(f"Skip missing/unreadable path: {p} ({e})")
        return dfs

    def _load_last_price(self) -> Optional[DataFrame]:
        # Allow explicit path from config; otherwise use default
        lp_conf = self.config.get("last_price_table_daily", {})
        out_base = lp_conf.get("output_path") or self.config.get(
            "detail_last_price_table_daily_output_path",
        )
        if out_base is None:
            bucket_name = self.config.get("bucket_name", "pluang-datalake-calculated-staging")
            out_base = f"s3a://{bucket_name}/processed_data/last_price/detail_last_price_table_daily/"
        try:
            df = self.io.read_parquet(out_base)
            if df is not None:
                return df.where(F.col("day") == F.lit(self.t_1))
        except Exception as e:
            self.logger.warning(f"Last price table not available: {e}")
        return None

    def execute(self):
        self.logger.info("🚀 Starting Detail-All aggregation")
        self.logger.info(f"Processing date: {self.t_1}")

        try:
            # 1) Load all asset outputs
            dfs = self._read_many(self._candidate_paths_for_assets())
            if not dfs:
                self.logger.warning("No asset outputs found; nothing to aggregate")
                return

            result = None
            for df in dfs:
                result = df if result is None else result.unionByName(df, allowMissingColumns=True)

            # 2) Optionally load last price for later enrichments (kept ready for specific joins)
            last_price = self._load_last_price()
            if last_price is not None:
                self.logger.info("Last price daily is available for downstream joins if needed")

            # 3) Apply common post-processing
            cpp = CommonPostProcessor(config=self.config)
            result = cpp.apply_common_transformations(result)

            # 4) Write output partitioned by day
            out_bucket = self.config.get("bucket_name", "pluang-datalake-calculated-staging")
            out_base = self.config.get(
                "detail_all_output_path",
                f"s3a://{out_bucket}/processed_data/detail_all_transactions_daily/",
            )
            self.logger.info(f"Writing detail_all output to: {out_base}")
            result.write.mode("overwrite").partitionBy("day").parquet(out_base)

        except Exception as e:
            self.logger.error(f"❌ Detail-All aggregation failed: {e}")
            import traceback
            traceback.print_exc()
            raise

    def run(self):
        try:
            self.execute()
        finally:
            if hasattr(self, "spark_utils"):
                self.spark_utils.stop_spark(self.spark)
        self.logger.info("🏁 Detail-All aggregation completed")


