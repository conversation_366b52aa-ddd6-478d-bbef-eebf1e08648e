"""
EMR Job Wrapper for Global Stock Transaction Processing
Provides a job wrapper class that can be instantiated by master_transaction_main.py dispatcher.
"""

from typing import Dict, Any
from src.utils.config_loader import ConfigLoader
from src.utils.spark_utils import SparkUtils
from src.utils.custom_logger import get_logger


class GlobalStockTransactionJob:
    """
    EMR-compatible job wrapper for global stock transaction processing.
    This class follows the expected pattern for the job dispatcher.
    Instantiated by master_transaction_main.py with config and kwargs.
    """
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        """Initialize the global stock transaction job with config and kwargs."""
        self.logger = get_logger()
        
        # Load master transaction specific configuration using ConfigLoader
        config_loader = ConfigLoader("aws")
        master_config = config_loader.load_master_transaction_config()

        # Merge with the generic config, giving priority to master transaction config
        self.config = {**config, **master_config}
        self.kwargs = kwargs
        
        # Extract common parameters from config (set by main.py) or kwargs (for direct calls)
        self.t_1 = self.config.get('t_1') or kwargs.get('t_1')
        self.h_1 = self.config.get('h_1') or kwargs.get('h_1') or self.t_1
        
        # Create Spark session using SparkUtils pattern
        self.spark_utils = SparkUtils("GlobalStockTransactionJob")
        self.spark = self.spark_utils.create_spark_session()
        self.logger.info("✅ Spark session created for GlobalStockTransactionJob")
        self.logger.info("✅ Configuration loaded")
    
    def execute(self):
        """Main execution method for global stock processing."""
        self.logger.info("🚀 Starting Global Stock Transaction Processing")
        self.logger.info(f"Processing date: {self.t_1}")
        self.logger.info(f"Processing hour: {self.h_1}")
        
        try:
            # Import required modules
            from src.utils.io_utils import IOUtils
            from src.utils.operations import Operations
            from src.jobs.master_transactions.transformers.gss_processor.global_stock_processor import GlobalStockProcessor
            from src.jobs.master_transactions.transformers.gss_processor.global_stock_split_processor import GlobalStockSplitProcessor
            from src.jobs.master_transactions.transformers.gss_processor.global_stock_reverse_split_processor import GlobalStockReverseSplitProcessor
            from src.jobs.master_transactions.transformers.gss_processor.global_stock_inventory_orders_processor import GlobalStockInventoryOrdersProcessor
            from src.jobs.master_transactions.transformers.gss_processor.global_stock_mission_rewards_processor import GlobalStockMissionRewardsProcessor
            
            # Initialize utilities
            io_utils = IOUtils(self.spark, self.config)
            ops = Operations(self.spark)
            
            # Create global stock processor
            processor = GlobalStockProcessor(
                config=self.config,
                spark=self.spark,
                io_utils=io_utils,
                ops=ops,
                t_1=self.t_1,
                h_1=self.h_1
            )
            
            # Process global stock transactions
            self.logger.info("=== Processing global stock transactions ===")
            result_df = processor.process_transactions()

            # Additional GSS processors
            extras = [
                GlobalStockSplitProcessor(self.config, self.t_1, self.h_1, self.spark),
                GlobalStockReverseSplitProcessor(self.config, self.t_1, self.h_1, self.spark),
                GlobalStockInventoryOrdersProcessor(self.config, self.t_1, self.h_1, self.spark),
                GlobalStockMissionRewardsProcessor(self.config, self.t_1, self.h_1, self.spark),
            ]
            for p in extras:
                part = p.process()
                if part is not None:
                    result_df = part if result_df is None else result_df.unionByName(part, allowMissingColumns=True)
            
            if result_df is None:
                self.logger.warning("⚠️  No global stock data found - path may not exist or data is empty")
                self.logger.info("✅ Job completed successfully with no data to process")
                return
            
            row_count = result_df.count()
            if row_count > 0:
                # Write results to S3
                bucket_name = self.config.get("bucket_name", "pluang-datalake-calculated-staging")
                output_base = self.config.get("master_transaction_path", f"s3a://{bucket_name}/processed_data/master_transactions/")
                output_path = f"{output_base}dt={self.t_1}/"
                if self.h_1 and self.h_1 != self.t_1:
                    output_path += f"hour={self.h_1}/"
                
                self.logger.info(f"Writing global stock results to: {output_path}")
                result_df.coalesce(10).write.mode("overwrite").parquet(output_path)
                
                self.logger.info(f"✅ Successfully processed {row_count} global stock transactions")
            else:
                self.logger.warning("⚠️  No global stock data to write - DataFrame is empty")
                self.logger.info("✅ Job completed successfully with no data to process")
                
        except Exception as e:
            self.logger.error(f"❌ Global stock processing failed: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def run(self):
        """Run method for compatibility with job dispatcher."""
        try:
            self.execute()
        finally:
            if hasattr(self, 'spark_utils'):
                self.spark_utils.stop_spark(self.spark)
        
        self.logger.info("🏁 Global stock transaction processing completed")

