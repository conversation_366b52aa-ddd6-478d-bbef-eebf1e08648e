"""
EMR Job Wrapper for Gold Transaction Processing
Provides a job wrapper class that can be instantiated by main.py dispatcher.
Uses GoldProcessor from transformers for processing logic.
"""

from typing import Dict, Any
from pyspark.sql.functions import lit
from src.utils.config_loader import ConfigLoader
from src.utils.spark_utils import SparkUtils
from src.utils.custom_logger import get_logger
from src.jobs.master_transactions.transformers.gold_processors.gold_trades_processor import (
    GoldProcessor,
)
from src.jobs.master_transactions.transformers.gold_processors.gold_withdrawals_processor import (
    GoldWithdrawalsProcessor,
)
from src.jobs.master_transactions.transformers.gold_processors.gold_loans_processor import (
    GoldLoansProcessor,
)
from src.jobs.master_transactions.transformers.gold_processors.gold_dormant_fees_processor import (
    GoldDormantFeesProcessor,
)


class GoldTransactionJob:
    """
    EMR-compatible job wrapper for gold transaction processing.
    This class follows the expected pattern for the job dispatcher.
    Instantiated by main.py with config and kwargs.
    """
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        """Initialize the gold transaction job with config and kwargs."""
        self.logger = get_logger()
        
        # Load master transaction specific configuration using ConfigLoader
        config_loader = ConfigLoader("aws")
        master_config = config_loader.load_master_transaction_config()

        # Merge with the generic config, giving priority to master transaction config
        self.config = {**config, **master_config}
        self.kwargs = kwargs
        
        # Extract common parameters from config (set by main.py) or kwargs (for direct calls)
        self.t_1 = self.config.get('t_1') or kwargs.get('t_1')
        self.h_1 = self.config.get('h_1') or kwargs.get('h_1') or self.t_1
        
        # Create Spark session using SparkUtils pattern
        self.spark_utils = SparkUtils("GoldTransactionJob")
        self.spark = self.spark_utils.create_spark_session()
        self.logger.info("✅ Spark session created for GoldTransactionJob")
        self.logger.info("✅ Configuration loaded")
    
    def execute(self):
        """Main execution method called by EMR framework."""
        self.logger.info("🚀 Starting Gold Transaction Processing")
        self.logger.info(f"Processing date: {self.t_1}")
        self.logger.info(f"Processing hour: {self.h_1}")
        
        try:
            # Buy/Sell processor
            processor = GoldProcessor(
                config=self.config,
                t_1=self.t_1,
                h_1=self.h_1,
                spark=self.spark
            )
            
            # Process buy/sell
            buy_sell_df = processor.process_transactions()

            # Process withdrawals via separate processor
            withdrawals_df = GoldWithdrawalsProcessor(
                config=self.config,
                t_1=self.t_1,
                h_1=self.h_1,
                spark=self.spark,
            ).process()

            # Loans
            loans_df = GoldLoansProcessor(
                config=self.config,
                t_1=self.t_1,
                h_1=self.h_1,
                spark=self.spark,
            ).process()

            # Gold dormant fees
            dormant_df = GoldDormantFeesProcessor(
                config=self.config,
                t_1=self.t_1,
                h_1=self.h_1,
                spark=self.spark,
            ).process()

            # Union available sources
            result_df = None
            for part in [buy_sell_df, withdrawals_df, loans_df, dormant_df]:
                if part is not None:
                    result_df = part if result_df is None else result_df.unionByName(part)
            
            if result_df is not None:
                # Write results to S3
                bucket_name = self.config.get("bucket_name", "pluang-datalake-calculated-staging")
                output_base = self.config.get("master_transaction_path", f"s3a://{bucket_name}/processed_data/master_transactions/")
                output_path = f"{output_base}asset=gold/dt={self.t_1}/"
                if self.h_1 and self.h_1 != self.t_1:
                    output_path += f"hour={self.h_1}/"
                
                self.logger.info(f"Writing gold results to: {output_path}")
                
                # Debug: Print schema to identify void columns
                self.logger.info("🔍 DataFrame schema before writing:")
                result_df.printSchema()
                
                # Check for void columns and fix them
                void_columns = []
                for field in result_df.schema.fields:
                    field_type_str = str(field.dataType)
                    if field_type_str in ["void", "NullType()", "NullType"] or "NullType" in field_type_str:
                        void_columns.append(field.name)
                        self.logger.warning(f"⚠️  Found void column: {field.name} (type: {field.dataType})")
                
                if void_columns:
                    self.logger.info(f"🔧 Fixing {len(void_columns)} void columns...")
                    for col_name in void_columns:
                        if col_name in ["contract_id", "contract_name", "product", "product_id", 
                                       "recurring_transaction_id", "user_pocket_id", "idr2usd_ref_id", 
                                       "usd2usdmargin_ref_id", "usdmargin2usd_ref_id", "ref_id_hedge", "network"]:
                            # String columns - use empty string
                            result_df = result_df.withColumn(col_name, lit("").cast("string"))
                        else:
                            # Numeric columns - use null with appropriate type
                            result_df = result_df.withColumn(col_name, lit(None).cast("decimal(20,8)"))
                    self.logger.info("✅ Fixed all void columns")
                
                result_df.coalesce(2).write.mode("overwrite").parquet(output_path)
                
                self.logger.info(f"✅ Successfully processed {result_df.count()} gold transactions")
            else:
                self.logger.warning("⚠️  No gold data to write")
                
        except Exception as e:
            self.logger.error(f"❌ Gold processing failed: {str(e)}")
            import traceback
            traceback.print_exc()
            raise e
    
    def run(self):
        """Run method for compatibility with job dispatcher."""
        try:
            self.execute()
        finally:
            if hasattr(self, 'spark_utils'):
                self.spark_utils.stop_spark(self.spark)
        
        self.logger.info("🏁 Gold transaction processing completed")

