from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.jobs.trading_competition.trading_competition_structs import *
from enum import Enum


class DeDupeOnConstants(Enum):
    crypto_currency_wallet_transfers = "updated_at"
    trading_competition_user_events = "eventTime"

class DeDupeKeysConstants(Enum):
    bappebti_wallets = ["account_id", "user_id"]
    trading_competition_user_events = ["userId", "accountId"]

class TransactionFilterColumnConstants(Enum):
    crypto_future_trades = "traded_at_timestamp"
    crypto_margin_wallets = "updated"
    crypto_future_instruments = "updated"
    crypto_broker_margin_wallet_balances = "updated"
    bappebti_wallets = "updated"
    leverage_wallet_accounts = "updated"
    forex_accounts = "updated"

class CreatedFilterConstants(Enum):
    crypto_currency_wallet_transfers = "created_at"
    trading_competition_user_events = "eventTime"

class FlattenColumnConstants(Enum):
    crypto_future_trades = {"column_name": "fee", "required_column": "$.totalTradeFee", "rename_to": "total_trade_fee"}
    crypto_margin_wallets = {"column_name": "exchange_response", "required_column": "$.walletBalance", "rename_to": "wallet_balance"}


class SnapAssets:

    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("snap_assets")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts")
        self.batch = kwargs.get("batch")
        self.ts_1 = None
        self.snapshot_path = config.get("snapshot_path")
        self.raw_data_path = self.config.get("raw_data_path")
        self.raw_data_format = "raw_json"
        self.snapshot_format = "parquet"

    def get_schema_for_asset(self, asset):
        try:
            asset_column_name = f"{asset}_columns"
            column_names = self.config.get(asset_column_name)
            # Define the schema explicitly
            return StructType([StructField(col, StringType(), True) for col in column_names])
        except Exception as e:
            raise e

    def process_trading_competition_user_events(self, df: DataFrame):
        # Extract the "object" field and parse "data" as JSON
        if not isinstance(df, DataFrame):
            return df
        df = df.withColumn("object", from_json(col("object"), StructType([
            StructField("type", StringType(), True),
            StructField("id", LongType(), True),
            StructField("data", schema_for_trading_competition_user_events)
        ])))
        # Select the fields from the nested "data" structure
        return df.select("object.data.*")

    def read_data(self, path, format, **kwargs):
        df = None
        if format is None or format == "csv":
            if kwargs.get("schema") is not None:
                self.logger.info("Reading data using schema")
                df = self.io_utils.read_csv_file(path, kwargs.get("schema"), True)
            else:
                df = self.io_utils.read_csv_file(path, None, False)
        elif format == "raw_json":
            df = self.io_utils.read_json_data(path, True, None, True)
        elif format == "json":
            df = self.io_utils.read_json_data(path)
        elif format == "parquet":
            df = self.io_utils.read_parquet_data(path)
        else:
            self.logger.info("No Valid file format is present")
        return df

    def custom_transformation(self, df_t_1, df_t_2, de_dupe_on):
        if df_t_1 is not None:
            self.logger.info("cutoff time is {}".format(self.ts_1))
            df_t_1 = df_t_1.withColumn(de_dupe_on, col(de_dupe_on)).filter(
                col(de_dupe_on) <= lit(self.ts_1))
            t_2_cols = df_t_2.columns
            df_t_1 = df_t_1.select(t_2_cols)
            data_types = df_t_2.dtypes
            for data_type in data_types:
                df_t_1 = df_t_1.withColumn(data_type[0], col(data_type[0]).cast(data_type[1]))
            if df_t_2.count() > 0:
                df_union = df_t_2.union(df_t_1)
            else:
                df_union = df_t_1
        else:
            df_union = df_t_2
        return df_union

    def write_data(self, df, path, format, number_of_partitions=5):
        if format is None or format == "parquet":
            self.io_utils.write_parquet_file(df, path, number_of_partitions)
        elif format == "csv":
            self.io_utils.write_csv_file(df, path, number_of_partitions)
        elif format == "json":
            self.io_utils.write_json_file(df, path, number_of_partitions)
        else:
            self.logger.info("No Valid file format is present")
        return df

    def start_processing(self):
        t_1, h_1, t_2, h_2, self.ts_1, ts_2 = DateUtils.get_tc_dates_and_timestamp(self.utc_cutoff_ts, self.config)
        self.logger.info(f"Jkt Date t_1: {t_1}, Snapshot write hour h_1: {h_1}, Snapshot and Raw Data Read Date  t_2: {t_2}, Snapshot read hour h_2: {h_2}")
        assets_batch = self.config.get("asset_snapshot").get(self.batch)
        for assets in assets_batch:
            for asset, dedupe in assets.items():
                #Read Last Snapshot Data
                self.logger.info(f"Snapshot asset : {asset}")

                try:
                    de_dupe_key = getattr(DeDupeKeysConstants, asset).value
                except AttributeError:
                    de_dupe_key = ["id"]

                try:
                    de_dupe_on = getattr(DeDupeOnConstants, asset).value
                except AttributeError:
                    de_dupe_on = "updated"

                try:
                    transaction_filter_column = getattr(TransactionFilterColumnConstants, asset).value
                except AttributeError:
                    transaction_filter_column = "transaction_time"

                last_snapshot_path = f"{self.bucket_path}/{self.snapshot_path}/{asset}/dt={t_2}/hour={h_2}"
                self.logger.info(f"Reading snapshot from {last_snapshot_path}")
                df_last_snapshot = self.io_utils.read_parquet_data(last_snapshot_path, self.get_schema_for_asset(asset), True)

                #Read Raw data
                raw_data_path = f"{self.bucket_path}/{self.raw_data_path}/{asset}/dt={t_1}"
                self.logger.info(f"Reading raw data from {raw_data_path}")
                df_raw_data = self.read_data(raw_data_path, self.raw_data_format)

                if asset == "trading_competition_user_events":
                    df_raw_data = self.process_trading_competition_user_events(df_raw_data)

                if isinstance(df_raw_data, DataFrame) and asset not in ["trading_competition_user_events", "options_contracts"]:
                    df_raw_data = df_raw_data.filter(col(transaction_filter_column).cast("timestamp") >= lit(
                        self.config["trading_competition"]["start_time"]).cast("timestamp"))

                try:
                    flatten_column = getattr(FlattenColumnConstants, asset).value
                except AttributeError:
                    flatten_column = False

                if flatten_column and isinstance(df_raw_data, DataFrame):
                    df_raw_data_flatten = df_raw_data.withColumn(flatten_column.get("rename_to"),
                                                                 get_json_object(col(flatten_column.get("column_name")),
                                                                                 flatten_column.get("required_column")))

                    df_raw_data = df_raw_data_flatten.drop(flatten_column.get("column_name"))

                #Union Last Snapshot Data and Raw data
                df_union = self.custom_transformation(df_raw_data, df_last_snapshot, de_dupe_on)

                # Remove Duplicates based on de_dupe_key and de_dupe_on
                df_union = df_union.dropDuplicates(de_dupe_key + [de_dupe_on])

                self.logger.info("Started De dupe operation")
                if dedupe:
                    df = self.ops.de_dupe_dataframe(df_union, de_dupe_key, de_dupe_on)
                else:
                    df = df_union
                de_dupe_write_path = f"{self.bucket_path}/{self.snapshot_path}/{asset}/dt={t_1}/hour={h_1}"
                self.logger.info("writing data in {}".format(de_dupe_write_path))
                self.write_data(df, de_dupe_write_path, self.snapshot_format)

    def run(self):
        self.start_processing()
        self.spark_utils.stop_spark(self.spark)
