from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.jobs.trading_competition.trading_competition_structs import schema_for_all_transactions

class TransactionTransformer:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("Transaction Transformer")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")
        self.tier_snapshot_path = "{}/{}".format(self.bucket_path, self.config['aum_tier_upgrade']['tier_snapshot_path'])
        self.transactions_overwrite_path = "{}/{}/".format(self.bucket_path, self.config['batches']['all_transaction_file_path'])
        self.crypto_rebrand_txn_path = "{}/{}/".format(self.bucket_path, self.config['crypto_rebrand_txn_path'])
        self.transactions_overwrite_hdfs_path = "hdfs:///{}/".format(self.config['batches']['all_transaction_file_path'])
        self.transactions_snapshot_path = "{}/{}/".format(self.bucket_path, self.config['snapshot_path'])
        self.start_asset_position_path = "{}/{}/".format(self.bucket_path, self.config['start_asset_position_path'])
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts") or DateUtils.get_utc_timestamp()
        self.partner_id = self.config["pluang_partner_id"]
        self.usdt_coin_id = self.config["usdt_coin_id"]
        self.trading_competition_start_time = DateUtils.get_utc_timestamp_from_string(self.config["trading_competition"]["start_time"])
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(self.utc_cutoff_ts, self.config)
        self.logger.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2
        ))

    def get_all_prices(self):
        forex_partner_price = self.io_utils.read_json_data("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['forex']['price_path'], self.t_1, self.h_1))
        forex_partner_price = forex_partner_price.filter((col("forex_id") == 10000) & (col("partner_id") == self.partner_id))
        forex_price = int(forex_partner_price.collect()[0]["mid_price"])

        gold_partner_price = self.io_utils.read_json_data("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['gold']['price_path'], self.t_1, self.h_1))
        gold_partner_price = gold_partner_price.filter(col("partnerId") == self.partner_id).withColumn("mid_price", floor(
            ((col("closeBuyBack").cast(DoubleType()) + col("closeSell").cast(DoubleType())) / 2)))
        gold_price = gold_partner_price.collect()[0]["mid_price"]

        global_stock_price = self.io_utils.read_csv_file("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['global_stock']['price_path'], self.t_1, self.h_1), None, False)
        global_stock_price = global_stock_price.withColumn("mid_price", round(col("mid_price"), 2))
        global_stock_price = global_stock_price.select(col("_id").alias("asset_id"), col("mid_price").alias("current_unit_price"))

        crypto_currency_price = self.io_utils.read_csv_file("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['crypto_currency']['price_path'], self.t_1, self.h_1), None, False)
        crypto_currency_price = crypto_currency_price.withColumn("mid_price", round(col("mid_price"), 2))
        crypto_currency_price = crypto_currency_price.select(col("_id").alias("asset_id"), col("mid_price").alias("current_unit_price"))

        usdt_price_df = crypto_currency_price.filter(col("asset_id") == self.usdt_coin_id)
        usdt_price = int(usdt_price_df.collect()[0]["current_unit_price"])

        crypto_futures_price = self.io_utils.read_csv_file("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['crypto_currency_futures']['price_path'], self.t_1, self.h_1), None, False)
        crypto_futures_price = crypto_futures_price.select(col("_id").alias("asset_id"), col("close_price").alias("current_unit_price"))

        fund_price = self.io_utils.read_json_data("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['fund']['price_path'], self.t_1, self.h_1))
        fund_price = fund_price.select(col("fund_id").alias("asset_id"), col("net_asset_value").alias("current_unit_price"))

        options_price = self.io_utils.read_csv_file("{}/{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['global_stock_options']['price_path'], self.config['prices']['global_stock_options']['price_snapshot_folder'], self.t_1, self.h_1), None, False)
        options_price = options_price.select(col("optionsContractId").alias("asset_id"), col("price").alias("current_unit_price"))

        return usdt_price, forex_price, gold_price, global_stock_price, crypto_currency_price, fund_price, options_price, crypto_futures_price

    def update_executed_quantity_splits(self, all_txn):
        all_txn = all_txn.withColumn("updated_executed_quantity", col("executed_quantity")) \
            .withColumn("updated_executed_unit_price", col("executed_unit_price"))
        splits = self.io_utils.read_csv_file("{}/{}/".format(self.bucket_path, self.config["global_stock_splits_path"]), None, False)
        all_txn = all_txn.join(splits, on=["asset_sub_type", "asset_id"], how="left")
        all_txn = all_txn.withColumn("ratio", when(((col("split_updated").isNotNull()) & (col("transaction_time") < col("split_updated"))), col("ratio")).otherwise(None))
        all_txn = all_txn.withColumn("updated_executed_quantity", when(col("ratio").isNull(), col("executed_quantity")).otherwise(round(col("executed_quantity") * col("ratio"), 12)))
        all_txn = all_txn.withColumn("updated_executed_unit_price", when(col("ratio").isNull(), col("executed_unit_price")).otherwise(round(col("executed_unit_price") / col("ratio"), 4)))
        return all_txn.drop("ratio", "split_updated")

    def get_user_tiers(self):
        tier = self.io_utils.read_csv_file("{}/dt={}/hour={}/".format(self.tier_snapshot_path, self.t_1, self.h_1), None, False)
        tier = tier.withColumn("trading_competition_start_time", lit(self.trading_competition_start_time))
        tier = tier.select("account_id", "tier", "trading_competition_start_time")
        return tier

    def get_crypto_currency_transactions(self, crypto_currency_price):
        # crypto_currency_transactions
        crypto_txn = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "crypto_currency_transactions", self.t_1, self.h_1), None, False)
        crypto_txn = crypto_txn.filter(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"])) \
            .filter(col("transaction_type").isin(["BUY", "SELL", "AIRDROP"])) \
            .filter(col("partner_id") == self.partner_id)
        crypto_txn = crypto_txn.select(lit("crypto_currency").alias("asset_type"), lit("crypto_currency_transactions").alias("asset_sub_type"),
                                       col("crypto_currency_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), "created", "updated", "executed_quantity", "executed_unit_price",
                                       "executed_total_price", col("fee").alias("fees"), "transaction_type", "status", lit(1).alias("currency_to_idr"), "transaction_time",
                                       lit(0).alias("leverage"), lit(1).alias("current_currency_to_idr"))

        crypto_rebranding_txn = self.io_utils.read_csv_file(self.crypto_rebrand_txn_path, None, False) \
            .filter(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"])) \
            .filter(col("partner_id") == self.partner_id)
        crypto_rebranding_txn = crypto_rebranding_txn.select(lit("crypto_currency").alias("asset_type"), lit("crypto_currency_transactions").alias("asset_sub_type"),
                                       col("crypto_currency_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), "created", "updated", "executed_quantity", "executed_unit_price",
                                       "executed_total_price", col("fee").alias("fees"), "transaction_type", "status", lit(1).alias("currency_to_idr"), "transaction_time",
                                       lit(0).alias("leverage"), lit(1).alias("current_currency_to_idr"))

        # crypto_currency_wallet_transfers
        crypto_wallet_transfers = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "crypto_currency_wallet_transfers", self.t_1, self.h_1), None, False)
        crypto_wallet_transfers = crypto_wallet_transfers.filter(col("status").isin(["SUCCESS"])) \
            .filter(col("partner_id") == self.partner_id)
        crypto_wallet_transfers = crypto_wallet_transfers.select(lit("crypto_currency").alias("asset_type"), lit("crypto_currency_wallet_transfers").alias("asset_sub_type"),
                                                                 col("crypto_currency_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), col("created_at").alias("created"), col("updated_at").alias("updated"), col("total_quantity").alias("executed_quantity"), col("unit_price").alias("executed_unit_price"),
                                                                 (col("total_quantity")*col("unit_price")).alias("executed_total_price"), col("transfer_fee").alias("fees"), "transaction_type", "status", lit(1).alias("currency_to_idr"), "transaction_time",
                                                                 lit(0).alias("leverage"), lit(1).alias("current_currency_to_idr"))

        # crypto_currency_pocket_transactions
        crypto_pocket_txn = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "crypto_currency_pocket_transactions", self.t_1, self.h_1), None, False)
        crypto_pocket_txn = crypto_pocket_txn.filter(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"])) \
            .filter(col("transaction_type").isin(["BUY", "SELL", "AIRDROP"])) \
            .filter(col("partner_id") == self.partner_id)
        crypto_pocket_txn = crypto_pocket_txn.select(lit("crypto_currency").alias("asset_type"), lit("crypto_currency_pocket_transactions").alias("asset_sub_type"),
                                                     col("crypto_currency_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), "created", "updated", "executed_quantity", "executed_unit_price",
                                                     "executed_total_price", col("transaction_fee").alias("fees"), "transaction_type", "status", lit(1).alias("currency_to_idr"), "transaction_time",
                                                     lit(0).alias("leverage"), lit(1).alias("current_currency_to_idr"))

        # get union
        all_crypto_txn = self.ops.get_union(crypto_txn, crypto_pocket_txn)
        all_crypto_txn = self.ops.get_union(all_crypto_txn, crypto_wallet_transfers)
        all_crypto_txn = self.ops.get_union(all_crypto_txn, crypto_rebranding_txn)

        # join with price
        all_crypto_txn = all_crypto_txn.join(crypto_currency_price, on=["asset_id"], how="left")
        return all_crypto_txn

    def get_global_stock_transactions(self, global_stock_price, forex_price):
        # global_stock_transactions
        gss_txn = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "global_stock_transactions", self.t_1, self.h_1), None, False)
        gss_txn = gss_txn.filter(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"])) \
            .filter(col("transaction_type").isin(["BUY", "SELL"])) \
            .filter(col("partner_id") == self.partner_id)
        gss_txn = gss_txn.withColumn("leverage", when(col("stock_type") == "CFD_LEVERAGE", 2).otherwise(0))
        gss_txn = gss_txn.withColumn("leverage", when((col("stock_type") == "CFD_LEVERAGE") & (col("trading_hours") == "INTRADAY"), 4).otherwise(col("leverage")))
        gss_txn = gss_txn.select(lit("global_stocks").alias("asset_type"), lit("global_stock_transactions").alias("asset_sub_type"),
                                 col("global_stock_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), "created", "updated", "executed_quantity",
                                 "executed_unit_price", "executed_total_price", col("transaction_fee").alias("fees"), "transaction_type", "status",
                                 col("usd_to_idr").alias("currency_to_idr"), "transaction_time", "leverage")

        self.logger.info("Reading gss_mergers_txn".format(gss_txn.count()))
        gss_mergers_txn = self.io_utils.read_csv_file("{}{}/".format(self.transactions_snapshot_path, "global_stock_merger_transactions"), None, False)
        gss_mergers_txn = gss_mergers_txn.select(lit("global_stocks").alias("asset_type"),
                                                 lit("global_stock_transactions").alias("asset_sub_type"),
                                                 "asset_id", "account_id", "user_id",
                                                 "transaction_id", "created", "updated", "executed_quantity",
                                                 "executed_unit_price", "fees", "transaction_type", "status",
                                                 "currency_to_idr", "transaction_time", "leverage")
        gss_mergers_txn = gss_mergers_txn.withColumn("executed_total_price", col("executed_quantity") * col("executed_unit_price"))
        self.logger.info("gss_mergers_txn count: {}".format(gss_mergers_txn.count()))
        gss_txn = self.ops.get_union(gss_txn, gss_mergers_txn)

        gss_txn = gss_txn.withColumn("current_currency_to_idr", lit(forex_price))

        # join price with global_stock_transactions
        gss_txn = gss_txn.join(global_stock_price, on=["asset_id"], how="left")
        return gss_txn

    def get_crypto_future_transactions(self, crypto_futures_price, usdt_price):
        # crypto_future_trades
        future_trades = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "crypto_future_trades", self.t_1, self.h_1), None, False)
        future_trades = future_trades.filter(col("transaction_type").isin(["BUY", "SELL"]))
        future_trades = future_trades.withColumn("leverage", lit(25))
        future_trades = future_trades.select(lit("crypto_futures").alias("asset_type"), lit("crypto_future_trades").alias("asset_sub_type"),
                                 col("crypto_future_instrument_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), "created", "updated", col("quantity").alias("executed_quantity"),
                                 col("price").alias("executed_unit_price"), (col("quantity")*col("price")).alias("executed_total_price"), (col("total_trade_fee") * col("settle_asset_mid_price")).alias("fees"), "transaction_type", lit("SUCCESS").alias("status"),
                                 col("settle_asset_mid_price").alias("currency_to_idr"), col("traded_at_timestamp").alias("transaction_time"), "leverage")
        future_trades = future_trades.withColumn("current_currency_to_idr", lit(usdt_price))

        # crypto_future_funding_transactions
        funding_transactions = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "crypto_future_funding_transactions", self.t_1, self.h_1), None, False)
        funding_transactions = funding_transactions.withColumn("transaction_type", lit("FUNDING"))
        funding_transactions = funding_transactions.withColumn("leverage", lit(25))
        funding_transactions = funding_transactions.select(lit("crypto_futures").alias("asset_type"), lit("crypto_future_funding_transactions").alias("asset_sub_type"),
                                             col("crypto_future_instrument_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), "created", "updated", col("quantity").alias("executed_quantity"),
                                             col("price").alias("executed_unit_price"), (col("quantity")*col("price")).alias("executed_total_price"), (col("fee") * col("settle_asset_mid_price")).alias("fees"), "transaction_type", lit("SUCCESS").alias("status"),
                                             col("settle_asset_mid_price").alias("currency_to_idr"), col("traded_at_timestamp").alias("transaction_time"), "leverage")
        funding_transactions = funding_transactions.withColumn("current_currency_to_idr", lit(usdt_price))

        # union
        crypto_future_txn = funding_transactions.union(future_trades)

        # join price with crypto_future_trades
        crypto_future_txn = crypto_future_txn.join(crypto_futures_price, on=["asset_id"], how="left")
        return crypto_future_txn

    def get_options_transactions(self, options_price, forex_price):
        # options_contract_transactions
        options_txn = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "options_contract_transactions", self.t_1, self.h_1), None, False)
        options_txn = options_txn.filter(col("status").isin(["SUCCESS", "PARTIALLY_FILLED"])) \
            .filter(col("transaction_type").isin(["LONG_OPEN", "LONG_CLOSE"])) \
            .filter(col("partner_id") == self.partner_id)
        options_txn = options_txn.withColumn("leverage", lit(0))
        options_contracts = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "options_contracts", self.t_1, self.h_1), None, False)
        options_contracts = options_contracts.select(col("id").alias("options_contract_id"), col("shares_per_contract"))
        options_txn = options_txn.join(options_contracts, on=["options_contract_id"], how="left")
        options_txn = options_txn.select(lit("global_stock_options").alias("asset_type"), lit("options_contract_transactions").alias("asset_sub_type"),
                                         col("options_contract_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), "created", "updated", (col("executed_quantity")*col("shares_per_contract")).alias("executed_quantity"),
                                         "executed_unit_price", "executed_total_price", col("transaction_fee").alias("fees"), "transaction_type", "status",
                                         col("usd_to_idr").alias("currency_to_idr"), "transaction_time", "leverage").drop("shares_per_contract")
        options_txn = options_txn.withColumn("current_currency_to_idr", lit(forex_price))
        # join price with options_contract_transactions
        options_txn = options_txn.join(options_price, on=["asset_id"], how="left")
        return options_txn

    def get_fund_transactions(self, fund_price):
        # fund_transactions
        fund_txn = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "fund_transactions", self.t_1, self.h_1), None, False)
        fund_txn = fund_txn.filter(col("status").isin(["APPROVED"])) \
            .filter(col("transaction_type").isin(["BUY", "SELL"])) \
            .filter(col("partner_id") == self.partner_id)
        fund_txn = fund_txn.withColumn("leverage", lit(0))
        fund_txn = fund_txn.select(lit("fund").alias("asset_type"),
                                   lit("fund_transactions").alias("asset_sub_type"),
                                   col("fund_id").alias("asset_id"), "account_id", "user_id",
                                   col("id").alias("transaction_id"), "created", "updated",
                                   col("quantity").alias("executed_quantity"),
                                   col("unit_price").alias("executed_unit_price"),
                                   col("final_price").alias("executed_total_price"),
                                   col("fee").alias("fees"), "transaction_type", "status",
                                   lit(1).alias("currency_to_idr"), lit(1).alias("current_currency_to_idr"), "transaction_time", "leverage")
        # join price with fund_transactions
        fund_txn = fund_txn.join(fund_price, on=["asset_id"], how="left")
        return fund_txn

    def get_forex_transactions(self, forex_price):
        # forex_transactions
        forex_txn = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "forex_transactions", self.t_1, self.h_1), None, False)
        forex_txn = forex_txn.filter(col("status").isin(["SUCCESS"])) \
            .filter(col("transaction_type").isin(["BUY", "SELL"])) \
            .filter(col("partner_id") == self.partner_id)
        forex_txn = forex_txn.withColumn("leverage", lit(0))
        forex_txn = forex_txn.select(lit("forex").alias("asset_type"),
                                     lit("forex_transactions").alias("asset_sub_type"),
                                     col("forex_id").alias("asset_id"), "account_id", "user_id",
                                     col("id").alias("transaction_id"), "created", "updated",
                                     col("quantity").alias("executed_quantity"),
                                     col("unit_price").alias("executed_unit_price"),
                                     col("total_price").alias("executed_total_price"),
                                     col("fee").alias("fees"), "transaction_type", "status",
                                     lit(1).alias("currency_to_idr"), lit(1).alias("current_currency_to_idr"),
                                     "transaction_time", "leverage")
        # join price with forex_transactions
        forex_txn = forex_txn.withColumn("current_unit_price", lit(forex_price))

        # forex_top_ups
        forex_top_ups = self.io_utils.read_parquet_data(
            "{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "forex_top_ups", self.t_1, self.h_1), None,
            False)
        forex_top_ups = forex_top_ups.filter(col("status").isin(["COMPLETED"])) \
            .withColumn("transaction_type", lit("BUY")) \
            .filter(col("partner_id") == self.partner_id)
        forex_top_ups = forex_top_ups.withColumn("leverage", lit(0))
        forex_top_ups = forex_top_ups.select(lit("forex").alias("asset_type"),
                                             lit("forex_top_ups").alias("asset_sub_type"),
                                             col("forex_id").alias("asset_id"), "account_id", "user_id",
                                             col("id").alias("transaction_id"), "created", "updated",
                                             col("final_amount").alias("executed_quantity"),
                                             col("unit_price").alias("executed_unit_price"),
                                             (col("final_amount")*col("unit_price")).alias("executed_total_price"),
                                             lit(0).alias("fees"), "transaction_type", "status",
                                             lit(1).alias("currency_to_idr"), lit(1).alias("current_currency_to_idr"),
                                             "transaction_time", "leverage")
        # join price with forex_top_ups
        forex_top_ups = forex_top_ups.withColumn("current_unit_price", lit(forex_price))

        # forex_cash_outs
        forex_cash_outs = self.io_utils.read_parquet_data(
            "{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "forex_cash_outs", self.t_1, self.h_1), None,
            False)
        forex_cash_outs = forex_cash_outs.filter(col("status").isin(["COMPLETED"])) \
            .withColumn("transaction_type", lit("SELL")) \
            .filter(col("partner_id") == self.partner_id)
        forex_cash_outs = forex_cash_outs.withColumn("leverage", lit(0))
        forex_cash_outs = forex_cash_outs.select(lit("forex").alias("asset_type"),
                                                 lit("forex_cash_outs").alias("asset_sub_type"),
                                                 col("forex_id").alias("asset_id"), "account_id", "user_id",
                                                 col("id").alias("transaction_id"), "created", "updated",
                                                 col("withdrawal_amount").alias("executed_quantity"),
                                                 col("unit_price").alias("executed_unit_price"),
                                                 (col("withdrawal_amount") * col("unit_price")).alias("executed_total_price"),
                                                 lit(0).alias("fees"), "transaction_type", "status",
                                                 lit(1).alias("currency_to_idr"), lit(1).alias("current_currency_to_idr"),
                                                 "transaction_time", "leverage")
        # join price with forex_cash_outs
        forex_cash_outs = forex_cash_outs.withColumn("current_unit_price", lit(forex_price))
        all_forex_txn = self.ops.get_union(forex_txn, forex_top_ups)
        all_forex_txn = self.ops.get_union(all_forex_txn, forex_cash_outs)

        return all_forex_txn

    def get_gold_transactions(self, gold_price):
        gold_txn = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "gold_transactions", self.t_1, self.h_1), None, False)
        gold_txn = gold_txn.filter(col("status").isin(["SUCCESS"])) \
            .filter(col("transaction_type").isin(["BUY", "SELL"])) \
            .filter(col("partner_id") == self.partner_id)
        gold_txn = gold_txn.withColumn("leverage", lit(0))
        gold_txn = gold_txn.select(lit("gold").alias("asset_type"),
                                   lit("gold_transactions").alias("asset_sub_type"),
                                   lit(1).alias("asset_id"), "account_id", "user_id",
                                   col("id").alias("transaction_id"), "created", "updated",
                                   col("quantity").alias("executed_quantity"),
                                   col("unit_price").alias("executed_unit_price"),
                                   col("final_amount").alias("executed_total_price"),
                                   col("fees"), "transaction_type", "status",
                                   lit(1).alias("currency_to_idr"), lit(1).alias("current_currency_to_idr"),
                                   "transaction_time", "leverage")
        # join price with forex_transactions
        gold_txn = gold_txn.withColumn("current_unit_price", lit(gold_price))

        # gold_withdrawals
        gold_withdrawals = self.io_utils.read_parquet_data("{}{}/dt={}/hour={}/".format(self.transactions_snapshot_path, "gold_withdrawals", self.t_1, self.h_1), None, False)
        gold_withdrawals = gold_withdrawals.filter(col("status").isin(["DELIVERED"])) \
            .withColumn("transaction_type", lit("SELL"))
        gold_withdrawals = gold_withdrawals.withColumn("leverage", lit(0))
        gold_withdrawals = gold_withdrawals.select(lit("gold").alias("asset_type"),
                                                   lit("gold_withdrawals").alias("asset_sub_type"),
                                                   lit(1).alias("asset_id"), "account_id", "user_id",
                                                   col("id").alias("transaction_id"), "created", "updated",
                                                   col("net_amount").alias("executed_quantity"),
                                                   col("sell_price").alias("executed_unit_price"),
                                                   (col("net_amount")*col("sell_price")).alias("executed_total_price"),
                                                   col("fee").alias("fees"), lit("GOLD_WITHDRAWAL").alias("transaction_type"), "status",
                                                   lit(1).alias("currency_to_idr"), lit(1).alias("current_currency_to_idr"),
                                                   "transaction_time", "leverage")
        # join price with forex_transactions
        gold_withdrawals = gold_withdrawals.withColumn("current_unit_price", lit(gold_price))

        all_gold_txn = self.ops.get_union(gold_txn, gold_withdrawals)

        return all_gold_txn

    def add_current_unit_price(self, initials, usdt_price, forex_price, gold_price, global_stock_price, crypto_currency_price, fund_price, options_price, crypto_futures_price):
        wallet = initials.filter(col("asset_type") == "wallet")

        gold = initials.filter(col("asset_type") == "gold").drop("current_unit_price")
        gold = gold.withColumn("current_unit_price", lit(gold_price)).withColumn("current_currency_to_idr", lit(1))

        crypto_currency = initials.filter(col("asset_type") == "crypto_currency").drop("current_unit_price")
        crypto_currency = crypto_currency.join(crypto_currency_price, on=["asset_id"], how="left").withColumn("current_currency_to_idr", lit(1))

        global_stocks = initials.filter(col("asset_type") == "global_stocks").drop("current_unit_price")
        global_stocks = global_stocks.join(global_stock_price, on=["asset_id"], how="left").withColumn("current_currency_to_idr", lit(forex_price))

        global_stock_options = initials.filter(col("asset_type") == "global_stock_options").drop("current_unit_price")
        global_stock_options = global_stock_options.join(options_price, on=["asset_id"], how="left").withColumn("current_currency_to_idr", lit(forex_price))

        crypto_futures = initials.filter(col("asset_type") == "crypto_futures").drop("current_unit_price")
        crypto_futures = crypto_futures.join(crypto_futures_price, on=["asset_id"], how="left").withColumn("current_currency_to_idr", lit(usdt_price))
        wallet_cols = wallet.columns
        all_wallet = wallet.union(gold.select(wallet_cols)).union(crypto_currency.select(wallet_cols)) \
            .union(global_stocks.select(wallet_cols)).union(global_stock_options.select(wallet_cols)) \
            .union(crypto_futures.select(wallet_cols))
        return all_wallet


    def cast_fields(self, all_txn):
        return all_txn.withColumn("created", col("created").cast(TimestampType())) \
            .withColumn("updated", col("updated").cast(TimestampType())) \
            .withColumn("transaction_time", col("transaction_time").cast(TimestampType()))

    def execute(self):
        # prices
        usdt_price, forex_price, gold_price, global_stock_price, crypto_currency_price, fund_price, options_price, crypto_futures_price = self.get_all_prices()
        self.logger.info("usdt price is: {} and forex_price is: {}".format(usdt_price, forex_price))

        # opt_ins
        opt_in_accounts = self.get_user_tiers()

        # transactions
        cols_of_txns = ["user_id", "account_id", "transaction_id", "asset_id", "created", "updated", "executed_quantity", "executed_unit_price",
                        "executed_total_price", "fees", "transaction_type", "status", "currency_to_idr", "transaction_time", "leverage", "asset_type",
                        "asset_sub_type", "current_unit_price", "current_currency_to_idr"]
        cols_of_balance = cols_of_txns

        crypto_txn = self.get_crypto_currency_transactions(crypto_currency_price).select(cols_of_txns)
        gss_txn = self.get_global_stock_transactions(global_stock_price, forex_price).select(cols_of_txns)
        options_txn = self.get_options_transactions(options_price, forex_price).select(cols_of_txns)
        gold_txn = self.get_gold_transactions(gold_price).select(cols_of_txns)
        crypto_future_txn = self.get_crypto_future_transactions(crypto_futures_price, usdt_price).select(cols_of_txns)

        all_txn = crypto_txn.union(gss_txn).union(options_txn).union(gold_txn).union(crypto_future_txn)

        # null current unit price in all txn
        null_price_in_txn = self.ops.check_null_values(all_txn, "asset_id", "current_unit_price")
        if len(null_price_in_txn) > 0:
            self.logger.error("asset ids with null current_unit_price in transactions: {}".format(null_price_in_txn))
            all_txn = all_txn.withColumn("current_unit_price", when((col("current_unit_price").isNull()), col("executed_unit_price")).otherwise(col("current_unit_price")))

        # null quantity in all txn
        null_quantity_in_txn = self.ops.check_null_values(all_txn, "asset_id", "executed_quantity")
        if len(null_quantity_in_txn) > 0:
            self.logger.error("asset ids with null executed_quantity in transactions: {}".format(null_quantity_in_txn))

        all_txn = self.cast_fields(all_txn)
        all_txn = all_txn.select(cols_of_balance)
        all_txn = all_txn.withColumn("ignore_for_gtv", when(col("asset_sub_type") == "crypto_future_funding_transactions", True) \
                                     .when(col("transaction_type") == "AIRDROP", True)
                                     .when(col("transaction_type") == "AIRDROP_SELL", True)
                                     .when(col("transaction_type") == "BUY_MA", True)
                                     .when(col("transaction_type") == "SELL_MA", True)
                                     .otherwise(False))

        # initials
        initials = self.io_utils.read_parquet_data(self.start_asset_position_path, None, False)
        initials = initials.select(cols_of_balance).withColumn("ignore_for_gtv", lit(True))
        initials = self.add_current_unit_price(initials, usdt_price, forex_price, gold_price, global_stock_price, crypto_currency_price, fund_price, options_price, crypto_futures_price)

        # Null checks
        null_price_in_initials = self.ops.check_null_values(initials, "asset_id", "current_unit_price")
        if len(null_price_in_initials) > 0:
            self.logger.error("asset ids with null current_unit_price: {}".format(null_price_in_initials))
            initials = initials.withColumn("current_unit_price", when((col("current_unit_price").isNull()),col("executed_unit_price")).otherwise(col("current_unit_price")))

        null_quantity_in_initials = self.ops.check_null_values(initials, "asset_id", "executed_quantity")
        if len(null_quantity_in_initials) > 0:
            self.logger.error("asset ids with null executed_quantity: {}".format(null_quantity_in_initials))

        # union wallet and txn
        all_txn = all_txn.union(initials)

        # add forex and usdt price
        all_txn = all_txn.withColumn("forex_price", lit(forex_price)) \
            .withColumn("usdt_price", lit(usdt_price))

        # filter only optin users txn
        all_txn = opt_in_accounts.join(all_txn, on=["account_id"], how="inner")
        all_txn = all_txn.filter(col("transaction_time") >= col("trading_competition_start_time")).drop("trading_competition_start_time", "tier")
        all_txn = self.ops.de_dupe_dataframe(all_txn, ["account_id", "user_id", "transaction_id", "transaction_time", "asset_type", "asset_sub_type", "leverage", "asset_id", "transaction_type"], "transaction_time")
        all_txn = self.update_executed_quantity_splits(all_txn)
        all_txn = all_txn.select(schema_for_all_transactions.fieldNames())
        # write txn of opt in users in s3 and hdfs both

        self.io_utils.write_parquet_file(all_txn, self.transactions_overwrite_path, partition=3)
        self.io_utils.write_parquet_file(all_txn, self.transactions_overwrite_hdfs_path, partition=3)

    def run(self):
        self.execute()
        self.spark_utils.stop_spark(self.spark)

