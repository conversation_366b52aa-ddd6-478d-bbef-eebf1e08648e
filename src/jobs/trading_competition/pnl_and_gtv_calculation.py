from src.utils.spark_utils import *
from src.utils.date_utils import *


class PNLAndGTVCalculation:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("PnL and GTV Calculation")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        # Write Paths
        self.bucket_path = self.config.get("bucket_path")
        self.batch_path = "{}/{}".format(self.bucket_path, self.config['batches']['batch_file_path'])
        self.tier_snapshot_path = "{}/{}".format(self.bucket_path, self.config['aum_tier_upgrade']['tier_snapshot_path'])
        self.partner_id = self.config["pluang_partner_id"]
        self.buy_types = self.config["buy_types"]

        # Handling Dates & Timestamps
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts") or DateUtils.get_utc_timestamp()
        self.trading_competition_start_time = DateUtils.get_utc_timestamp_from_string(
            self.config["trading_competition"]["start_time"])
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(
            self.utc_cutoff_ts, self.config)
        self.logger.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2))

    def get_user_tiers(self):
        tier = self.io_utils.read_csv_file("{}/dt={}/hour={}/".format(self.tier_snapshot_path, self.t_1,self.h_1))
        tier = tier.select("account_id", "tier", "opt_in_time") \
            .withColumn("opt_in_time", col("opt_in_time").cast(TimestampType()))
        return tier

    def get_user_niv(self):
        niv_date = self.t_1 - timedelta(days=1)
        niv = self.io_utils.read_csv_file("{}/{}/dt={}/".format(self.bucket_path, self.config['niv_path'], niv_date))
        niv = niv.select("account_id", "invested_value")
        return niv

    def get_user_gtv(self, batches):
        gtv = batches.filter((col("ignore_for_gtv") == False) & (col("transaction_type") != "WITHDRAWAL") & (col("transaction_type") != "GOLD_WITHDRAWAL"))
        gtv = gtv.withColumn("total_gtv", F.round(col("executed_quantity") * col("executed_unit_price") * col("currency_to_idr")).cast(LongType()))
        gtv = gtv.withColumn("total_gtv", when(col("asset_sub_type") == "options_contract_transactions", F.round(col("executed_quantity") * col("currency_to_idr") * col("gtv_multiplier")).cast(LongType())).otherwise(col("total_gtv")))
        gtv = gtv.groupby(["account_id"]).agg( sum("total_gtv").alias("total_gtv"),
                                               sum(when((col("asset_type") == "global_stock_options") | (col("asset_type") == "crypto_futures"),
                                                        col("total_gtv")).otherwise(0)).alias("derivatives_gtv") )
        return gtv

    def calculate_pnl_and_gtv(self, batches, tiers):
        self.logger.info("trading_competition_start_time: {}".format(self.trading_competition_start_time))

        batches = batches.withColumn("realized_pnl", col("realized_pnl").cast(LongType()))
        batches = batches.withColumn("unrealized_pnl", col("unrealized_pnl").cast(LongType()))

        pnl = batches.groupby(["account_id"]).agg(sum("realized_pnl").alias("realized_pnl"),
                                                  sum("unrealized_pnl").alias("unrealized_pnl"))

        pnl = tiers.join(pnl, on=["account_id"], how="left").fillna(0)

        pnl = pnl.withColumn("trading_competition_start_time", lit(self.trading_competition_start_time))
        pnl = pnl.withColumn("trading_competition_entry_time", F.greatest(col("opt_in_time"), col("trading_competition_start_time")))
        pnl = pnl.withColumn("execution_time", lit(self.utc_cutoff_ts))

        pnl = pnl.withColumn("pnl", (col("realized_pnl") + col("unrealized_pnl")).cast("long"))

        niv = self.get_user_niv()
        pnl = pnl.join(niv, on=["account_id"], how="left").fillna({'invested_value': 0})

        gtv = self.get_user_gtv(batches)
        pnl_gtv = pnl.join(gtv, on=["account_id"], how="left").fillna({'total_gtv': 0})

        pnl_window = Window.partitionBy(["tier"]).orderBy(col("pnl").desc(), col("total_gtv").desc(), col("invested_value").desc(), col("opt_in_time").asc())
        pnl_df = pnl_gtv.withColumn("pnl_rank", F.rank().over(pnl_window))

        gtv_window = Window.partitionBy(["tier"]).orderBy(col("total_gtv").desc(), col("invested_value").desc(), col("opt_in_time").asc())
        gtv_df = pnl_gtv.withColumn("gtv_rank", F.rank().over(gtv_window))

        return pnl_df, gtv_df

    def execute(self):
        batches = self.io_utils.read_parquet_data("{}/dt={}/hour={}".format(self.batch_path, self.t_1, self.h_1))
        tiers = self.get_user_tiers()
        pnl, gtv = self.calculate_pnl_and_gtv(batches, tiers)
        self.io_utils.write_csv_file(pnl, "{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['pnl_path'], self.t_1, self.h_1))
        self.io_utils.write_csv_file(gtv, "{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['gtv_path'], self.t_1, self.h_1))

    def run(self):
        self.execute()
        self.spark_utils.stop_spark(self.spark)