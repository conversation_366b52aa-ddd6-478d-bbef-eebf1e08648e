from src.utils.spark_utils import *
from src.utils.date_utils import *


class AUMTierUpgradeProcessor:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("aum_tier_upgrade")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")
        self.config["offset"] = self.config["trading_competition"]["offset"]
        self.aum_file_path = "{}/{}".format(self.bucket_path, self.config["aum_tier_upgrade"]["aum_file_path"])
        self.tier_event_snapshot_path = "{}/{}".format(self.bucket_path, self.config["aum_tier_upgrade"]["tier_event_snapshot_path"])
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts") or DateUtils.get_utc_timestamp()
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(self.utc_cutoff_ts, self.config)
        self.logger.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2
        ))
        self.tier_upgrade_aum_range = self.config["aum_tier_upgrade"]["tier_upgrade_range"]

    def publish_clevertap_event(self, tier):
        prev_tier_path = "{}/{}/dt={}/hour={}/".format(
            self.bucket_path, self.config["aum_tier_upgrade"]["tier_snapshot_path"], self.t_2, self.h_2
        )
        prev_tier = None
        try:
            prev_tier = self.io_utils.read_csv_file(prev_tier_path, None, False)
        except Exception as e:
            self.logger.error("Previous Tier File is not found")
        if prev_tier is not None:
            prev_tier = prev_tier.select("account_id", "user_id", col("tier").alias("prev_tier"))
            prev_tier = prev_tier.select("account_id", "user_id", "prev_tier")
            tier = tier.join(prev_tier, on=["account_id", "user_id"], how="left")
            tier = tier.filter(col("prev_tier").isNotNull() & (col("prev_tier") != col("tier")))
            current_utc_ts = DateUtils.get_utc_timestamp()
            tier = tier.withColumn("x-request-id", F.expr("uuid()").cast("binary"))
            tier = tier.withColumn("data", F.struct(
                col("user_id").alias("userId"),
                col("account_id").alias("accountId"),
                F.struct(
                    lit(current_utc_ts).alias("upgrade_tier_time"),
                    col("prev_tier").alias("previous_tier"),
                    col("tier").alias("upgrade_tier")
                ).alias("eventData"),
                lit("tc_tier_upgrade_success").alias("eventName")
            )).withColumn("type", lit("event")) \
                .withColumn("loggerContext", F.struct(col("x-request-id").cast(StringType()).alias("x-request-id")))
            tier = tier.select(
                col("account_id").cast(StringType()).alias("key"),
                F.to_json(F.struct(["data", "type", "loggerContext"])).alias("value"),
                F.array(F.struct(lit("x-request-id").alias("key"), col("x-request-id").alias("value"))).alias("headers")
            )
            self.io_utils.write_data_in_kafka(tier, self.config["kafka_topics"]["clevertap_events_topic"])

    def get_user_tier(self):
        tier_events = self.io_utils.read_parquet_data("{}/dt={}/hour={}/".format(self.tier_event_snapshot_path, self.t_1, self.h_1), None, False)
        tier_events = tier_events.withColumn("eventTime", col("eventTime").cast(TimestampType()))

        opt_in_time = tier_events.filter(col("userAction").isin(["OPT_IN"]))
        opt_in_time = self.ops.de_dupe_dataframe(opt_in_time, ["accountId"], "eventTime", type="asc")
        opt_in_time = opt_in_time.select(col("accountId").alias("account_id"), col("eventTime").alias("opt_in_time"))

        tier = tier_events.filter(col("userAction").isin(["OPT_IN", "ACCEPTED"]))
        tier = self.ops.de_dupe_dataframe(tier, ["accountId"], "eventTime")
        tier = tier.withColumn("tier", when(col("userAction") == "OPT_IN", col("currentTier")).otherwise(col("eligibleUpgradeTier")))
        tier = tier.select(
            col("accountId").alias("account_id"),
            col("userId").alias("user_id"),
            col("name"),
            col("email"),
            col("tradingCompetitionId").alias("trading_competition_id"),
            col("tier")
        )
        tier = tier.join(opt_in_time, on=["account_id"], how="left")
        return tier, tier_events

    def load_and_process_aum_data(self):
        t_2_aum = self.t_1 - timedelta(1)
        tier_upgrade_aum = self.spark.createDataFrame(self.tier_upgrade_aum_range)
        tier_upgrade_aum = tier_upgrade_aum.withColumnRenamed("tier", "eligible_upgrade_tier")

        aum = self.io_utils.read_csv_file("{}/dt={}/".format(self.aum_file_path, t_2_aum), None, False)
        aum = aum.select("account_id", col("portfolioValue").alias("aum")) \
            .withColumn("aum_to_compare", when(col("aum") < 0, 0).otherwise(col("aum")))
        aum = aum.join(
            tier_upgrade_aum,
            (col("aum_to_compare") >= col("min_aum")) & (
                        (col("aum_to_compare") < col("max_aum")) | col("max_aum").isNull()),
            "left"
        ).drop("min_aum", "max_aum", "aum_to_compare")
        return aum

    def process_tier_eligibility(self, tier):
        tier = tier.withColumn(
            "eligible_upgrade_tier",
            when((col("tier") == "CHALLENGER") & (col("eligible_upgrade_tier") == "LEGEND"),
                 col("eligible_upgrade_tier"))
            .otherwise(None)
        )

        tier = tier.withColumn("is_upgradable", when(
            ((col("eligible_upgrade_tier") == col("tier")) | col("eligible_upgrade_tier").isNull()), False).otherwise(
            True))
        tier = tier.withColumn("user_action_for_tier_upgrade", lit(None))
        return tier

    def process_declined_actions(self, tier, tier_events):
        declined_tier = tier_events.filter(col("userAction").isin(["DECLINED"]))
        declined_tier = self.ops.de_dupe_dataframe(declined_tier, ["accountId", "currentTier", "eligibleUpgradeTier"],
                                                   "eventTime")
        declined_tier = declined_tier.select(
            col("accountId").alias("account_id"),
            col("currentTier").alias("tier"),
            col("eligibleUpgradeTier").alias("eligible_upgrade_tier"),
            col("userAction").alias("user_action")
        )

        tier = tier.join(declined_tier, on=["account_id", "tier", "eligible_upgrade_tier"], how="left")
        tier = tier.withColumn("is_upgradable",
                               when(col("user_action").isNull(), col("is_upgradable")).otherwise(False))
        tier = tier.withColumn("user_action_for_tier_upgrade",
                               when(col("user_action").isNotNull(), "DECLINED").otherwise(
                                   col("user_action_for_tier_upgrade"))).drop("user_action")
        return tier

    def process_ignored_actions(self, tier, tier_events):
        ignored_tier = tier_events.filter(col("userAction").isin(["IGNORED"]))
        ignored_tier = self.ops.de_dupe_dataframe(ignored_tier, ["accountId", "currentTier", "eligibleUpgradeTier"],
                                                  "eventTime")
        ignored_tier = ignored_tier.select(
            col("accountId").alias("account_id"),
            col("currentTier").alias("tier"),
            col("eligibleUpgradeTier").alias("eligible_upgrade_tier"),
            col("userAction").alias("user_action")
        )

        tier = tier.join(ignored_tier, on=["account_id", "tier", "eligible_upgrade_tier"], how="left")
        tier = tier.withColumn("user_action_for_tier_upgrade",
                               when(col("user_action").isNotNull(), "IGNORED").otherwise(
                                   col("user_action_for_tier_upgrade")))
        return tier

    def add_metadata_columns(self, tier):
        current_ts = DateUtils.get_utc_timestamp()
        tier = tier.withColumn("updated_at", lit(current_ts))
        tier = tier.withColumn("execution_time", lit(self.utc_cutoff_ts))
        return tier

    def get_snapshotted_tier(self, tier):
        output_path = "{}/{}/dt={}/hour={}/".format(
            self.bucket_path, self.config["aum_tier_upgrade"]["tier_snapshot_path"], self.t_1, self.h_1
        )
        tier = tier.withColumn("user_action_for_tier_upgrade",
                               when(col("user_action_for_tier_upgrade").isNull(), None).otherwise(
                                   col("user_action_for_tier_upgrade")))
        self.io_utils.write_csv_file(tier, output_path)
        return tier

    def write_tier_data_to_mongo(self, tier):
        tier = tier.select("account_id", "user_id", "tier", "is_upgradable", "user_action_for_tier_upgrade",
                           "eligible_upgrade_tier", "updated_at", "aum", "trading_competition_id")
        tier = tier.withColumn("user_action_for_tier_upgrade",
                               when(col("user_action_for_tier_upgrade").isNull(), "").otherwise(
                                   col("user_action_for_tier_upgrade")))
        tier = tier.withColumn("eligible_upgrade_tier",
                               when(col("eligible_upgrade_tier").isNull(), "").otherwise(col("eligible_upgrade_tier")))

        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_config['collection'] = self.config["data_store"]["trading_competition"]["collection"]
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)
        mongo_write_config = {
            "uri": mongo_uri,
            "collection": self.config["data_store"]["trading_competition"]["collection"],
            "batch_size": "500",
            "mode": "append"
        }

        self.io_utils.write_dataset_to_mongo(tier, mongo_write_config, "aum_tier_upgrade", "update",
                                             "{'userId':1,'tradingCompetitionId':1}", add_created_at=False)
        return tier

    def execute(self):
        aum = self.load_and_process_aum_data()

        tier, tier_events = self.get_user_tier()
        tier = tier.select(col("account_id").cast(LongType()).alias("account_id"), col("user_id").cast(LongType()).alias("user_id"), "tier", "opt_in_time", "name", "email", "trading_competition_id")
        tier = tier.join(aum, on=["account_id"], how="left").fillna({'aum': 0})

        tier = self.process_tier_eligibility(tier)

        tier = self.process_declined_actions(tier, tier_events)

        tier = self.process_ignored_actions(tier, tier_events)

        tier = self.add_metadata_columns(tier)

        snapshotted_tier = self.get_snapshotted_tier(tier)

        mongo_tier_data = self.write_tier_data_to_mongo(snapshotted_tier)
        self.publish_clevertap_event(mongo_tier_data)

    def run(self):
        self.execute()
        self.spark_utils.stop_spark(self.spark)
