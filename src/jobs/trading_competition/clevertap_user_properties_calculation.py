from src.utils.spark_utils import *
from src.utils.redis_utils import *
from src.utils.date_utils import *
import json


class CleverTapUserPropertiesCalculation:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("clevertap_user_properties_calculation")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.redis_utils = RedisUtils(self.config)
        self.redis_client = self.redis_utils.get_redis_client()

        self.partner_id = self.config["pluang_partner_id"]
        self.tier_upgrade_aum_range = self.config["aum_tier_upgrade"]["tier_upgrade_range"]

        # Handling Dates & Timestamps
        self.utc_cutoff_ts = self.config.get("utc_cutoff_ts") or DateUtils.get_utc_timestamp()
        self.trading_competition_start_time = DateUtils.get_utc_timestamp_from_string(
            self.config["trading_competition"]["start_time"])
        self.trading_competition_id = self.config["trading_competition"]["id"]
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(
            self.utc_cutoff_ts, self.config)
        self.logger.info("utc_cutoff_ts: {}, t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(
            self.utc_cutoff_ts, self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2))

        # Write Paths
        self.bucket_path = self.config.get("bucket_path")
        self.pnl_path = "{}/{}/".format(self.bucket_path, self.config['pnl_path'])
        self.gtv_path = "{}/{}/".format(self.bucket_path, self.config['gtv_path'])
        self.tier_snapshot_path = "{}/{}/".format(self.bucket_path, self.config['aum_tier_upgrade']['tier_snapshot_path'])
        self.clevertap_user_properties_path = "{}/{}/".format(self.bucket_path, self.config['clevertap_user_properties_path'])
        self.clevertap_user_property_calculation_hour = 24
        self.clevertap_content_api_data_path = "{}/{}/".format(self.bucket_path, self.config['clevertap_content_api_data_path'])
        self.trading_competition_events_path = "{}/{}/".format(self.bucket_path, self.config['aum_tier_upgrade']['tier_event_snapshot_path'])
        self.trading_competition_batches_path = "{}/{}".format(self.bucket_path, self.config['batches']['batch_file_path'])
        self.options_contracts_snapshot_path = "{}/{}/{}/".format(self.bucket_path, self.config['snapshot_path'],'options_contracts')

    def get_user_tiers(self):
        tier_t_1 = self.io_utils.read_csv_file("{}dt={}/hour={}/".format(self.tier_snapshot_path, self.t_1, self.h_1))
        tier_t_1 = tier_t_1.select("user_id", "account_id", "tier", "aum")
        try:
            tier_t_2 = self.io_utils.read_csv_file("{}dt={}/hour={}/".format(self.tier_snapshot_path, (self.t_1 - timedelta(1)), self.h_1))
            tier_t_2 = tier_t_2.select("account_id", "tier")
        except Exception as e:
            tier_t_2 = None
        return tier_t_1, tier_t_2

    def get_user_pnl(self):
        pnl_t_1 = self.io_utils.read_csv_file("{}dt={}/hour={}/".format(self.pnl_path, self.t_1, self.h_1))
        pnl_t_1 = pnl_t_1.select("account_id", "pnl", "pnl_rank")
        try:
            pnl_t_2 = self.io_utils.read_csv_file("{}dt={}/hour={}/".format(self.pnl_path, (self.t_1 - timedelta(1)), self.h_1))
            pnl_t_2 = pnl_t_2.select("account_id", "pnl", "pnl_rank")
        except Exception as e:
            pnl_t_2 = None
        return pnl_t_1,pnl_t_2

    def get_user_gtv(self):
        gtv_t_1 = self.io_utils.read_csv_file("{}dt={}/hour={}/".format(self.gtv_path, self.t_1, self.h_1))
        gtv_t_1 = gtv_t_1.select("account_id", "gtv_rank", "total_gtv")
        try:
            gtv_t_2 = self.io_utils.read_csv_file("{}dt={}/hour={}/".format(self.gtv_path, (self.t_1 - timedelta(1)), self.h_1))
            gtv_t_2 = gtv_t_2.select("account_id", "gtv_rank", "total_gtv")
        except Exception as e:
            gtv_t_2 = None
        return gtv_t_1, gtv_t_2

    def rename_t_2_columns(self, df):
        cols = df.columns
        for cl in cols:
            if cl != "account_id":
                df = df.withColumnRenamed(cl, "prev_"+cl)
        return df

    def set_tc_redis_data(self,content_api_data, utc_cutoff_ts, trading_competition_start_time):
        tc_intend_data = {}
        tc_intend_data["traders"] = content_api_data["opted_in_user_count"]
        tc_intend_data["days"] = (utc_cutoff_ts - trading_competition_start_time).days
        tc_intend_data["profit"] = content_api_data["combined_pnl"]
        self.logger.info(f"Setting data in redis {tc_intend_data}")
        response = self.redis_client.set("tc_intend_data", json.dumps(tc_intend_data))
        self.logger.info(f"Response from redis {response}")

    def execute_user_properties(self):
        """
        :return:
            user_id: User Id
            account_id: Account Id
            pnl: Participants $PnL for the trading comp
            pnl_rank: User's $PnL leaderboard position
            tier: User's Current Tier
            aum: User’s Current AUM
            total_gtv: Trading Competition GTV
            prev_pnl_rank: User's $PnL leaderboard position, D-1 (Yesterday)
            delta_pnl_rank: User's $PnL Leaderboard # of positions changes (since yesterday)
            next_tier_idr_gap: The amount of IDR gap for the next tier (user level)
            next_tier: Next Tier User can upgrade to

        """
        tier_t_1, tier_t_2 = self.get_user_tiers()
        pnl_t_1, pnl_t_2 = self.get_user_pnl()
        gtv_t_1, gtv_t_2 = self.get_user_gtv()

        if tier_t_2 is not None:
            tier_t_2 = self.rename_t_2_columns(tier_t_2)
            tier_t_1 = tier_t_1.join(tier_t_2, on=["account_id"], how="left")
        else:
            tier_t_1 = tier_t_1.withColumn("prev_tier", lit(None))

        tier_t_1 = tier_t_1.join(pnl_t_1, on=["account_id"], how="left")
        if pnl_t_2 is not None:
            pnl_t_2 = self.rename_t_2_columns(pnl_t_2)
            tier_t_1 = tier_t_1.join(pnl_t_2, on=["account_id"], how="left")
        else:
            tier_t_1 = tier_t_1.withColumn("prev_pnl", lit(0)) \
                        .withColumn("prev_pnl_rank", lit(0))

        tier_t_1 = tier_t_1.join(gtv_t_1, on=["account_id"], how="left")
        if gtv_t_2 is not None:
            gtv_t_2 = self.rename_t_2_columns(gtv_t_2)
            tier_t_1 = tier_t_1.join(gtv_t_2, on=["account_id"], how="left")
        else:
            tier_t_1 = tier_t_1.withColumn("prev_gtv", lit(0)) \
                .withColumn("prev_gtv_rank", lit(0)) \
                .withColumn("prev_total_gtv", lit(0))
        tier_t_1 = tier_t_1.fillna(0)
        tier_t_1 = tier_t_1.select("user_id", "account_id", "pnl", "pnl_rank",
                                   "tier", "aum", "total_gtv", col("prev_pnl_rank").cast("long"))
        tier_t_1 = tier_t_1.withColumn("delta_pnl_rank", when(col("prev_pnl_rank").isNull(), col("pnl_rank"))
                                       .otherwise(col("pnl_rank") - col("prev_pnl_rank")))

        # assign lead tier and lead min_aum value
        tier_upgrade_aum = self.spark.createDataFrame(self.tier_upgrade_aum_range)
        window_spec = Window.orderBy("min_aum")
        tier_upgrade_aum = tier_upgrade_aum.withColumn("next_tier", F.lead("tier").over(window_spec)) \
            .withColumn("next_min_aum", F.lead("min_aum").over(window_spec)) \
            .select("tier", "next_tier", "next_min_aum")
        tier_t_1 = tier_t_1.join(tier_upgrade_aum, on=["tier"], how="left")
        tier_t_1 = tier_t_1.withColumn("next_tier_idr_gap", when(col("next_tier").isNull(), 0).otherwise(col("next_min_aum") - col("aum"))) \
            .drop("next_min_aum")

        tier_t_1 = tier_t_1.select(col("user_id").alias("identity"), col("tier").alias("Current Tier"),
                                   col("pnl").alias("TC PNL"), col("pnl_rank").alias("TC PNL Rank"),
                                   col("total_gtv").alias("TC GTV"), col("prev_pnl_rank").alias("TC PNL D-1 Rank"),
                                   col("delta_pnl_rank").alias("PNL Rank Change"), col("next_tier").alias("Next Tier"),
                                   col("next_tier_idr_gap").alias("Next Tier AUM Gap"))

        self.io_utils.write_csv_file(tier_t_1, "{}/dt={}/".format(self.clevertap_user_properties_path, self.t_1))

    def get_global_stock_options_contracts(self):
        options_contracts = self.io_utils.read_parquet_data("{}/dt={}/hour={}/".format(self.options_contracts_snapshot_path, self.t_1, self.h_1))
        options_contracts = options_contracts.select("id",col("contract_symbol").alias("symbol"))
        return options_contracts

    def get_top_performing_assets(self, pnl_t_1):
        self.logger.info("Getting top performing assets")
        top_performers = pnl_t_1.filter((col("tier") == "LEGEND") & (
                    col("pnl_rank").cast("double") <= 10)).select("account_id", "pnl", "pnl_rank")
        self.logger.info("Reading trading competition batches")
        tc_batches = self.io_utils.read_parquet_data("{}/dt={}/hour={}/".format(self.trading_competition_batches_path, self.t_1, self.h_1))
        joined_batches_df = top_performers.join(tc_batches, on=["account_id"], how="left")
        joined_batches_df = joined_batches_df.filter(col("asset_type").isin(self.config["clevertap_asset_types"]))
        joined_batches_df = joined_batches_df.groupBy("account_id", "pnl_rank", "asset_type","asset_id").agg((sum(col("realized_pnl").cast("double"))
                                                                                                                                    + sum(col("unrealized_pnl").cast("double"))).alias("asset_pnl"))
        df_ranked = joined_batches_df.withColumn("rank", F.rank().over(Window.partitionBy("account_id").orderBy(col("asset_pnl").desc())))

        self.logger.info("Getting asset data")
        crypto_currencies = self.io_utils.get_asset_data("crypto_currencies_topic","kafka").select("id","symbol")
        global_stocks = self.io_utils.get_asset_data("global_stock_topic","kafka").select("id", col("pluang_company_code").alias("symbol"))
        crypto_futures = self.io_utils.get_asset_data("crypto_future_instruments_topic","kafka").select("id", col("future_pair_symbol").alias("symbol"))
        options_contracts = self.get_global_stock_options_contracts().select("id", "symbol")


        df_top3 = df_ranked.filter(col("rank") <= 3).drop("rank")
        df_top3 = df_top3.withColumn("asset_name",when(col("asset_type") == "gold", lit("Gold")).otherwise(lit("")))
        df_top3 = df_top3.join(crypto_currencies, (df_top3.asset_id == crypto_currencies.id) & (df_top3.asset_type == "crypto_currency"), "left").drop(crypto_currencies.id)
        df_top3 = df_top3.withColumn("asset_name",when(col("asset_type") == "crypto_currency", col("symbol")).otherwise(col("asset_name"))).drop("symbol")
        df_top3 = df_top3.join(global_stocks, (df_top3.asset_id == global_stocks.id) & (df_top3.asset_type == "global_stocks"), "left").drop(global_stocks.id)
        df_top3 = df_top3.withColumn("asset_name",when(col("asset_type") == "global_stocks", col("symbol")).otherwise(col("asset_name"))).drop("symbol")
        df_top3 = df_top3.join(options_contracts, (df_top3.asset_id == options_contracts.id) & (df_top3.asset_type == "global_stock_options"), "left").drop(options_contracts.id)
        df_top3 = df_top3.withColumn("asset_name",when(col("asset_type") == "global_stock_options", col("symbol")).otherwise(col("asset_name"))).drop("symbol")
        df_top3 = df_top3.join(crypto_futures, (df_top3.asset_id == crypto_futures.id) & (df_top3.asset_type == "crypto_futures"), "left").drop(crypto_futures.id)
        df_top3 = df_top3.withColumn("asset_name",when(col("asset_type") == "crypto_futures", col("symbol")).otherwise(col("asset_name"))).drop("symbol")
        df_top3 = df_top3.withColumn("trading_competition_id", lit(self.trading_competition_id))
        pnl_top_performers = df_top3.filter((col("pnl_rank").cast("double") <= 10)).select("asset_name","asset_type", "trading_competition_id")
        pnl_top_performers = pnl_top_performers.dropDuplicates(["asset_name","asset_type"])
        pnl_asset_data_tmp = pnl_top_performers.groupBy("asset_type", "trading_competition_id").agg(collect_set("asset_name").alias("assets")).collect()
        self.logger.info(pnl_asset_data_tmp)
        pnl_asset_data = [row.asDict() for row in pnl_asset_data_tmp]
        self.logger.info("Top performing pnl assets data: {}".format(pnl_asset_data))
        return json.dumps({"pnl":pnl_asset_data})

    def execute_content_api_data(self):
        content_api_data = {}
        self.logger.info("Executing content api data")
        tier_t_1, tier_t_2 = self.get_user_tiers()
        pnl_t_1, pnl_t_2 = self.get_user_pnl()
        tc_events = self.io_utils.read_parquet_data("{}/dt={}/hour={}/".format(self.trading_competition_events_path, self.t_1, self.h_1))
        content_api_data["opted_in_user_count"] = tier_t_1.count()
        content_api_data["profitable_user_count"] = pnl_t_1.filter(col("pnl") > 0).count()
        content_api_data["combined_pnl"] = pnl_t_1.filter(col("pnl") > 0).agg(sum("pnl")).collect()[0][0]
        content_api_data["tier_upgraded_user_count"] = tc_events.filter(col("userAction") == "ACCEPTED").select("accountId").distinct().count()
        top_performing_assets = self.get_top_performing_assets(pnl_t_1)
        content_api_data["top_performing_assets"] = top_performing_assets
        content_api_data["created"] = DateUtils.get_utc_timestamp()
        content_api_data_df = self.spark.createDataFrame([content_api_data])
        self.logger.info("Content API Data: {}".format(content_api_data))
        self.io_utils.write_json_file(content_api_data_df, "{}/dt={}/hour={}/".format(self.clevertap_content_api_data_path, self.t_1, self.h_1))
        self.set_tc_redis_data(content_api_data, self.utc_cutoff_ts, self.trading_competition_start_time)

    def run(self):
        self.execute_content_api_data()
        if self.h_1 == self.clevertap_user_property_calculation_hour:
            self.execute_user_properties()
        else:
            self.logger.info(
                "Not executing clevertap user property for {} hour. It is scheduled for {} hour.".format(self.h_1, self.clevertap_user_property_calculation_hour))
        self.spark_utils.stop_spark(self.spark)
