from enum import Enum

class StakingStatus(Enum):
    STAKING_REQUESTED = "STAKING_REQUESTED"
    STAKING_IN_PROGRESS = "STAKING_IN_PROGRESS"
    UNSTAKING_REQUESTED = "UNSTAKING_REQUESTED"
    UNSTAKING_IN_WAITING = "UNSTAKING_IN_WAITING"
    UNSTAKING_IN_PROGRESS = "UNSTAKING_IN_PROGRESS"
    STAKING_CANCELLED = "STAKING_CANCELLED"
    UNSTAKING_CANCELLED = "UNSTAKING_CANCELLED"
    STAKED = "STAKED"
    UNSTAKED = "UNSTAKED"

    @classmethod
    def non_terminal_status(cls):
        return [
            cls.STAKING_REQUESTED,
            cls.STAKING_IN_PROGRESS,
            cls.UNSTAKING_REQUESTED,
            cls.UNSTAKING_IN_WAITING,
            cls.UNSTAKING_IN_PROGRESS
        ]

    @classmethod
    def terminal_status(cls):
        return [cls.STAKING_CANCELLED, cls.<PERSON><PERSON>AKING_CANCELLED, cls.STAKED, cls.<PERSON><PERSON>AKED]
