from src.utils.spark_utils import *

cashouts_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("user_bank_id", IntegerType(), True),
    StructField("nominal", LongType(), False),
    StructField("fee", LongType(), False),
    StructField("amount", LongType(), False),
    StructField("vendor_channel", StringType(), False),
    StructField("payment_channel", StringType(), False),
    StructField("vendor_transaction_id", StringType(), False),
    StructField("status", StringType(), False),
    StructField("invoice_number", StringType(), True),
    StructField("information", StringType(), True),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("wallet_type", StringType(), False),
    StructField("weekly_cashout", LongType(), True),
    StructField("today_cashout", LongType(), True),
    StructField("asset_info", StringType(), True),
    StructField("rejection_reason", StringType(), True),
    StructField("client_id", IntegerType(), True),
    StructField("auto_approved", BooleanType(), True),
    StructField("rejection_reason_id", StringType(), True),
    StructField("transaction_time", TimestampType(), True),
    StructField("kki_sync", StringType(), True),
    StructField("transaction_fee_info", StringType(), True),
])

topups_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("nominal", LongType(), False),
    StructField("fee", LongType(), False),
    StructField("amount", LongType(), False),
    StructField("vendor_channel", StringType(), False),
    StructField("payment_channel", StringType(), False),
    StructField("vendor_transaction_id", StringType(), False),
    StructField("status", StringType(), False),
    StructField("information", StringType(), True),
    StructField("valid_payment_time", TimestampType(), False),
    StructField("payment_time", TimestampType(), True),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("invoice_number", StringType(), True),
    StructField("wallet_type", StringType(), False),
    StructField("asset_info", StringType(), True),
    StructField("transaction_receipt", StringType(), True),
    StructField("client_id", IntegerType(), True),
    StructField("transaction_time", TimestampType(), True),
    StructField("internal_status", StringType(), True),
    StructField("rejection_reason", StringType(), True),
    StructField("kki_sync", StringType(), True),
    StructField("account_name", StringType(), True),
    StructField("transaction_fee_info", StringType(), True),
])