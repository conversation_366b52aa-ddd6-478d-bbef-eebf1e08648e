from src.utils.spark_utils import *

fund_transactions_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("fund_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("quantity", DoubleType(), True),
    StructField("estimated_quantity", DoubleType(), True),
    StructField("unit_price", DoubleType(), True),
    StructField("estimated_unit_price", DoubleType(), True),
    StructField("fee", DoubleType(), True),
    StructField("admin_fee", LongType(), True),
    StructField("final_price", DoubleType(), True),
    StructField("vendor_channel", StringType(), True),
    StructField("payment_channel", StringType(), True),
    StructField("user_bank_id", IntegerType(), True),
    StructField("matched", BooleanType(), True),
    StructField("info", StringType(), True),  # jsonb
    StructField("user_bank_details", StringType(), True),  # jsonb
    StructField("ref_id", StringType(), False),
    StructField("rejection_reason", StringType(), True),
    StructField("fund_sell_type", StringType(), True),
    StructField("status", StringType(), False),
    StructField("transaction_type", StringType(), False),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("disbursement_id", StringType(), True),
    StructField("disbursement_status", StringType(), True),
    StructField("disbursement_details", StringType(), True),  # jsonb
    StructField("rejection_reason_json", StringType(), True),  # jsonb
    StructField("currency", StringType(), True),
    StructField("amount", DoubleType(), True),
    StructField("usd_to_idr", DoubleType(), True),
    StructField("transaction_time", TimestampType(), True),
    StructField("transaction_fee_info", StringType(), True),  # jsonb
])