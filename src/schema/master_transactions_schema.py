"""
Master Transaction Schemas
Defines PySpark schemas for all master transaction asset types based on test data structure.
"""

from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, BooleanType, TimestampType
from src.schema import crypto_currency_schema, global_stocks_schema, crypto_futures_schema, fund_schema, forex_schema, gold_schema, options_schema, cash_transactions_schema, indo_stocks_v2_schema


def get_crypto_currency_transactions_schema():
    """Schema for crypto_currency_transactions based on test data structure"""
    return crypto_currency_schema.crypto_currency_transactions_schema


def get_crypto_currency_pocket_transactions_schema():
    """Schema for crypto_currency_pocket_transactions"""
    return crypto_currency_schema.crypto_currency_pocket_transactions_schema


def get_crypto_currency_mission_rewards_schema():
    """Schema for crypto_currency_mission_rewards"""
    return StructType([
        StructField("id", StringType(), True),
        StructField("crypto_currency_id", StringType(), True),
        StructField("user_id", StringType(), True),
        StructField("account_id", StringType(), True),
        StructField("client_id", StringType(), True),
        StructField("partner_id", StringType(), True),
        StructField("quantity", StringType(), True),
        StructField("unit_price", StringType(), True),
        StructField("total_price", StringType(), True),
        StructField("ref_id", StringType(), True),
        StructField("status", StringType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("created", StringType(), True),
        StructField("updated", StringType(), True),
        StructField("transaction_time", StringType(), True),
        StructField("claim_date", StringType(), True),
        StructField("mission_id", StringType(), True),
        StructField("mission_type", StringType(), True),
    ])


def get_crypto_currency_pluangcuan_yields_schema():
    """Schema for crypto_currency_pluangcuan_yields"""
    return StructType([
        StructField("id", StringType(), True),
        StructField("crypto_currency_id", StringType(), True),
        StructField("user_id", StringType(), True),
        StructField("account_id", StringType(), True),
        StructField("client_id", StringType(), True),
        StructField("partner_id", StringType(), True),
        StructField("quantity", StringType(), True),
        StructField("unit_price", StringType(), True),
        StructField("total_price", StringType(), True),
        StructField("ref_id", StringType(), True),
        StructField("status", StringType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("created", StringType(), True),
        StructField("updated", StringType(), True),
        StructField("transaction_time", StringType(), True),
        StructField("yield_type", StringType(), True),
        StructField("yield_rate", StringType(), True),
    ])


def get_crypto_margin_wallet_transfers_schema():
    """Schema for crypto_margin_wallet_transfers"""
    return StructType([
        StructField("id", StringType(), True),
        StructField("crypto_currency_id", StringType(), True),
        StructField("user_id", StringType(), True),
        StructField("account_id", StringType(), True),
        StructField("client_id", StringType(), True),
        StructField("partner_id", StringType(), True),
        StructField("quantity", StringType(), True),
        StructField("unit_price", StringType(), True),
        StructField("total_price", StringType(), True),
        StructField("ref_id", StringType(), True),
        StructField("status", StringType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("created", StringType(), True),
        StructField("updated", StringType(), True),
        StructField("transaction_time", StringType(), True),
        StructField("transfer_type", StringType(), True),
        StructField("from_wallet", StringType(), True),
        StructField("to_wallet", StringType(), True),
    ])


def get_global_stock_transactions_schema():
    """Schema for global_stock_transactions"""
    return StructType([
        StructField("id", StringType(), True),
        StructField("global_stock_id", StringType(), True),
        StructField("user_id", StringType(), True),
        StructField("account_id", StringType(), True),
        StructField("client_id", StringType(), True),
        StructField("partner_id", StringType(), True),
        StructField("quantity", StringType(), True),
        StructField("unit_price", StringType(), True),
        StructField("fee", StringType(), True),
        StructField("total_price", StringType(), True),
        StructField("ref_id", StringType(), True),
        StructField("status", StringType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("info", StringType(), True),
        StructField("created", StringType(), True),
        StructField("updated", StringType(), True),
        StructField("exchange_total_price", StringType(), True),
        StructField("source", StringType(), True),
        StructField("executed_quantity", StringType(), True),
        StructField("executed_unit_price", StringType(), True),
        StructField("executed_total_price", StringType(), True),
        StructField("order_type", StringType(), True),
        StructField("transaction_time", StringType(), True),
        StructField("taxes_and_fees", StringType(), True),
        StructField("taxation_fee", StringType(), True),
        StructField("fx_rate", StringType(), True),
        StructField("fx_currency", StringType(), True),
    ])


def get_schema_for_source(source_name: str):
    """Get schema for a specific data source"""
    schema_mapping = {
        "crypto_currency_transactions": get_crypto_currency_transactions_schema,
        "crypto_currency_pocket_transactions": get_crypto_currency_pocket_transactions_schema,
        "crypto_currency_mission_rewards": get_crypto_currency_mission_rewards_schema,
        "crypto_currency_pluangcuan_yields": get_crypto_currency_pluangcuan_yields_schema,
        "crypto_margin_wallet_transfers": get_crypto_margin_wallet_transfers_schema,
        "global_stock_transactions": get_global_stock_transactions_schema,
    }
    
    if source_name in schema_mapping:
        return schema_mapping[source_name]()
    else:
        print(f"⚠️  No schema defined for source: {source_name}")
        return None
