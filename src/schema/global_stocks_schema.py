from src.utils.spark_utils import *

global_stock_transactions_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("global_stock_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("quantity", DoubleType(), False),
    StructField("unit_price", DoubleType(), False),
    StructField("transaction_fee", DoubleType(), False),
    StructField("premium_fee", DoubleType(), False),
    StructField("total_price", DoubleType(), False),
    StructField("executed_total_price", DoubleType(), True),
    StructField("estimated_total_price", DoubleType(), True),
    StructField("estimated_quantity", DoubleType(), True),
    StructField("executed_quantity", DoubleType(), True),
    StructField("estimated_unit_price", DoubleType(), True),
    StructField("executed_unit_price", DoubleType(), True),
    StructField("ref_id", StringType(), False),
    StructField("order_type", StringType(), False),
    StructField("status", StringType(), False),
    StructField("transaction_type", StringType(), False),
    StructField("source", StringType(), False),
    StructField("hedging_type", StringType(), False),
    StructField("expiry_date_time", TimestampType(), True),
    StructField("info", StringType(), True),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("usd_to_idr", LongType(), False),
    StructField("forex_price_id", IntegerType(), True),
    StructField("cancelled_type", StringType(), True),
    StructField("effective_spread", DoubleType(), False),
    StructField("raw_spread", DoubleType(), False),
    StructField("exchange_mid_price", DoubleType(), False),
    StructField("lock_type", StringType(), False),
    StructField("amend_order_info", StringType(), True),
    StructField("order_sub_type", StringType(), True),
    StructField("vendor_transaction_id", StringType(), True),
    StructField("advanced_order_price_info", StringType(), True),
    StructField("linked_transaction_info", StringType(), True),
    StructField("wallet_type", StringType(), True),
    StructField("realised_gain", DoubleType(), True),
    StructField("trading_margin_used", DoubleType(), True),
    StructField("recurring_order_type", StringType(), True),
    StructField("recurring_transaction_id", StringType(), True),
    StructField("retry_info", StringType(), True),
    StructField("client_order_id", StringType(), True),
    StructField("order_details", StringType(), True),
    StructField("user_pocket_txn_ref_id", StringType(), True),
    StructField("user_pocket_id", IntegerType(), True),
    StructField("reward_quantity_used", DoubleType(), True),
    StructField("trading_hours", StringType(), True),
    StructField("transaction_fee_config_id", IntegerType(), True),
    StructField("is_jfx_synced", BooleanType(), True),
    StructField("stock_type", StringType(), True),
    StructField("transaction_time", TimestampType(), True),
    StructField("sub_state", StringType(), True),
    StructField("device_meta_info", StringType(), True),
    StructField("waived_off_fee", DoubleType(), True),
    StructField("stop_out_detail_id", IntegerType(), True),
    StructField("cancel_request_id", IntegerType(), True),
    StructField("transaction_fee_info", StringType(), True),
])

options_contract_transactions_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("options_contract_id", IntegerType(), False),
    StructField("global_stock_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("quantity", DoubleType(), False),
    StructField("unit_price", DoubleType(), False),
    StructField("total_price", DoubleType(), False),
    StructField("estimated_quantity", DoubleType(), True),
    StructField("estimated_unit_price", DoubleType(), True),
    StructField("estimated_total_price", DoubleType(), True),
    StructField("executed_quantity", DoubleType(), True),
    StructField("executed_unit_price", DoubleType(), True),
    StructField("executed_total_price", DoubleType(), True),
    StructField("order_type", StringType(), False),
    StructField("transaction_type", StringType(), False),
    StructField("status", StringType(), False),
    StructField("time_in_force", StringType(), False),
    StructField("expiry_date_time", TimestampType(), True),
    StructField("ref_id", StringType(), False),
    StructField("vendor_transaction_id", StringType(), True),
    StructField("info", StringType(), True),
    StructField("client_order_id", StringType(), True),
    StructField("order_details", StringType(), True),
    StructField("sub_state", StringType(), True),
    StructField("transaction_fee_config_id", IntegerType(), True),
    StructField("transaction_fee", DoubleType(), False),
    StructField("cancelled_type", StringType(), True),
    StructField("lock_type", StringType(), False),
    StructField("retry_info", StringType(), True),
    StructField("usd_to_idr", LongType(), False),
    StructField("transaction_time", TimestampType(), True),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("device_meta_info", StringType(), True),
    StructField("is_jfx_synced", BooleanType(), True),
    StructField("transaction_fee_info", StringType(), True),
    StructField("waived_off_fee", DoubleType(), True),
    StructField("stop_price", DoubleType(), True),
    StructField("stop_loss", DoubleType(), True),
    StructField("take_profit", DoubleType(), True),
    StructField("linked_transaction_info", StringType(), True),
])

ph_global_stock_returns = StructType([
    StructField("id", LongType(), False),
    StructField("global_stock_id", LongType(), False),
    StructField("account_id", LongType(), False),
    StructField("user_id", LongType(), False),
    StructField("total_quantity", DoubleType(), False),
    StructField("batch_quantity", DoubleType(), False),
    StructField("weighted_cost", DoubleType(), False),
    StructField("realised_gain", DoubleType(), False),
    StructField("weighted_cost_in_idr", LongType(), False),
    StructField("usd_to_idr_quantity_average", LongType(), False),
    StructField("realised_gain_in_idr", LongType(), False),
    StructField("total_dividend", DoubleType(), False),
    StructField("total_dividend_in_idr", LongType(), False),
    StructField("total_trading_margin_used", DoubleType(), False),
    StructField("free_credit", DoubleType(), False),
    StructField("free_credit_in_idr", LongType(), False),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False)
])

ph_global_stock_accounts_schema = StructType([
    StructField("id", LongType(), False),
    StructField("global_stock_id", LongType(), False),
    StructField("user_id", LongType(), False),
    StructField("account_id", LongType(), False),
    StructField("partner_id", LongType(), False),
    StructField("client_id", LongType(), False),
    StructField("balance", DoubleType(), False),
    StructField("first_order_date", TimestampType(), True),
    StructField("pending_transactions", LongType(), False),
    StructField("reward_balance", DoubleType(), False),
    StructField("pending_reward_balance", DoubleType(), False),
    StructField("total_reward_balance", DoubleType(), False),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False)
])

options_corporate_actions_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("global_stock_id", IntegerType(), False),
    StructField("occ_id", StringType(), False),
    StructField("ca_type", StringType(), False),
    StructField("effective_date", DateType(), False),
    StructField("status", StringType(), False),
    StructField("is_verified", BooleanType(), True),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False)
])

options_ca_affected_contracts_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("corporate_action_id", IntegerType(), False),
    StructField("global_stock_id", IntegerType(), False),
    StructField("options_contract_id", IntegerType(), False),
    StructField("pre_adjustment_symbol", StringType(), False),
    StructField("post_adjustment_symbol", StringType(), False),
    StructField("pre_adjustment_strike_price", DoubleType(), False),
    StructField("post_adjustment_strike_price", DoubleType(), False),
    StructField("pre_adjustment_multiplier", IntegerType(), False),
    StructField("post_adjustment_multiplier", IntegerType(), False),
    StructField("pre_adjustment_underlier_symbol", StringType(), False),
    StructField("post_adjustment_underlier_symbol", StringType(), False),
    StructField("pre_adjustment_quantity", DoubleType(), False),
    StructField("post_adjustment_quantity", DoubleType(), False),
    StructField("enable_buy", BooleanType(), False),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False)
])

options_ca_user_position_adjustments_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("corporate_action_id", IntegerType(), False),
    StructField("options_contract_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("pre_adjustment_quantity", DoubleType(), False),
    StructField("post_adjustment_quantity", DoubleType(), False),
    StructField("pre_adjustment_weighted_cost", DoubleType(), False),
    StructField("post_adjustment_weighted_cost", DoubleType(), False),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("transaction_time", TimestampType(), False),
    StructField("status", StringType(), False)
])
