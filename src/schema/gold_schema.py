from src.utils.spark_utils import *

gold_transactions_schema = StructType([
    StructField("id", LongType(), True),
    StructField("partner_id", LongType(), True),
    StructField("account_id", LongType(), True),
    StructField("transaction_type", StringType(), True),
    StructField("quantity", StringType(), True),
    StructField("unit_price", LongType(), True),
    StructField("fees", LongType(), True),
    StructField("final_amount", LongType(), True),
    StructField("partner_reference", StringType(), True),
    StructField("commission_percent", StringType(), True),
    StructField("commission_amount", LongType(), True),
    StructField("status", StringType(), True),
    StructField("created", TimestampType(), True),
    StructField("transaction_time", TimestampType(), True),
    StructField("updated", TimestampType(), True),
    StructField("meta", StringType(), True),
    StructField("client_id", LongType(), True),
    StructField("user_id", LongType(), True),
    StructField("auto_invest", StringType(), True),
    StructField("round_up", StringType(), True),
    StructField("jfx_synced", StringType(), True)])

installment_payments_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("gold_loan_id", IntegerType(), False),
    StructField("installment_index", IntegerType(), False),
    StructField("money_paid", LongType(), False),
    StructField("money_fine", LongType(), False),
    StructField("installment", LongType(), False),
    StructField("status", StringType(), False),
    StructField("date_paid_on", TimestampType(), True),
    StructField("due_date", TimestampType(), True),
    StructField("payment_channel", StringType(), True),
    StructField("admin_id", IntegerType(), True),
    StructField("commission_percent", DecimalType(38, 18), False),  # Adjust precision & scale if needed
    StructField("commission_amount", LongType(), False),
    StructField("paid_principal", LongType(), True),
    StructField("partner_reference", StringType(), False),
    StructField("deleted", TimestampType(), True),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("jfx_synced", BooleanType(), True),
    StructField("jfx_sync_date", TimestampType(), True),
    StructField("transaction_time", TimestampType(), True),
])

gold_withdrawals_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("gold_amount", DecimalType(38, 18), False),  # Adjust precision/scale as needed
    StructField("delivery_address", StringType(), False),
    StructField("order_ref", StringType(), True),
    StructField("invoice_link", StringType(), True),
    StructField("receipt_link", StringType(), True),
    StructField("status", StringType(), False),  # Enum mapped to String
    StructField("information", StringType(), True),  # JSONB as String or use MapType if structured
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("fee", DecimalType(38, 18), False),
    StructField("net_amount", DecimalType(38, 18), False),
    StructField("old_invoice_number", StringType(), True),
    StructField("old_merchant_id", IntegerType(), True),
    StructField("shipping_method", StringType(), False),
    StructField("delivery_fee", LongType(), False),
    StructField("printing_fee", LongType(), False),
    StructField("printing_fee_type", StringType(), False),  # Enum as String
    StructField("denomination", StringType(), True),  # JSONB as String (or MapType if parsed)
    StructField("unit_price", LongType(), True),
    StructField("insurance_fee", LongType(), True),
    StructField("final_price", LongType(), True),
    StructField("client_id", IntegerType(), True),
    StructField("ref_id", StringType(), True),
    StructField("address_proof_link", StringType(), True),
    StructField("delivery_address_type", StringType(), True),  # Enum as String
    StructField("buy_price", LongType(), True),
    StructField("sell_price", LongType(), True),
    StructField("jfx_synced", BooleanType(), True),
    StructField("jfx_sync_date", TimestampType(), True),
    StructField("transaction_time", TimestampType(), True),
])
