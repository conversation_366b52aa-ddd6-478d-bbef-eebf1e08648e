from src.schema.global_stocks_schema import *
from src.schema.crypto_futures_schema import *
from src.schema.crypto_currency_schema import *
from src.schema.fund_schema import *
from src.schema.forex_schema import *
from src.schema.gold_schema import *
from src.schema.options_schema import *
from src.schema.cash_transactions_schema import *
from src.schema.indo_stocks_v2_schema import *
from src.schema.master_transactions_schema import (
    get_crypto_currency_transactions_schema,
    get_crypto_currency_pocket_transactions_schema,
    get_crypto_currency_mission_rewards_schema,
    get_crypto_currency_pluangcuan_yields_schema,
    get_crypto_margin_wallet_transfers_schema,
    get_global_stock_transactions_schema,
    get_schema_for_source
)

schema_dict = {
    "ph_global_stock_accounts": ph_global_stock_accounts_schema,
    "ph_global_stock_returns": ph_global_stock_returns,
    "options_corporate_actions": options_corporate_actions_schema,
    "options_ca_affected_contracts": options_ca_affected_contracts_schema,
    "options_ca_user_position_adjustments": options_ca_user_position_adjustments_schema,
    # Master transaction schemas
}