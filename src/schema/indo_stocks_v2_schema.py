from src.utils.spark_utils import *

fields = [
    ("id", LongType()),
    ("created", TimestampType()),
    ("updated", TimestampType()),
    ("account_id", LongType()),
    ("realised_gain", LongType()),
    ("stock_id", LongType()),
    ("user_id", LongType()),
    ("total_quantity", StringType()),
    ("weighted_cost", StringType()),
    ("dividend", FloatType())
]

schema_for_indo_stock_returns = StructType([
    StructField(name, dtype, nullable=True) for name, dtype in fields
])
