from src.utils.spark_utils import *

forex_transactions_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("forex_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("partner_price_id", IntegerType(), False),
    StructField("quantity", DoubleType(), False),  # numeric
    StructField("unit_price", LongType(), False),  # int8
    StructField("fee", LongType(), False),
    StructField("total_price", LongType(), False),
    StructField("ref_id", StringType(), False),
    StructField("status", StringType(), False),  # ENU<PERSON>ructField("transaction_type", StringType(), False),  # ENUM
    StructField("info", StringType(), True),  # jsonb
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("centralized_wallet_id", StringType(), True),
    StructField("transaction_time", TimestampType(), True),
    StructField("transaction_fee_config_id", IntegerType(), True),
    StructField("device_meta_info", StringType(), True),  # jsonb
])

forex_top_ups_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("forex_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("status", StringType(), False),  # ENUM
    StructField("top_up_amount", DoubleType(), False),  # numeric
    StructField("source_bank", StringType(), False),
    StructField("source_bank_account_number", StringType(), False),
    StructField("proof_of_transfer", StringType(), False),
    StructField("top_up_form", StringType(), False),
    StructField("bank_statement_transaction_id", StringType(), True),
    StructField("bank_statement_transaction_proof", StringType(), True),
    StructField("final_amount", DoubleType(), True),  # numeric
    StructField("unit_price", LongType(), True),  # int8
    StructField("rejection_reason", StringType(), True),  # jsonb
    StructField("internal_rejection_reason", StringType(), True),
    StructField("state_transition_detail", StringType(), True),  # jsonb
    StructField("info", StringType(), True),  # jsonb
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("transaction_time", TimestampType(), True),
])

forex_cash_outs_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("forex_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("status", StringType(), False),  # ENUM
    StructField("withdrawal_amount", DoubleType(), False),  # numeric
    StructField("destination_bank_account", StringType(), False),
    StructField("destination_bank_account_owner", StringType(), False),
    StructField("destination_bank_account_owner_address", StringType(), False),
    StructField("destination_bank", StringType(), False),
    StructField("destination_bank_swift", StringType(), False),
    StructField("destination_bank_address", StringType(), False),
    StructField("destination_bank_city", StringType(), False),
    StructField("destination_bank_country", StringType(), False),
    StructField("proof_of_request", StringType(), False),
    StructField("proof_of_bank_account_ownership", StringType(), True),
    StructField("unit_price", LongType(), True),
    StructField("cs_call_confirmation_link", StringType(), True),
    StructField("bank_transfer_slip", StringType(), True),
    StructField("finance_jira_link", StringType(), True),
    StructField("rejection_reason", StringType(), True),  # jsonb
    StructField("internal_rejection_reason", StringType(), True),
    StructField("state_transition_detail", StringType(), True),  # jsonb
    StructField("info", StringType(), True),  # jsonb
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("transaction_time", TimestampType(), True),
])

forex_unhedged_transactions_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("transaction_id", IntegerType(), False),
    StructField("table_name", StringType(), False),
    StructField("forex_id", IntegerType(), False),
    StructField("quantity", DoubleType(), False),
    StructField("unit_price", DoubleType(), False),
    StructField("transaction_type", StringType(), False),
    StructField("status", StringType(), False),
    StructField("inventory_order_id", IntegerType(), True),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("transaction_time", TimestampType(), True),
])
