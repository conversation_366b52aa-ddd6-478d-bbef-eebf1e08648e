from src.utils.spark_utils import *

crypto_future_transactions_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("source", StringType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("crypto_future_user_id", IntegerType(), False),
    StructField("crypto_future_instrument_id", IntegerType(), False),
    StructField("crypto_margin_wallet_id", IntegerType(), False),
    StructField("transaction_type", StringType(), False),
    StructField("order_type", StringType(), False),
    StructField("ref_id", StringType(), False),
    StructField("exec_inst", StringType(), True),
    StructField("exchange_time_in_force", StringType(), True),
    StructField("exchange_order_id", StringType(), True),
    StructField("client_order_id", StringType(), True),
    StructField("failure_reason", StringType(), True),  # jsonb
    StructField("transaction_charges_breakdown", StringType(), True),  # jsonb
    StructField("exchange_status", StringType(), False),
    StructField("user_status", StringType(), False),
    StructField("unit_price", DecimalType(38, 18), True),
    StructField("executed_unit_price", DecimalType(38, 18), False),
    StructField("executed_quantity", DecimalType(38, 18), False),
    StructField("quantity", DecimalType(38, 18), False),
    StructField("total_price", DecimalType(38, 18), False),
    StructField("estimate_total_price", DecimalType(38, 18), False),
    StructField("estimate_realised_gain", DecimalType(38, 18), False),
    StructField("realised_gain", DecimalType(38, 18), False),
    StructField("exchange_raw_response", StringType(), True),
    StructField("transaction_time", TimestampType(), True),
    StructField("offsets", StringType(), True),  # jsonb
    StructField("info", StringType(), True),     # jsonb
    StructField("leverage", DecimalType(38, 18), False),
    StructField("initial_margin_required", DecimalType(38, 18), True),
    StructField("crypto_future_position_id", IntegerType(), True),
    StructField("cfx_order_type", StringType(), True),
    StructField("client_order_link_id", StringType(), True),
    StructField("cfx_execution_instructions", StringType(), True),  # _text as String
    StructField("advanced_order_info", StringType(), True),  # jsonb
    StructField("trigger_price", DecimalType(38, 18), True),
])