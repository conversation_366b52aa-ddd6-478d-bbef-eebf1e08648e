from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.utils.python_utils import PythonUtils
from datetime import datetime, timedelta
from redis import Redis
import json


class AssetUtils:
    def __init__(self, spark: SparkSession, config):
        self.spark = spark
        self.config = config
        self.logger = get_logger()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.bucket_path = self.config.get("bucket_path")

    def get_crypto_assets(self):
        self.logger.info("Reading Crypto assets from kafka")
        df_crypto_code = self.io_utils.read_from_kafka_in_memory(
            self.config["bootstrap_servers"], self.config["kafka_topics"]["crypto_currencies_topic"]
        )
        df_crypto_code = df_crypto_code.select(
            col("value.id").alias("id"), col("value.symbol").alias("symbol"),
            col("value.price_precision").alias("price_precision"), col("value.active").alias("active"),
            col("value.safety_label").alias("safety_label"), col("value.enable_sell").alias("enable_sell"),
            col("value.enable_withdrawal").alias("enable_withdrawal"), col("value.updated").alias("updated"))
        df_crypto_code = self.ops.de_dupe_dataframe(df_crypto_code, ["id"], "updated")
        self.logger.info("Reading Crypto codes from kafka is successful")
        return df_crypto_code

    def get_fund_assets(self):
        self.logger.info("reading fund codes from kafka topic")
        df_fund_code = self.io_utils.read_from_kafka_in_memory(
            self.config["bootstrap_servers"], self.config["kafka_topics"]["funds_topic"]
        )
        df_fund_code = df_fund_code.select(
            col("value.id").alias("id"), col("value.code").alias("code"), col("value.name").alias("name"),
            col("value.__source_ts_ms").cast(LongType()).alias("__source_ts_ms"))
        df_fund_code = self.ops.de_dupe_dataframe(df_fund_code, ["id"], "__source_ts_ms")
        self.logger.info("Successfully read fund codes from kafka topic")
        return df_fund_code

    def get_crypto_future_instrument_assets(self):
        self.logger.info("reading crypto future instruments code from kafka topic")
        crypto_futures_instruments = self.io_utils.read_from_kafka_in_memory(
            self.config["bootstrap_servers"], self.config["kafka_topics"]["crypto_future_instruments_topic"]
        )
        crypto_futures_instruments = self.ops.explode_col_df(crypto_futures_instruments)
        crypto_futures_instruments = self.ops.de_dupe_dataframe(crypto_futures_instruments, ["id"], "__source_ts_ms")
        self.logger.info("successfully read crypto future instruments from kafka")
        return crypto_futures_instruments

    def get_global_stock_assets(self):
        self.logger.info("reading global stock codes from kafka topic")
        df_global_stock_code = self.io_utils.read_from_kafka_in_memory(
            self.config["bootstrap_servers"], self.config["kafka_topics"]["global_stock_topic"]
        )
        df_global_stock_code = self.ops.explode_col_df(df_global_stock_code)
        df_global_stock_code = self.ops.de_dupe_dataframe(df_global_stock_code, ["id"], "__source_ts_ms")
        self.logger.info("Successfully read global stock codes from kafka topic")
        return df_global_stock_code

    def get_indo_stock_mapping(self):
        """Fetch stock id to code mapping from the configured source."""
        self.logger.info("Loading stock mapping from Kafka")

        indo_stock_mapping_df = self.io_utils.get_asset_data("indo_stocks_v2_topic", "kafka")
        self.logger.info("Stock mapping loaded")
        return indo_stock_mapping_df

