from src.utils.spark_utils import *
from src.utils.python_utils import PythonUtils


class Operations:
    def __init__(self, spark: SparkSession):
        self.spark = spark

    def convert_columns_snake_to_camel_case(self, df):
        original_columns = df.columns
        for cols in original_columns:
            input_str = cols
            renamed_col = PythonUtils.convert_snake_to_camel_case(input_str)
            df = df.withColumnRenamed(cols, renamed_col)
        return df

    def de_dupe_dataframe(self, df, keys, over, **kwargs):
        if (kwargs.get("type") is not None) and (kwargs.get("type") == "asc"):
            window = Window.partitionBy([col(x) for x in keys]).orderBy(col(over).asc())
        else:
            window = Window.partitionBy([col(x) for x in keys]).orderBy(col(over).desc())
        df = df.withColumn("row", row_number().over(window)).filter(col("row") == 1).drop("row")
        return df

    def apply_schema_from(self, target: DataFrame, source: DataFrame):
        schema = source.schema
        target = target.select(source.columns)
        for field in schema.fields:
            if field.name in target.columns:
                target = target.withColumn(field.name, col(field.name).cast(field.dataType))
        return target

    def get_union(self, df1, df2):
        if df1 is None:
            return df2
        elif df2 is None:
            return df1
        else:
            column = df1.columns
            df2 = df2.select(column)
            return df1.union(df2)

    def explode_col_df(self, df):
        keys_df = df.select(F.explode(F.map_keys(col("value")))).distinct()
        keys = list(map(lambda row: row[0], keys_df.collect()))
        key_cols = list(map(lambda x: col("value").getItem(x).alias(str(x)), keys))
        df = df.select(key_cols)
        return df

    def check_non_null_values(self, df, primary_key, filter_col=None):
        null_val_df = df.filter(col(filter_col).isNotNull()).select(primary_key).distinct().rdd.flatMap(
            lambda x: x).collect()
        return null_val_df

    def check_null_values(self, df, primary_key, filter_col=None):
        null_val_df = df.filter(col(filter_col).isNull()).select(primary_key).distinct().rdd.flatMap(lambda x: x).collect()
        return null_val_df

    def cast_to_bigdecimal(self, df, cols, remove_trailing_zero=False):
        for cl in cols:
            df = df.withColumn(cl, F.format_string('%.16f', col(cl).cast("double")))
            if remove_trailing_zero:
                df = df.withColumn(cl, F.expr(f"regexp_replace({cl}, '([.]?)(0+)$', '')"))
        return df

    def create_jkt_month_text_df(self, tax_year):
        df = self.spark.createDataFrame(
            [
                (1, "Januari "+str(tax_year)),
                (2, "Februari "+str(tax_year)),
                (3, "Maret "+str(tax_year)),
                (4, "April "+str(tax_year)),
                (5, "Mei "+str(tax_year)),
                (6, "Juni "+str(tax_year)),
                (7, "Juli "+str(tax_year)),
                (8, "Agustus "+str(tax_year)),
                (9, "September "+str(tax_year)),
                (10, "Oktober "+str(tax_year)),
                (11, "November "+str(tax_year)),
                (12, "Desember "+str(tax_year)),
            ],
            ["month", "monthText"]
        )
        return df

    def create_english_month_text_df(self, tax_year):
        df = self.spark.createDataFrame(
            [
                (1, "January "+str(tax_year)),
                (2, "February "+str(tax_year)),
                (3, "March "+str(tax_year)),
                (4, "April "+str(tax_year)),
                (5, "May "+str(tax_year)),
                (6, "June "+str(tax_year)),
                (7, "July "+str(tax_year)),
                (8, "August "+str(tax_year)),
                (9, "September "+str(tax_year)),
                (10, "October "+str(tax_year)),
                (11, "November "+str(tax_year)),
                (12, "December "+str(tax_year)),
            ],
            ["month", "monthText"]
        )
        return df

    def create_jkt_month_df(self):
        df = self.spark.createDataFrame(
            [
                (1, "Jan"),
                (2, "Feb"),
                (3, "Mar"),
                (4, "Apr"),
                (5, "Mei"),
                (6, "Jun"),
                (7, "Jul"),
                (8, "Agu"),
                (9, "Sep"),
                (10, "Okt"),
                (11, "Nov"),
                (12, "Des"),
            ],
            ["month", "month_name"]
        )
        return df

    