import json
import os
from src.utils.custom_logger import init_logger
from src.utils.date_utils import DateUtils


class ConfigLoader:
    def __init__(self, env):
        self.env = env

    def load_secrets(self):
        if self.env == "local":
            path = os.path.join(os.path.dirname(__file__), "../../config/secret.json")
        else:
            path = "secret.json"
        with open(path, "r") as f:
            config = json.load(f)
        env = config.get("env", "dev")
        init_logger(env)
        return config

    def load_job_config(self):
        if self.env == "local":
            path = os.path.join(os.path.dirname(__file__), "../../config/config.json")
        else:
            path = "config.json"
        with open(path, "r") as f:
            config = json.load(f)
        return config

    def load_trading_competition_config(self):
        if self.env == "local":
            path = os.path.join(os.path.dirname(__file__), "../../config/trading_competition_config.json")
        else:
            path = "trading_competition_config.json"
        config = {}
        if os.path.isfile(path):
            with open(path, "r") as f:
                config = json.load(f)
        return config

    def load_master_transaction_config(self, custom_path=None):
        """
        Load master transaction specific configuration.
        
        Args:
            custom_path: Optional custom path to config file (for EMR compatibility)
            
        Returns:
            Dict containing configuration, empty dict if not found
        """
        # Priority order:
        # 1. Custom path if provided
        # 2. EMR path: /home/<USER>/master_transaction_config.json
        # 3. Working directory: master_transaction_config.json
        # 4. Local dev path: ../../config/master_transaction_config.json
        
        paths_to_try = []
        
        if custom_path:
            paths_to_try.append(custom_path)
        
        if self.env == "local":
            paths_to_try.append(os.path.join(os.path.dirname(__file__), "../../config/master_transaction_config.json"))
        else:
            # For EMR/AWS environments, try multiple locations
            paths_to_try.extend([
                "/home/<USER>/master_transaction_config.json",  # EMR standard location
                "master_transaction_config.json",  # Working directory
            ])
        
        config = {}
        for path in paths_to_try:
            if os.path.isfile(path):
                try:
                    with open(path, "r") as f:
                        config = json.load(f)
                    print(f"✅ Master transaction config loaded from {path}")
                    return config
                except json.JSONDecodeError as e:
                    print(f"⚠️  Invalid JSON in {path}: {e}")
                    continue
        
        print(f"⚠️  Master transaction config not found, using defaults. Tried paths: {paths_to_try}")
        return config

    def load_config(self):
        secret_config = self.load_secrets()
        job_config = self.load_job_config()
        tc_config = self.load_trading_competition_config()
        mt_config = self.load_master_transaction_config()
        config = {**job_config, **secret_config, **tc_config, **mt_config}
        if (config.get("bucket") is not None) and (self.env != "local"):
            config["bucket_path"] = "s3a://{}".format(secret_config.get("bucket"))
        else:
            config["bucket_path"] = secret_config.get("bucket")
        config["env"] = self.env
        return config
