from src.utils.custom_logger import get_logger
import redis
import time
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type, before_sleep
import json

class RedisUtils:
    def __init__(self, config, max_tries=None, retry_delay=None, ttl=None):
        self.logger = get_logger()
        self.config = config
        self.max_retries = max_tries
        self.retry_delay = retry_delay
        self.ttl = ttl
        self.redis_client = redis.Redis(host=self.config["data_store"]["redis"]["host"], port=self.config["data_store"]["redis"]["port"],
                                   username=self.config["data_store"]["redis"]["user"], password=self.config["data_store"]["redis"]["password"])

    def get_redis_client(self):
        return self.redis_client

    def _get_retry_decorator(self):
        """Create a retry decorator with instance-specific settings"""
        def log_retry_attempt(retry_state):
            """Log retry attempts"""
            self.logger.warning(f"Redis operation failed, retrying... Attempt {retry_state.attempt_number}/{self.max_retries}. "
                              f"Exception: {retry_state.outcome.exception()}")

        return retry(
            stop=stop_after_attempt(self.max_retries),
            wait=wait_fixed(self.retry_delay),
            retry=retry_if_exception_type((redis.ConnectionError, TimeoutError, redis.exceptions.RedisError)),
            before_sleep=log_retry_attempt
        )

    def delete_single_redis_key(self, key):
        """Delete a single Redis key with retry logic"""
        @self._get_retry_decorator()
        def _delete_key():
            self.logger.info(f"Deleting key: {key}")
            if self.redis_client.exists(key):
                self.redis_client.delete(key)
                self.logger.info(f"Deleted specific key: {key}")
            else:
                self.logger.info(f"No key found: {key}")

        try:
            return _delete_key()
        except Exception as e:
            self.logger.error(f"Failed to delete key '{key}': {e}")
            raise

    def _delete_keys_by_pattern(self, pattern):
        """Delete Redis keys matching a pattern using SCAN"""

        @self._get_retry_decorator()
        def _scan_and_delete():
            self.logger.info(f"Scanning for keys matching pattern: {pattern}")

            try:
                matching_keys = list(self.redis_client.scan_iter(match=pattern))
                self.logger.info(f"Found {len(matching_keys)} keys matching pattern: {pattern}")
                if matching_keys:
                    deleted_count = self.redis_client.delete(*matching_keys)
                    self.logger.info(f"Deleted {deleted_count} keys matching pattern '{pattern}'")
                else:
                    self.logger.info(f"No keys found matching pattern '{pattern}'")

            except Exception as e:
                self.logger.error(f"Error during pattern-based deletion for '{pattern}': {e}")
                raise

        try:
            return _scan_and_delete()
        except Exception as e:
            self.logger.error(f"Failed to delete keys matching pattern '{pattern}': {e}")
            raise

    def delete_redis_keys(self, keys_to_delete):
        self.logger.info("Delete Started Now")
        for key in keys_to_delete:
            if '*' in key:
                self._delete_keys_by_pattern(key)
            else:
                self.delete_single_redis_key(key)

    def update_single_redis_key(self, key, value):
        """Update a single Redis key with retry logic"""
        @self._get_retry_decorator()
        def _update_key():
            existing_value = self.redis_client.get(key)
            if existing_value is not None:
                self.logger.info(f"Existing value for key '{key}': {existing_value.decode('utf-8')}")
            else:
                self.logger.info(f"No existing value for key '{key}'.")

            self.redis_client.set(key, value, ex=self.ttl)
            self.logger.info(f"Key '{key}' set successfully with value {value} and TTL {self.ttl} seconds.")

        try:
            return _update_key()
        except Exception as e:
            self.logger.error(f"Failed to update key '{key}': {e}")
            raise

    def update_redis_key(self, data):
        for key, value in data.items():
            self.update_single_redis_key(key, value)
