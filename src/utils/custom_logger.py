# custom_logger.py
import json
import json_logging
import logging as root_logging
import sys
import os
from datetime import datetime
from typing import Optional
from logging import Logger

class CustomJSONLog(json_logging.JSONLogFormatter):
    def __init__(self, *args, **kwargs):
        self.env = kwargs.pop('env', 'dev')
        self.tag = kwargs.pop('tag', None)
        super(CustomJSONLog, self).__init__(*args, **kwargs)

    def format(self, record):
        utcnow = datetime.utcnow()
        log_data = {
            "timestamp": json_logging.util.iso_time_format(utcnow),
            "msg": record.getMessage(),
            "type": "log",
            "logger_name": record.name,
            "thread_name": record.threadName,
            "level": record.levelname,
            "module": record.module,
            "line_no": record.lineno,
            "env": self.env,
            "project": "PluangSparkBatchProcessingJobs"
        }
        
        # Add tag if provided
        if self.tag:
            log_data["tag"] = self.tag
            
        return json.dumps(log_data)


_logger: Optional[Logger] = None


def init_logger(env: str, tag: str = None):
    global _logger
    if _logger is None:
        json_logging.init_non_web()
        formatter = CustomJSONLog(env=env, tag=tag)
        _logger = root_logging.getLogger("AppLogger")
        _logger.setLevel(root_logging.DEBUG)
        handler = root_logging.StreamHandler(sys.stdout)
        handler.setFormatter(formatter)
        _logger.addHandler(handler)


def get_logger(env: str=None, tag: str = None) -> Logger:
    if _logger is None:
        raise RuntimeError("Logger not initialized. Call init_logger(env) first.")
    
    # If a tag is provided, create a new logger instance with the tag
    if tag:
        json_logging.init_non_web()
        formatter = CustomJSONLog(env=env, tag=tag)
        tagged_logger = root_logging.getLogger(f"AppLogger_{tag}")
        tagged_logger.setLevel(root_logging.DEBUG)
        handler = root_logging.StreamHandler(sys.stdout)
        handler.setFormatter(formatter)
        tagged_logger.addHandler(handler)
        return tagged_logger
    
    return _logger