from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.functions import to_date, date_sub
from pyspark.sql.functions import col, count, sum, aggregate, array, lit, row_number, rank, dense_rank, when, from_json, get_json_object, floor, date_format, from_utc_timestamp, round, collect_set, broadcast, ceil, first, last, struct, concat
from pyspark.sql import DataFrame
from pyspark.sql.window import Window
from pyspark.sql.types import StructField, BooleanType, StringType, StructType, DoubleType, LongType, TimestampType, DateType, FloatType, IntegerType, DecimalType, BinaryType, ArrayType, MapType
from pyspark.sql.utils import AnalysisException
from py4j.java_gateway import java_import
from py4j.protocol import Py4JJavaError

from src.utils.operations import Operations
from src.utils.constants import Constants
from src.utils.custom_logger import get_logger
from src.utils.s3_paths import S3Paths
from src.utils.io_utils import IOUtils
from src.utils.date_utils import DateUtils
from src.utils.price_utils import PriceUtils
from src.utils.asset_utils import AssetUtils
from src.reference_data.user_properties import UserProperties
from src.schema.schema_dict import schema_dict


class SparkUtils:
    def __init__(self, app_nane):
        self.app_name = app_nane
        self.logger = get_logger()

    def create_spark_session(self):
        spark = SparkSession.builder \
            .appName(self.app_name) \
            .getOrCreate()
        self.logger.info("Spark session created")
        return spark

    def stop_spark(self, spark: SparkSession):
        try:
            spark.stop()
            self.logger.info("Spark session stopped successfully")
        except Exception as e:
            self.logger.warning(f"Failed to stop Spark session gracefully: {str(e)}")



