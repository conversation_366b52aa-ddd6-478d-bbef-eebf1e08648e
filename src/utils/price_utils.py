from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.utils.python_utils import PythonUtils
from datetime import datetime, timedelta
from redis import Redis
import json


class PriceUtils:
    def __init__(self, spark: SparkSession, config):
        self.spark = spark
        self.config = config
        self.logger = get_logger()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.bucket_path = self.config.get("bucket_path")

    def get_current_forex_price(self, partner_id, dt, forex_id=None):
        try:
            s3_path = "{}/{}/dt={}/".format(self.bucket_path, S3Paths.forex_price, dt)
            data = self.io_utils.read_json_data(s3_path)
            forex_partner_prices = data.select(
                "forex_id", "partner_id", "buy_back_price", "sell_price", "mid_price", "created", "updated"
            )
            forex_partner_prices = forex_partner_prices.where((col("partner_id") == partner_id))
            w2 = Window.partitionBy("forex_id").orderBy(col("updated").desc())
            current_forex_prices = forex_partner_prices.withColumn("row", row_number().over(w2)).filter(
                col("row") == 1).drop("row")
            if forex_id:
                current_forex_prices = current_forex_prices.where(col("partner_forex_id") == forex_id)

            self.logger.info("Successfully loaded forex partner prices")
            return current_forex_prices
        except Exception as e:
            self.logger.error("An error has occurred while loading forex partner_prices: {}".format(repr(e)))
            self.logger.exception(e)
            raise e

