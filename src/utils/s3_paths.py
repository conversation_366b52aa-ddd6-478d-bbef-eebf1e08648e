class S3Paths:
    # raw_data
    topups_raw_data = "raw_data/topups"
    cashouts_raw_data = "raw_data/cashouts"
    crypto_currency_transactions_raw_data = "raw_data/crypto_currency_transactions"
    crypto_currency_returns_raw_data = "raw_data/crypto_currency_returns"
    crypto_currency_pocket_transactions_raw_data = "raw_data/crypto_currency_pocket_transactions"
    crypto_currency_wallet_transfers_raw_data = "raw_data/crypto_currency_wallet_transfers"
    crypto_future_transactions_raw_data = "raw_data/crypto_futures_transactions"
    global_stock_transactions_raw_data = "raw_data/global_stock_transactions"
    options_contract_transactions_raw_data = "raw_data/options_contract_transactions"
    gold_transactions_raw_data = "raw_data/gold_transactions"
    global_stock_accounts_raw_data = "raw_data/global_stock_accounts"
    global_stock_pocket_accounts_raw_data = "raw_data/global_stock_pocket_accounts"
    global_stock_returns_raw_data = "raw_data/global_stock_returns"
    global_stock_pocket_returns_raw_data = "raw_data/global_stock_pocket_returns"
    forex_returns_raw_data = "raw_data/forex_returns"
    forex_accounts_raw_data = "raw_data/forex_accounts"
    leverage_wallet_accounts_raw_data = "raw_data/leverage_wallet_accounts"
    options_contracts_raw_data = "raw_data/options_contracts"
    options_contract_accounts_raw_data = "raw_data/options_contract_accounts"
    indo_stock_transactions_v2_raw_data = "raw_data/indo_stock_transactions_v2"
    gold_gift_transactions_raw_data = "raw_data/gold_gift_transactions"
    gold_withdrawals_raw_data = "raw_data/gold_withdrawals"
    gold_loans_raw_data = "raw_data/gold_loans"
    installment_payment_raw_data = "raw_data/installment_payments"
    fund_transactions_raw_data = "raw_data/fund_transactions"
    forex_top_ups_raw_data = "raw_data/forex_top_ups"
    forex_cash_outs_raw_data = "raw_data/forex_cash_outs"
    forex_transactions_raw_data = "raw_data/forex_transactions"
    ph_global_stock_accounts_raw_data = "raw_data/ph_global_stock_accounts"
    ph_global_stock_returns_raw_data = "raw_data/ph_global_stock_returns"
    campaigns_raw_data = "raw_data/campaigns"

    # snapshots
    gold_accounts = "gold_accounts/t_2_files"
    ph_global_stock_accounts = "ph_global_stock_accounts"
    ph_global_stock_returns = "ph_global_stock_returns"
    user_tag_mappings = "user_tag_mappings/t_2_files"
    accounts = "accounts/snapshots"
    crypto_currency_wallet_transfers_de_dupe = "crypto_currency_wallet_transfers"
    topups = "snapshots/topups"
    cashouts = "snapshots/cashouts"
    cashouts_agg = "cashouts"
    topups_agg = "cashin"
    forex_top_ups_snapshot = "forex_top_ups"
    forex_cash_outs_snapshot = "forex_cash_outs"
    crypto_currency_transactions = "snapshots/crypto_currency_transactions"
    crypto_currency_pocket_transactions = "snapshots/crypto_currency_pocket_transactions"
    crypto_currency_wallet_transfers = "snapshots/crypto_currency_wallet_transfers"
    crypto_future_transactions = "snapshots/crypto_future_transactions"
    crypto_future_funding_transactions = "snapshots/crypto_future_funding_transactions"
    global_stock_transactions = "snapshots/global_stock_transactions"
    options_contract_transactions = "snapshots/options_contract_transactions"
    gold_transactions = "snapshots/gold_transactions"
    global_stock_dividend_transactions = "snapshots/global_stock_dividend_transactions"
    global_stock_dividends_v2 = "snapshots/global_stock_dividends_v2"
    gold_gift_transactions = "snapshots/gold_gift_transactions"
    gold_withdrawals = "snapshots/gold_withdrawals"
    gold_loans = "snapshots/gold_loans"
    installment_payment = "snapshots/installment_payments"
    fund_transactions = "snapshots/fund_transactions"
    forex_top_ups = "snapshots/forex_top_ups"
    forex_cash_outs = "snapshots/forex_cash_outs"
    forex_transactions = "snapshots/forex_transactions"
    forex_yield_transactions = "snapshots/forex_yield_transactions"
    indo_stock_dividend = "indo_stock_balance_v2/snapshots"
    indo_stock_accounts_v2_t_2_files = "indo_stock_accounts_v2/t_2_files"
    indo_stock_returns_v2_t_2_files = "indo_stock_returns_v2/t_2_files"
    options_contracts = "global_stock_options_contracts/t_2_files"
    options_contract_accounts = "global_stock_options_accounts/t_2_files"
    global_stock_accounts = "global_stock_accounts/t_2_files"
    global_stock_pocket_accounts = "global_stock_pocket_accounts/t_2_files"
    global_stock_returns = "global_stock_returns/t_2_files"
    global_stock_pocket_returns = "global_stock_pocket_returns/t_2_files"
    leverage_wallet_accounts = "leverage_wallet_accounts/t_2_files"
    staking_accounts_snapshot = "snapshots/crypto_currency_staking_accounts"
    staking_requests_snapshot = "snapshots/crypto_currency_staking_transactions"
    staking_assets_snapshot = "snapshots/crypto_currency_staking_assets"
    staking_accrued_rewards_snapshot = "snapshots/crypto_currency_staking_accrued_rewards"
    staking_rewards_accrual_history = "crypto_staking/rewards_accrual_history"
    staking_rewards_disbursal_history = "crypto_staking/rewards_disbursal_history"

    indo_stock_transactions = "snapshots/indo_stock_transactions_v2"
    indo_stock_wallet_activity = "snapshots/indo_stock_wallet_activity_v2"
    indo_stock_price_v2 = "indo_stock_prices_v2/indo_stock_one_day_ohlc_price_stats"
    indo_stock_price_v2_one_hour = "indo_stock_prices_v2/indo_stock_one_hour_ohlc_price_stats"
    indo_stock_corporate_action_v2 = "indo_stock_prices_v2/corporate_action"
    indo_stock_returns_v2 = "indo_stock_returns_v2"
    indo_stock_returns_v2_snapshots = "indo_stock_returns_v2/snapshots"
    indo_stock_price_v2_watchlist_52_week_low_price = "predefined_watchlist/indo_stock_price_v2_watchlist/52_week_low_price"
    indo_stock_price_v2_watchlist_52_week_high_price = "predefined_watchlist/indo_stock_price_v2_watchlist/52_week_high_price"
    indo_stock_price_v2_watchlist_daily_volume = "predefined_watchlist/indo_stock_price_v2_watchlist/volume"
    indo_stock_price_v2_watchlist_max_dividend = "predefined_watchlist/indo_stock_price_v2_watchlist/max_dividend"
    crypto_currency_snapshot = "crypto_currency_returns/snapshots"
    crypto_currency_returns_t_2_files = "crypto_currency_returns/t_2_files"
    crypto_currency_pocket_snapshot = "crypto_currency_pocket_returns/snapshot"
    forex_snapshot = "forex_returns/snapshots"
    forex_accounts_snapshot = "forex_accounts/t_2_files"
    fund_snapshot = "fund_returns/snapshots"
    fund_returns = "fund_returns/t_2_files"
    global_stock_snapshot = "global_stock_returns/snapshots"
    global_stock_pocket_snapshot = "global_stock_pocket_returns/snapshots"
    indo_stock_wallet_v2 = "indo_stock_wallet_v2/t_2_files"
    gold_snapshot = "gold_returns/snapshots"
    gold_returns = "gold_returns/t_2_files"
    bappebti_wallet_snapshot = "bappebti_wallets/snapshots"
    gold_gift_and_withdrawal_snapshot = "gold_gift_and_withdrawal_snapshot"
    leverage_wallet_accounts_snapshot = "leverage_wallet_accounts/t_2_files"
    stock_index_snapshot = "stock_index_decommission/snapshots"
    options_snapshot = "global_stock_options_accounts/snapshots"
    crypto_futures_snapshot = "crypto_future_positions/snapshots"
    crypto_futures_positions_t_2_files = "crypto_future_positions/t_2_files"
    crypto_margin_wallets = "crypto_margin_wallets/t_2_files"
    global_stock_intraday_snapshot = "global_stock_intraday_accounts/snapshots"
    indo_stock_dividend_snapshot = "snapshots/indo_stock_dividend_transactions_v2"
    global_stock_intraday_t_2_files = "global_stock_intraday_accounts/t_2_files"
    crypto_currency_withdrawal_snapshot = "crypto_currency_withdrawal_snapshot"
    campaigns = "campaigns/all_campaings"
    forex_unhedged_transactions = "snapshots/forex_unhedged_transactions"
    data_validation_forex_unhedged_transaction = "data_validation/forex_transaction"
    data_validation_idss_user_position_validation = "data_validation/idss/user_position_validation"
    data_validation_idss_user_buying_power_validation = "data_validation/idss/user_buying_power_validation"
    data_validation_idss_user_trades_validation = "data_validation/idss/user_trades_validation"
    indo_stock_trades_v2_snapshots = "snapshots/indo_stock_trades_v2"
    s21_rt_client_cash = "S21-RT/client_cash"
    s21_rt_client_stock = "S21-RT/client_stock"
    s21_rt_rt_trade = "S21-RT/rt_trade"
    s21_rt_view_order = "S21-RT/view_order"



    # prices
    global_stock_price = "global_stocks_prices/mongo_hourly_ohlc_price"
    options_contract_price = "global_stock_options_price/snapshots"
    forex_price = "forex_prices/forex_partner_price"

    # calculations
    gold_maintenance_fees_s3_folder = "gold_maintenance/gold_maintenance_fees"
    gold_maintenance_fees_intermediate_folder = "gold_maintenance/gold_maintenance_fees_intermediate"
    ph_global_stock_dividend_folder = "ph_data/global_stocks_dividend/snapshots"
    portfolio_snapshot_folder = "portfolio/snapshots"
    indo_stock_daily_statement_folder = "user_daily_statement/indo_stock_daily_statement"
    indo_stock_daily_statement_whitelisted_user_folder = "user_daily_statement/indo_stock_whitelisted_user"
    indo_stock_daily_statement_blacklisted_user_folder = "user_daily_statement/indo_stock_blacklisted_user"
    profit_and_loss = "profit-and-loss"
    monthly_statement_output = "user_daily_statement/monthly_statement"
    monthly_statement_whitelisted_users = "user_daily_statement/monthly_whitelisted_users"
    monthly_statement_blacklisted_users = "user_daily_statement/monthly_blacklisted_users"
    gold_tax_report = "tax_report/gold_tax_report"
    crypto_currency_tax_report = "tax_report/crypto_tax_report"
    fund_tax_report = "tax_report/fund_tax_report"
    us_stocks_tax_report = "tax_report/us_stocks_tax_report"
    indo_stock_v2_tax_report = "tax_report/indo_stock_v2_tax_report"
