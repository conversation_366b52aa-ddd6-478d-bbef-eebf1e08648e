# Pluang Multi-Asset Transaction Processing Rules

## Project Context and Architecture
SYSTEM_CONTEXT: |
  You are a senior developer working on a PySpark-based multi-asset transaction processing system for Pluang Trading Platform.
  
  CORE BUSINESS CONTEXT:
  - Process daily transactions from 7 asset types: crypto, crypto_futures, global_stock_transactions, indo_stock_transaction, mutual_funds, options_contract_transaction, and prices
  - Read raw transaction data from AWS S3 with date partitioning
  - Apply asset-specific business transformations to produce unified schema
  - Write transformed data back to S3 with asset_name/date partition structure
  
  Required file reads on startup:
  - ai-docs/architecture_template.md: System architecture and component relationships
  - ai-docs/task_template.md: Current development tasks and requirements
  - config/config.json: Environment settings and asset configurations
  - src/schema/: Asset-specific schemas and unified schema definitions

  Before making any changes:
  1. Understand the multi-asset transaction processing architecture
  2. Check current task context from ai-docs/task_template.md
  3. Review asset-specific schemas in src/schema/
  4. Follow technical specifications from config/config.json
   
# File Management Rules
ON_FILE_CHANGE: |
  Required actions after any code changes:
  1. READ ai-docs/architecture_template.md to verify architectural compliance
  2. UPDATE ai-docs/task_template.md with:
     - Current progress on asset-specific transformations
     - Any new issues encountered during processing
     - Completed asset modules and unified schema updates
  3. VALIDATE changes against config/config.json specifications
  4. VERIFY asset-specific schema compliance in src/schema/

## Code Style & Standards
- Tech Stack: Python 3.9+ with PySpark 3.4+
- Follow PEP 8 naming conventions (snake_case for functions/variables, PascalCase for classes)
- Keep functions focused and single-purpose
- Use descriptive variable names that reflect asset-specific business logic
- Asset-specific functions should be prefixed with asset name (e.g., `crypto_transform_`, `gold_calculate_`)

## Multi-Asset Data Engineering Best Practices
- Use Spark DataFrame operations over RDD when possible
- Implement proper error handling and logging for data pipeline failures
- Use configuration files for environment-specific settings and asset configurations
- Follow the existing project structure in src/jobs/ for new batch jobs
- Use the existing utility modules in src/utils/ for common operations
- **Asset-Specific Processing**: Each asset type must have its own transformation module
- **Unified Schema**: All asset transformations must produce data conforming to the unified schema
- **S3 Integration**: Use existing S3 utilities for reading/writing partitioned data
- **Date Partitioning**: Always process data with proper date partitioning (YYYY/MM/DD format)

## Testing Requirements
- Write unit tests for all new functions and classes
- Test data validation logic thoroughly for each asset type
- Test unified schema compliance across all asset transformations
- Use pytest for testing framework
- Run tests with: `source .venv/bin/activate && python -m pytest tests/ -v`
- Ensure all tests pass before considering work complete
- **Asset-Specific Testing**: Test each asset transformation module independently
- **Integration Testing**: Test unified schema output from all asset types

## File Organization
- Place new batch jobs in appropriate subdirectories under src/jobs/
- **Asset-Specific Jobs**: Create separate modules for each asset type:
  - src/jobs/calculations/crypto_transactions.py
  - src/jobs/calculations/crypto_futures_transactions.py
  - src/jobs/calculations/global_stock_transactions.py
  - src/jobs/calculations/indo_stock_transactions.py
  - src/jobs/calculations/mutual_fund_transactions.py
  - src/jobs/calculations/options_contract_transactions.py
  - src/jobs/calculations/price_transformations.py
- Use existing schema definitions in src/schema/ for data validation
- **Unified Schema**: Define unified output schema in src/schema/unified_transaction_schema.py
- Follow the enum pattern in src/enums/ for constants
- Add new utility functions to appropriate modules in src/utils/

## Multi-Asset Data Processing Guidelines
- Always handle null values explicitly in data transformations
- Use appropriate data types (avoid string for numeric data)
- Implement data quality checks and validation rules for each asset type
- Log data processing metrics and job execution times per asset
- Use the existing job_metrics utility for monitoring
- **Asset-Specific Transformations**: Apply business logic specific to each asset type
- **Unified Schema Compliance**: Ensure all transformations produce data matching unified schema
- **S3 Path Management**: Use consistent S3 path patterns: s3://bucket/asset_name/YYYY/MM/DD/
- **Date Handling**: Always use proper date partitioning and timezone handling
- **Data Validation**: Validate input data against asset-specific schemas before processing

## S3 and AWS Configuration Management
- Use config/config.json for environment settings and S3 bucket configurations
- **S3 Bucket Configuration**: Define source and destination S3 buckets per environment
- **Asset-Specific Paths**: Configure S3 paths for each asset type in config
- Never hardcode sensitive information (API keys, passwords, S3 credentials)
- Use environment variables for deployment-specific configurations
- Follow the existing config_loader pattern
- **AWS Credentials**: Use IAM roles or environment variables for S3 access

## Error Handling and Monitoring
- Log errors with appropriate context and stack traces per asset type
- Implement retry logic for S3 operations and API calls
- Use the existing custom_logger utility
- **Asset-Specific Error Handling**: Handle asset-specific data quality issues
- **S3 Error Handling**: Implement proper error handling for S3 read/write operations
- **Data Quality Alerts**: Set up monitoring for data quality issues per asset type

## Performance Considerations
- Cache DataFrames when reused multiple times
- **Asset-Specific Caching**: Cache reference data and price data for reuse
- **S3 Optimization**: Use appropriate S3 partitioning and file formats (Parquet)
- **Memory Management**: Optimize memory usage for large transaction datasets
- **Parallel Processing**: Process multiple asset types in parallel when possible

## S3 Output Structure Requirements
- **Output Path Pattern**: s3://bucket/asset_name/YYYY/MM/DD/
- **File Format**: Use Parquet format for optimal performance
- **Partitioning**: Partition by date for efficient querying
- **Schema Evolution**: Handle schema changes gracefully across asset types
- **Data Validation**: Validate output data against unified schema before writing

## Git Workflow
- Never run git commands automatically
- Focus on code quality and functionality
- Ensure all tests pass before completion
- **Asset-Specific Commits**: Group commits by asset type for better tracking

## Cross-repo ownership
IGNORE_IN_THIS_REPO: |
  # The Airflow DAG is owned by a different repository; do not commit or surface changes here
  - src/jobs/master_transactions/master_transactions_dag.py