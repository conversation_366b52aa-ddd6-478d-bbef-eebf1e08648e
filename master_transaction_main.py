# Master Transaction Main Entry Point
import sys
import os
import importlib
import json
from datetime import date, timedelta, datetime
import argparse
from src.utils.job_metrics import JobMetrics
from src.utils.date_utils import DateUtils
from src.utils.config_loader import Config<PERSON>oa<PERSON>


def get_job_class(full_class_path):
    """Dynamically import and return job class"""
    *module_parts, class_name = full_class_path.split(".")
    module_name = ".".join(module_parts)
    module = importlib.import_module(module_name)
    return getattr(module, class_name)


def main():
    """Main entry point for master transaction jobs"""
    print("🚀 Starting Master Transaction Job")
    
    # Load configuration using ConfigLoader
    # Detect environment: use 'local' if running locally, 'aws' otherwise
    env = "aws" if os.path.exists("/home/<USER>") else "local"
    config_loader = ConfigLoader(env)
    config = config_loader.load_config()
    job_name = None
    start_time = datetime.now()

    # Parse command line arguments
    parser = argparse.ArgumentParser()
    parser.add_argument("--offset", help="offset for the job")
    parser.add_argument("--utc_cutoff_ts", help="UTC cutoff timestamp")
    parser.add_argument("--execution_time", help="execution time of the job")
    parser.add_argument("--job_name", help="fully qualified job class")
    parser.add_argument("--t_1", help="target date (YYYY-MM-DD)")
    parser.add_argument("--h_1", help="target hour (HH)")
    args, unknown = parser.parse_known_args()
    
    # Process known arguments
    if args.offset:
        config['offset'] = int(args.offset)

    if args.utc_cutoff_ts:
        config['utc_cutoff_ts'] = DateUtils.get_utc_timestamp_from_string(args.utc_cutoff_ts) if args.utc_cutoff_ts else None

    if args.execution_time:
        config['execution_time'] = args.execution_time
        
    if args.t_1:
        config['t_1'] = args.t_1
        
    if args.h_1:
        config['h_1'] = args.h_1

    if args.job_name:
        job_name = args.job_name

    # Process additional arguments
    additional_args = {}
    for i in range(0, len(unknown), 2):
        key = unknown[i].lstrip("--")
        value = unknown[i + 1]
        additional_args[key] = value

    # Set date/time parameters if not provided
    if not config.get('t_1') or not config.get('h_1'):
        cutoff_ts, t_1, t_2 = DateUtils.get_cutoff_time_and_jkt_date(config)
        config["cutoff_ts"] = cutoff_ts
        if not config.get('t_1'):
            config["t_1"] = t_1
        if not config.get('h_1'):
            config["h_1"] = config["t_1"]  # Default h_1 to t_1
        config["t_2"] = t_2

    print(f"📅 Processing date: {config.get('t_1')}")
    print(f"⏰ Processing hour: {config.get('h_1')}")
    print(f"🎯 Job class: {job_name}")

    # Load and execute job
    try:
        # For master transactions, don't add src.jobs prefix as it's already included
        if job_name and not job_name.startswith("src.jobs"):
            full_job_name = "src.jobs.{}".format(job_name)
        else:
            full_job_name = job_name
            
        job_class = get_job_class(full_job_name)
        job_instance = job_class(config=config, **additional_args)
        
        print("✅ Job instance created successfully")
        job_instance.run()
        print("🎉 Job completed successfully")
        
    except Exception as e:
        print(f"❌ Job failed: {str(e)}")
        import traceback
        traceback.print_exc()
        raise

    # Record metrics
    end_time = datetime.now()
    job_metrics = JobMetrics()
    job_metrics.job_time_metrics(start_time, end_time, job_name)


if __name__ == "__main__":
    main()
