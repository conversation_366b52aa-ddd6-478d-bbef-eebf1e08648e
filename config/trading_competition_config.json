{"aum_tier_upgrade": {"aum_file_path": "portfolio/snapshots", "tier_event_snapshot_path": "trading_competition_q3_2025/snapshots/trading_competition_user_events", "tier_snapshot_path": "trading_competition_q3_2025/tier_upgrade", "tier_upgrade_range": [{"tier": "CHALLENGER", "min_aum": 0, "max_aum": 100000000}, {"tier": "LEGEND", "min_aum": 100000000, "max_aum": null}]}, "asset_snapshot": {"batch_1": [{"trading_competition_user_events": true}, {"crypto_currency_pocket_transactions": true}, {"crypto_currency_transactions": true}, {"crypto_currency_wallet_transfers": true}, {"global_stock_transactions": true}, {"options_contract_transactions": true}, {"leverage_wallet_transactions": true}, {"gold_gift_transactions": true}, {"gold_loans": true}, {"gold_transactions": true}, {"installment_payments": true}, {"gold_withdrawals": true}], "batch_2": [{"fund_transactions": true}, {"forex_transactions": true}, {"forex_yield_transactions": true}, {"forex_top_ups": true}, {"forex_cash_outs": true}, {"options_contracts": true}, {"crypto_future_funding_transactions": true}, {"crypto_future_instruments": true}, {"crypto_future_trades": true}, {"crypto_future_transactions": true}]}, "snapshot_path": "trading_competition_q3_2025/snapshots", "raw_data_path": "raw_data", "batches": {"batch_file_path": "trading_competition_q3_2025/transaction_batches", "flash_games_batch_file_path": "trading_competition_q3_2025/flash_game_transaction_batches", "all_transaction_file_path": "trading_competition_q3_2025/all_transactions"}, "crypto_currency_pocket_transactions_columns": ["id", "crypto_currency_id", "user_id", "account_id", "client_id", "partner_id", "user_pocket_id", "quantity", "unit_price", "transaction_fee", "taxation_fee", "total_price", "estimated_quantity", "executed_quantity", "estimated_unit_price", "executed_unit_price", "executed_total_price", "ref_id", "user_pocket_txn_ref_id", "order_type", "status", "transaction_type", "source", "is_tax_reported", "recurring_order_type", "recurring_transaction_id", "hedging_status", "created", "updated", "transaction_time"], "crypto_currency_transactions_columns": ["id", "crypto_currency_id", "user_id", "account_id", "client_id", "partner_id", "quantity", "unit_price", "fee", "total_price", "ref_id", "status", "transaction_type", "created", "updated", "exchange_total_price", "source", "hedging_status", "unhedged_quantity", "hedging_info", "yield_quantity_used", "premium_fee", "transaction_fee", "estimated_quantity", "estimated_unit_price", "executed_quantity", "executed_unit_price", "executed_total_price", "vendor_transaction_id", "order_type", "reward_quantity_used", "taxation_fee", "is_tax_reported", "lock_type", "recurring_order_type", "recurring_transaction_id", "transaction_time", "system_status", "parent_transaction_id"], "crypto_currency_wallet_transfers_columns": ["id", "user_id", "account_id", "client_id", "partner_id", "crypto_currency_id", "custodian_asset_id", "network", "source_address", "destination_address", "quantity", "total_quantity", "unit_price", "transfer_fee", "network_fee", "block_explorer_url", "status", "transaction_type", "external_response", "source", "vendor_txn_id", "external_status", "created_at", "updated_at", "risk_score", "transaction_sub_status", "tag", "taxation_fee", "taxation_fee_percentage", "yield_quantity_used", "reward_quantity_used", "network_fee_unit_price", "transaction_time"], "global_stock_transactions_columns": ["id", "global_stock_id", "user_id", "account_id", "client_id", "partner_id", "quantity", "unit_price", "transaction_fee", "premium_fee", "total_price", "executed_total_price", "estimated_total_price", "estimated_quantity", "executed_quantity", "estimated_unit_price", "executed_unit_price", "ref_id", "order_type", "status", "transaction_type", "source", "hedging_type", "expiry_date_time", "created", "updated", "usd_to_idr", "forex_price_id", "cancelled_type", "effective_spread", "raw_spread", "exchange_mid_price", "lock_type", "order_sub_type", "vendor_transaction_id", "wallet_type", "realised_gain", "trading_margin_used", "recurring_order_type", "recurring_transaction_id", "client_order_id", "order_details", "user_pocket_txn_ref_id", "user_pocket_id", "reward_quantity_used", "trading_hours", "transaction_fee_config_id", "is_jfx_synced", "stock_type", "transaction_time", "sub_state", "waived_off_fee", "stop_out_detail_id"], "options_contract_transactions_columns": ["id", "options_contract_id", "global_stock_id", "user_id", "account_id", "client_id", "partner_id", "quantity", "unit_price", "total_price", "estimated_quantity", "estimated_unit_price", "estimated_total_price", "executed_quantity", "executed_unit_price", "executed_total_price", "order_type", "transaction_type", "status", "time_in_force", "expiry_date_time", "ref_id", "vendor_transaction_id", "client_order_id", "order_details", "sub_state", "transaction_fee_config_id", "transaction_fee", "cancelled_type", "lock_type", "usd_to_idr", "transaction_time", "is_jfx_synced", "created", "updated", "waived_off_fee", "stop_price"], "leverage_wallet_transactions_columns": ["id", "user_id", "account_id", "client_id", "partner_id", "quantity", "ref_id", "status", "wallet_transaction_type", "total_price_in_idr", "unit_price_in_idr", "currency", "created", "updated", "transaction_time"], "gold_gift_transactions_columns": ["id", "account_id", "target_account_id", "user_id", "target_user_id", "transaction_type", "quantity", "unit_price", "fees", "final_amount", "gift_id", "status", "actor_role", "receiver_phone_no", "client_id", "created", "updated", "jfx_synced", "transaction_time", "creator_admin_id", "approver_admin_id", "approver_jira_link"], "gold_loans_columns": ["id", "account_id", "partner_reference", "partner_id", "gold_loan_amount", "gold_frozen", "tenure", "admin_fee", "down_payment", "interest_rate", "issued_price", "currency", "monthly_installment", "total_gold_price", "total_installment", "source", "status", "installment_index", "due_date", "cash_back", "fine", "fine_pay_date", "deleted", "created", "updated", "user_id", "client_id", "auto_debit_emi", "gold_brand", "missed_emis", "total_principal", "cancellation_jfx_synced", "cancellation_jfx_sync_date", "transaction_time"], "gold_transactions_columns": ["id", "partner_id", "account_id", "transaction_type", "quantity", "unit_price", "fees", "final_amount", "partner_reference", "commission_percent", "commission_amount", "status", "created", "updated", "client_id", "user_id", "auto_invest", "round_up", "jfx_synced", "success_date", "jfx_sync_date", "transaction_time"], "installment_payments_columns": ["id", "gold_loan_id", "installment_index", "money_paid", "money_fine", "installment", "status", "date_paid_on", "due_date", "payment_channel", "admin_id", "commission_percent", "commission_amount", "paid_principal", "partner_reference", "deleted", "created", "updated", "partner_id", "jfx_synced", "jfx_sync_date", "transaction_time"], "gold_withdrawals_columns": ["id", "account_id", "user_id", "gold_amount", "order_ref", "invoice_link", "receipt_link", "status", "created", "updated", "fee", "net_amount", "old_invoice_number", "old_merchant_id", "shipping_method", "delivery_fee", "printing_fee", "printing_fee_type", "unit_price", "insurance_fee", "final_price", "client_id", "ref_id", "address_proof_link", "delivery_address_type", "buy_price", "sell_price", "jfx_synced", "jfx_sync_date", "transaction_time"], "fund_transactions_columns": ["id", "account_id", "user_id", "fund_id", "partner_id", "client_id", "quantity", "estimated_quantity", "unit_price", "estimated_unit_price", "fee", "admin_fee", "final_price", "vendor_channel", "payment_channel", "user_bank_id", "matched", "user_bank_details", "ref_id", "rejection_reason", "fund_sell_type", "status", "transaction_type", "created", "updated", "disbursement_id", "disbursement_status", "disbursement_details", "rejection_reason_json", "currency", "amount", "usd_to_idr", "transaction_time"], "forex_transactions_columns": ["id", "forex_id", "user_id", "account_id", "client_id", "partner_id", "partner_price_id", "quantity", "unit_price", "fee", "total_price", "ref_id", "status", "transaction_type", "created", "updated", "centralized_wallet_id", "transaction_time", "transaction_fee_config_id"], "forex_yield_transactions_columns": ["id", "account_id", "user_id", "forex_id", "yield_quantity", "yield_percentage", "yield_tax", "yield_qty_received", "average_wallet_balance", "usd_to_idr", "month", "year", "info", "created", "updated", "transaction_time"], "forex_top_ups_columns": ["id", "forex_id", "user_id", "account_id", "client_id", "partner_id", "status", "top_up_amount", "source_bank", "source_bank_account_number", "proof_of_transfer", "top_up_form", "bank_statement_transaction_id", "bank_statement_transaction_proof", "final_amount", "unit_price", "rejection_reason", "internal_rejection_reason", "created", "updated", "transaction_time"], "forex_cash_outs_columns": ["id", "forex_id", "user_id", "account_id", "client_id", "partner_id", "status", "withdrawal_amount", "destination_bank_account", "destination_bank_account_owner", "destination_bank_account_owner_address", "destination_bank", "destination_bank_swift", "destination_bank_address", "destination_bank_city", "destination_bank_country", "proof_of_request", "proof_of_bank_account_ownership", "unit_price", "cs_call_confirmation_link", "bank_transfer_slip", "finance_jira_link", "rejection_reason", "internal_rejection_reason", "created", "updated", "transaction_time"], "options_contracts_columns": ["id", "global_stock_id", "contract_symbol", "contract_type", "expiration_date", "shares_per_contract", "strike_price", "enable_buy", "enable_sell", "is_trading_halted", "trading_halt_reason", "status", "created", "updated"], "trading_competition_user_events_columns": ["accountId", "userId", "name", "email", "tradingCompetitionId", "previousTier", "currentTier", "eligibleUpgradeTier", "eventTime", "userAction"], "crypto_future_transactions_columns": ["id", "created", "updated", "source", "user_id", "account_id", "partner_id", "client_id", "crypto_future_user_id", "crypto_future_instrument_id", "crypto_margin_wallet_id", "transaction_type", "order_type", "ref_id", "exec_inst", "exchange_time_in_force", "exchange_order_id", "client_order_id", "exchange_status", "user_status", "unit_price", "executed_unit_price", "executed_quantity", "quantity", "total_price", "estimate_total_price", "estimate_realised_gain", "realised_gain", "transaction_time", "offsets", "initial_margin_required", "leverage", "crypto_future_position_id"], "crypto_future_trades_columns": ["id", "created", "updated", "source", "user_id", "account_id", "crypto_future_user_id", "crypto_future_instrument_id", "crypto_future_transaction_id", "exchange_trade_id", "transaction_type", "quantity", "remaining_quantity", "price", "traded_at_timestamp", "active", "settle_asset_mid_price", "total_trade_fee"], "crypto_future_instruments_columns": ["id", "created", "updated", "source", "symbol", "future_pair_symbol", "name", "exchange_status", "exchange_settlement_asset", "underlying_asset", "enable_pricing", "enable_buy", "enable_sell", "active"], "crypto_future_funding_transactions_columns": ["id", "created", "updated", "source", "user_id", "account_id", "crypto_future_user_id", "crypto_future_instrument_id", "exchange_trade_id", "quantity", "price", "fee", "funding_rate", "traded_at_timestamp", "transaction_time", "settle_asset_mid_price", "crypto_margin_wallet_id", "crypto_future_position_id"], "prices": {"global_stock": {"price_path": "trading_competition_q3_2025/prices/global_stocks_prices", "collection": "global_stock_hourly_ohlc_price_stats"}, "fund": {"price_path": "trading_competition_q3_2025/prices/fund_prices", "db_table": "fund_partner_prices"}, "forex": {"price_path": "trading_competition_q3_2025/prices/forex_prices", "db_table": "forex_partner_prices"}, "crypto_currency": {"price_path": "trading_competition_q3_2025/prices/crypto_currency_prices", "collection": "pluang_crypto_currency_prices.crypto_currency_hourly_ohlc_price_stats"}, "crypto_currency_futures": {"price_path": "trading_competition_q3_2025/prices/crypto_currency_futures_prices", "collection": "pluang_crypto_futures_prices.crypto_futures_hourly_ohlc_price_stats"}, "gold": {"price_path": "trading_competition_q3_2025/prices/gold_prices", "collection": "pluang_reporting.partner_gold_price_stats_ohlc"}, "indo_stock": {"price_path": "trading_competition_q3_2025/prices/indo_stocks_prices", "collection": "pluang_indo_stock_static_data.indo_stock_one_day_ohlc_price_stats"}, "global_stock_options": {"price_path": "trading_competition_q3_2025/prices/global_stock_options_price", "raw_price_folder": "raw_price", "price_snapshot_folder": "snapshots"}}, "crypto_rebrand_txn_path": "trading_competition_q3_2025/crypto_rebrand_transactions", "start_asset_position_path": "trading_competition_q3_2025/start_asset_position", "global_stock_splits_path": "trading_competition_q3_2025/global_stock_splits", "usdt_coin_id": 10005, "pluang_partner_id": 1000002, "fund": {"partner_id": 1000002}, "buy_types": ["BUY", "LONG_OPEN", "DEPOSIT", "RECEIVE", "AIRDROP", "BUY_MA"], "sell_types": ["SELL", "LONG_CLOSE", "WITHDRAWAL", "SEND", "GOLD_WITHDRAWAL", "AIRDROP_SELL", "SELL_MA"], "initial_balance_files": {"gold": "gold_returns/t_2_files", "accounts": "accounts/snapshots", "global_stocks": "global_stock_returns/t_2_files", "global_stocks_pocket": "global_stock_pocket_returns/t_2_files", "crypto_currency": "crypto_currency_returns/t_2_files", "crypto_currency_pocket": "crypto_currency_pocket_returns/t_2_files", "global_stock_options": "global_stock_options_accounts/t_2_files", "crypto_futures": "crypto_future_positions/t_2_files", "crypto_margin_wallets": "crypto_margin_wallets/t_2_files", "bappebti_wallets": "bappebti_wallets/t_2_files", "forex_accounts": "forex_accounts/t_2_files", "leverage_wallet_accounts": "leverage_wallet_accounts/t_2_files", "global_stock_intraday_accounts": "global_stock_intraday_accounts/t_2_files", "global_stock_options_contracts": "global_stock_options_contracts/t_2_files"}, "split_supported_asset_sub_types": ["global_stock_transactions", "options_contract_transactions"], "niv_path": "pluang_plus_member_validity/snapshot", "gtv_path": "trading_competition_q3_2025/gtv", "pnl_path": "trading_competition_q3_2025/pnl", "flash_games_pnl_path": "trading_competition_q3_2025/flash_games_pnl", "flash_games_assets_path": "trading_competition_q3_2025/flash_games_assets", "tags_path": "trading_competition_q3_2025/tags", "clevertap_user_properties_path": "trading_competition_q3_2025/clevertap_user_properties", "clevertap_content_api_data_path": "trading_competition_q3_2025/clevertap_content_api_data", "clevertap_asset_types": ["crypto_currency", "gold", "global_stocks", "crypto_futures", "global_stock_options"]}