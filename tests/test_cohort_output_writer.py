"""
Unit tests for CohortOutputWriter - Concise version with comprehensive data validation.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pyspark.sql.types import StructType, StructField, StringType, IntegerType
from src.jobs.cohorting.writers.cohort_output_writer import CohortOutputWriter, SchemaFactoryType
from tests.helpers.cohorting_test_helpers import (
    validate_data_integrity, create_test_schema, assert_basic_dataframe_properties
)


class TestCohortOutputWriter:
    """Test cases for CohortOutputWriter."""

    @pytest.fixture
    def mock_config(self):
        """Mock configuration."""
        return {
            "bucket_path": "s3a://test-bucket",
            "cohorting": {
                "top_page_visit_assets_num": 1,
                "individual_cohort_output_path": "cohorting/individual_cohorts/{date}/{cohort_id}/",
                "clevertap_cohort_upload_path": "cohorting/clevertap_upload/",
                "clevertap_cohort_clear_path": "cohorting/clevertap_cohort_clear/latest/",
                "cohort_matrix_output_path": "cohorting/matrix/{date}/{hour}/"
            },
            "sink_configurations": {
                "S3": {
                    "sinks": {
                        "customer_risk_rating": {
                            "s3_path": "risk_rating/{date}/{hour}/{cohort_id}/"
                        },
                        "test_sink": {
                            "s3_path": "test_output/{date}/{cohort_id}/"
                        }
                    }
                }
            }
        }

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_initialization(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test CohortOutputWriter initialization."""
        mock_logger = Mock()
        
        mock_ops_instance = Mock()
        mock_operations.return_value = mock_ops_instance
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        
        assert writer.spark == spark_session
        assert writer.config == mock_config
        assert writer.logger == mock_logger
        assert writer.ops == mock_ops_instance
        assert writer.io_utils == mock_io_instance

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_write_cohort_to_default_location_success(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test successful writing to default location."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        # Create test DataFrame
        test_data = [("ACC001", "USER001"), ("ACC002", "USER002")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        result_path = writer.write_cohort_to_default_location("test_cohort", test_df, "2025-10-15")
        
        expected_path = "s3a://test-bucket/cohorting/individual_cohorts/2025-10-15/test_cohort/"
        assert result_path == expected_path
        
        mock_io_instance.write_csv_file.assert_called_once_with(test_df, expected_path)
        mock_logger.info.assert_called()

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_write_cohort_to_default_location_special_cohort(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test writing special cohort with transformation."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        # Create test DataFrame
        test_data = [("ACC001", "USER001"), ("ACC002", "USER002")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        result_path = writer.write_cohort_to_default_location("viewed_ticker_not_invested_24h", test_df, "2025-10-15")
        
        expected_path = "s3a://test-bucket/cohorting/individual_cohorts/2025-10-15/viewed_ticker_not_invested_24h/"
        assert result_path == expected_path
        
        # Should be called twice - once for regular path, once for clear path
        assert mock_io_instance.write_csv_file.call_count == 2
        
        # Check the clear path call (latest path)
        clear_call = mock_io_instance.write_csv_file.call_args_list[1]
        expected_clear_path = "s3a://test-bucket/cohorting/clevertap_cohort_clear/latest/"
        assert clear_call[0][1] == expected_clear_path

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_write_individual_cohort_output_s3_sink(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test writing to S3 sink."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        # Create test DataFrame
        test_data = [("ACC001", "USER001"), ("ACC002", "USER002")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        
        # Mock the _write_to_s3 method
        expected_path = "s3a://test-bucket/test_output/2025-10-15/test_cohort/"
        with patch.object(writer, '_write_to_s3', return_value=expected_path) as mock_write_s3:
            result_path = writer.write_individual_cohort_output("test_cohort", test_df, "2025-10-15", "14", "S3.test_sink")
        
        assert result_path == expected_path
        mock_write_s3.assert_called_once_with("test_cohort", test_df, "2025-10-15", "14", "test_sink")

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_write_individual_cohort_output_invalid_sink_format(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test error handling for invalid sink format."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        # Create test DataFrame
        test_data = [("ACC001", "USER001")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        
        with pytest.raises(ValueError, match="Invalid data_sink format"):
            writer.write_individual_cohort_output("test_cohort", test_df, "2025-10-15", "14", "invalid_format")

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_write_individual_cohort_output_unknown_sink_type(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test handling of unknown sink type."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        # Create test DataFrame
        test_data = [("ACC001", "USER001")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        
        # Mock the _write_to_s3 method
        expected_path = "s3a://test-bucket/default/path/"
        with patch.object(writer, '_write_to_s3', return_value=expected_path) as mock_write_s3:
            result_path = writer.write_individual_cohort_output("test_cohort", test_df, "2025-10-15", "14", "KAFKA.unknown_sink")
        
        assert result_path == expected_path
        mock_write_s3.assert_called_once_with("test_cohort", test_df, "2025-10-15", "14")
        mock_logger.warning.assert_called_with("Sink type KAFKA not yet implemented. Using default S3 sink.")

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_write_to_s3_with_sink_config(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test writing to S3 with specific sink configuration."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        # Create test DataFrame
        test_data = [("ACC001", "USER001")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        result_path = writer._write_to_s3("test_cohort", test_df, "2025-10-15", "14", "customer_risk_rating")
        
        expected_path = "s3a://test-bucket/risk_rating/2025-10-15/14/test_cohort/"
        assert result_path == expected_path
        
        # Verify the DataFrame.show() was called for logging
        mock_logger.info.assert_called()

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_write_clevertap_cohort_upload_success(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test successful Clevertap cohort upload."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        # Create test DataFrames
        cohort1_data = [("ACC001", "USER001"), ("ACC002", "USER002")]
        cohort2_data = [("ACC001", "USER001"), ("ACC003", "USER003")]
        
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        
        cohort1_df = spark_session.createDataFrame(cohort1_data, test_schema)
        cohort2_df = spark_session.createDataFrame(cohort2_data, test_schema)
        
        cohort_results = [("cohort1", cohort1_df), ("cohort2", cohort2_df)]
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        
        # Mock the _join_cohort_dataframes method
        mock_joined_df = Mock()
        mock_joined_df.distinct.return_value = mock_joined_df
        mock_joined_df.columns = ["account_id", "user_id"]
        mock_joined_df.drop.return_value = mock_joined_df
        mock_joined_df.withColumnRenamed.return_value = mock_joined_df
        mock_joined_df.withColumn.return_value = mock_joined_df
        mock_joined_df.count.return_value = 3
        
        with patch.object(writer, '_join_cohort_dataframes', return_value=mock_joined_df):
            writer.write_clevertap_cohort_upload(cohort_results, "2025-10-15", "14")
        
        # Should write only to date-partitioned path
        assert mock_io_instance.write_csv_file.call_count == 1
        
        # Check call - date-partitioned path
        expected_date_path = "s3a://test-bucket/cohorting/clevertap_upload/dt=2025-10-15/"
        call_args = mock_io_instance.write_csv_file.call_args_list[0]
        assert call_args[0][1] == expected_date_path

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_write_clevertap_cohort_upload_no_data(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test Clevertap upload with no valid data."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        
        # Mock _join_cohort_dataframes to return None
        with patch.object(writer, '_join_cohort_dataframes', return_value=None):
            writer.write_clevertap_cohort_upload([], "2025-10-15", "14")
        
        mock_logger.warning.assert_called_with("No valid cohort data found for Clevertap upload")
        mock_io_instance.write_csv_file.assert_not_called()

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_join_cohort_dataframes_success(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test successful joining of cohort DataFrames."""
        mock_logger = Mock()
        
        # Create test DataFrames
        cohort1_data = [("USER001",), ("USER002",)]
        cohort2_data = [("USER001",), ("USER003",)]
        
        test_schema = StructType([StructField("user_id", StringType(), True)])
        
        cohort1_df = spark_session.createDataFrame(cohort1_data, test_schema)
        cohort2_df = spark_session.createDataFrame(cohort2_data, test_schema)
        
        cohort_results = [("cohort1", cohort1_df), ("cohort2", cohort2_df)]
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        result = writer._join_cohort_dataframes(cohort_results)
        
        assert result is not None
        # The result should have 3 unique users (USER001, USER002, USER003)
        assert result.count() == 3

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_join_cohort_dataframes_empty_input(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test joining with empty input."""
        mock_logger = Mock()
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        result = writer._join_cohort_dataframes([])
        
        assert result is None

    def test_schema_factory_enum(self):
        """Test SchemaFactoryType enum."""
        assert SchemaFactoryType.CUSTOMER_RISK_RATING_KAFKA.value == "CustomerRiskRatingKafkaSchema"
        assert SchemaFactoryType.CUSTOMER_RISK_RATING_S3.value == "CustomerRiskRatingS3Schema"

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_get_available_schema_factories(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test getting available schema factories."""
        mock_logger = Mock()
        
        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        factories = writer.get_available_schema_factories()
        
        expected_factories = ["CustomerRiskRatingKafkaSchema", "CustomerRiskRatingS3Schema"]
        assert set(factories) == set(expected_factories)

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_transform_viewed_ticker_cohort(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test _transform_viewed_ticker_cohort method."""
        mock_logger = Mock()

        # Create test data with asset types and symbols
        data = [
            {"user_id": "user1", "asset_type": "global_stocks", "asset_symbol": "AAPL", "account_id": 1001, "view_count": 10},
            {"user_id": "user1", "asset_type": "crypto_currency", "asset_symbol": "BTC", "account_id": 1001, "view_count": 7},
            {"user_id": "user2", "asset_type": "global_stocks", "asset_symbol": "GOOGL", "account_id": 1002, "view_count": 5},
            {"user_id": "user2", "asset_type": "crypto_future", "asset_symbol": "BTCF", "account_id": 1002, "view_count": 3},
        ]

        schema = StructType([
            StructField("user_id", StringType(), True),
            StructField("asset_type", StringType(), True),
            StructField("asset_symbol", StringType(), True),
            StructField("account_id", IntegerType(), True),
            StructField("view_count", IntegerType(), True)
        ])

        test_df = spark_session.createDataFrame(data, schema)

        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        result = writer._transform_viewed_ticker_cohort(test_df)

        # Verify transformation
        assert result is not None
        result_columns = result.columns

        # Should have pivoted columns
        expected_columns = [
            "user_id", "account_id",
            "Global Stocks Page Visits List",
            "Crypto Currency Page Visits List",
            "Crypto Future Page Visits List"
        ]

        for col in expected_columns:
            assert col in result_columns

        # Verify asset_type and asset_symbol are removed
        assert "asset_type" not in result_columns
        assert "asset_symbol" not in result_columns

        # Verify data transformation
        result_data = result.collect()
        assert len(result_data) == 2  # Two distinct users

        # Validate data using helper
        expected_mappings = {
            "user1": {"Global Stocks Page Visits List": "AAPL", "Crypto Currency Page Visits List": "BTC"},
            "user2": {"Global Stocks Page Visits List": "GOOGL", "Crypto Future Page Visits List": "BTCF"}
        }
        validate_data_integrity(result_data, expected_mappings)

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_transform_viewed_ticker_cohort_empty_data(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test _transform_viewed_ticker_cohort with empty data."""
        mock_logger = Mock()

        # Create empty DataFrame with correct schema
        schema = StructType([
            StructField("user_id", StringType(), True),
            StructField("asset_type", StringType(), True),
            StructField("asset_symbol", StringType(), True),
            StructField("account_id", IntegerType(), True)
        ])

        empty_df = spark_session.createDataFrame([], schema)

        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        result = writer._transform_viewed_ticker_cohort(empty_df)

        # Should handle empty data gracefully
        assert result is not None
        assert result.count() == 0

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_transform_viewed_ticker_cohort_single_asset_type(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test _transform_viewed_ticker_cohort with single asset type."""
        mock_logger = Mock()

        # Create test data with only one asset type
        data = [
            {"user_id": "user1", "asset_type": "global_stocks", "asset_symbol": "AAPL", "account_id": 1001, "view_count": 5},
            {"user_id": "user1", "asset_type": "global_stocks", "asset_symbol": "GOOGL", "account_id": 1001, "view_count": 4},
        ]

        schema = StructType([
            StructField("user_id", StringType(), True),
            StructField("asset_type", StringType(), True),
            StructField("asset_symbol", StringType(), True),
            StructField("account_id", IntegerType(), True),
            StructField("view_count", IntegerType(), True)
        ])

        test_df = spark_session.createDataFrame(data, schema)

        writer = CohortOutputWriter(spark_session, mock_config, mock_logger)
        result = writer._transform_viewed_ticker_cohort(test_df)

        # Verify transformation
        assert result is not None
        result_data = result.collect()
        assert len(result_data) == 1

        # Should have Global Stocks data, others should be null or empty string
        row = result_data[0]
        assert row["Global Stocks Page Visits List"] is not None
        # Spark pivot operations may return empty strings instead of null for missing values
        crypto_currency_value = row["Crypto Currency Page Visits List"]
        crypto_future_value = row["Crypto Future Page Visits List"]
        assert crypto_currency_value is None or crypto_currency_value == ""
        assert crypto_future_value is None or crypto_future_value == ""

    @patch('src.jobs.cohorting.writers.cohort_output_writer.Operations')
    @patch('src.jobs.cohorting.writers.cohort_output_writer.IOUtils')
    def test_transform_viewed_ticker_cohort_multiple_assets_comma_separated(self, mock_io_utils, mock_operations, mock_config, spark_session):
        """Test _transform_viewed_ticker_cohort with multiple assets per user to verify comma-separated format."""
        mock_logger = Mock()

        # Create test data with multiple assets per user per asset type (include view_count for ranking)
        data = [
            {"user_id": "user1", "asset_type": "global_stocks", "asset_symbol": "AAPL",  "account_id": 1001, "view_count": 100},
            {"user_id": "user1", "asset_type": "global_stocks", "asset_symbol": "GOOGL", "account_id": 1001, "view_count": 50},
            {"user_id": "user1", "asset_type": "global_stocks", "asset_symbol": "MSFT",  "account_id": 1001, "view_count": 75},
            {"user_id": "user1", "asset_type": "crypto_currency", "asset_symbol": "BTC",  "account_id": 1001, "view_count": 10},
            {"user_id": "user1", "asset_type": "crypto_currency", "asset_symbol": "ETH",  "account_id": 1001, "view_count": 20},
            {"user_id": "user2", "asset_type": "crypto_future",   "asset_symbol": "BTCF", "account_id": 1002, "view_count": 5},
            {"user_id": "user2", "asset_type": "crypto_future",   "asset_symbol": "ETHF", "account_id": 1002, "view_count": 8},
        ]

        schema = StructType([
            StructField("user_id", StringType(), True),
            StructField("asset_type", StringType(), True),
            StructField("asset_symbol", StringType(), True),
            StructField("account_id", IntegerType(), True),
            StructField("view_count", IntegerType(), True)
        ])

        test_df = spark_session.createDataFrame(data, schema)

        # Override config to request top 2 assets per type
        cfg = dict(mock_config)
        cfg["cohorting"] = {"top_page_visit_assets_num": 2}
        writer = CohortOutputWriter(spark_session, cfg, mock_logger)
        result = writer._transform_viewed_ticker_cohort(test_df)

        # Verify transformation
        assert result is not None
        result_data = result.collect()
        assert len(result_data) == 2  # Two distinct users

        # Verify comma-separated asset lists
        user1_row = next(row for row in result_data if row["user_id"] == "user1")
        user2_row = next(row for row in result_data if row["user_id"] == "user2")

        # Validate comma-separated asset lists using helper (top 2 by view_count)
        expected_mappings = {
            "user1": {
                "Global Stocks Page Visits List": {"AAPL", "MSFT"},
                "Crypto Currency Page Visits List": {"ETH", "BTC"},
                "Crypto Future Page Visits List": None
            },
            "user2": {
                "Global Stocks Page Visits List": None,
                "Crypto Currency Page Visits List": None,
                "Crypto Future Page Visits List": {"ETHF", "BTCF"}
            }
        }
        validate_data_integrity(result_data, expected_mappings)
