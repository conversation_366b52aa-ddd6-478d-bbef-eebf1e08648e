import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestStakingRewardsAccrual:
    """Test class for StakingRewardsAccrual functionality."""

    def _setup_mocks(self, mock_date_utils, mock_operations, mock_io_utils, mock_spark_utils, mock_logger, spark_session):
        """Helper to setup common mocks."""
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
        return mock_io_utils.return_value, mock_operations.return_value

    @patch('src.jobs.crypto_staking.staking_rewards_accrual.get_logger')
    @patch('src.jobs.crypto_staking.staking_rewards_accrual.SparkUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_accrual.IOUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_accrual.Operations')
    @patch('src.jobs.crypto_staking.staking_rewards_accrual.DateUtils')
    def test_init(self, mock_date_utils, mock_operations, mock_io_utils, 
                  mock_spark_utils, mock_logger, mock_crypto_staking_config, spark_session):
        """Test StakingRewardsAccrual initialization."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        self._setup_mocks(mock_date_utils, mock_operations, mock_io_utils, mock_spark_utils, mock_logger, spark_session)
        
        processor = StakingRewardsAccrual(mock_crypto_staking_config)
        
        assert processor.config == mock_crypto_staking_config
        assert processor.bucket_path == "s3a://test-bucket"
        assert processor.t_1 == "2025-01-15"
        assert processor.t_2 == "2025-01-14"
        mock_spark_utils.assert_called_once_with("staking_rewards_accrual")

    def _create_staking_accounts_schema(self):
        """Helper to create staking accounts schema."""
        return StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("staked_balance", DoubleType(), True),
            StructField("unstaking_requested_balance", DoubleType(), True)
        ])

    def _create_staking_assets_schema(self):
        """Helper to create staking assets schema."""
        return StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("reward_disbursal_frequency", StringType(), True),
            StructField("reward_cron_schedule", StringType(), True)
        ])

    def _create_staked_balance_schema(self):
        """Helper to create staked balance schema."""
        return StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("eligible_staked_quantity", DoubleType(), True),
            StructField("ref_id", StringType(), True),
            StructField("reward_frequency", StringType(), True),
            StructField("reward_cron_schedule", StringType(), True)
        ])

    def test_get_staked_balance_for_today_multiple_users_same_coin(self, spark_session):
        """Test multiple users with the same coin scenario."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, 10.0),  # User 1, BTC
            (2, 1002, 102, 1, 1, 825, 200.0, 20.0),  # User 2, BTC
            (3, 1003, 103, 1, 1, 825, 50.0, 5.0),    # User 3, BTC
            (4, 1004, 104, 1, 1, 826, 150.0, 0.0)    # User 4, ETH
        ]
        
        staking_accounts_df = spark_session.createDataFrame(test_data, self._create_staking_accounts_schema())
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        
        result = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
        result_data = result.collect()
        
        assert len(result_data) == 4
        btc_accounts = [row for row in result_data if row['crypto_currency_id'] == 825]
        assert len(btc_accounts) == 3
        
        for row in result_data:
            expected_eligible = row['staked_balance'] + row['unstaking_requested_balance']
            assert abs(row['eligible_staked_quantity'] - expected_eligible) < 0.********

    def test_get_staked_balance_for_today_multiple_coins_per_user(self, spark_session):
        """Test multiple coins per user scenario."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        # Create test data with users having multiple crypto currencies
        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, 10.0),  # User 1, BTC
            (2, 1001, 101, 1, 1, 826, 200.0, 20.0),  # User 1, ETH
            (3, 1001, 101, 1, 1, 827, 50.0, 5.0),    # User 1, ADA
            (4, 1002, 102, 1, 1, 825, 150.0, 0.0),   # User 2, BTC
            (5, 1002, 102, 1, 1, 826, 75.0, 25.0)    # User 2, ETH
        ]
        
        schema = StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("staked_balance", DoubleType(), True),
            StructField("unstaking_requested_balance", DoubleType(), True)
        ])
        
        staking_accounts_df = spark_session.createDataFrame(test_data, schema)
        
        # Create mock processor
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        
        # Execute the method
        result = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
        
        # Verify results
        result_data = result.collect()
        assert len(result_data) == 5  # All accounts have positive eligible staked quantity
        
        # Check User 1 has 3 different coins
        user1_accounts = [row for row in result_data if row['user_id'] == 101]
        assert len(user1_accounts) == 3
        user1_coins = set([row['crypto_currency_id'] for row in user1_accounts])
        assert user1_coins == {825, 826, 827}
        
        # Check User 2 has 2 different coins
        user2_accounts = [row for row in result_data if row['user_id'] == 102]
        assert len(user2_accounts) == 2
        user2_coins = set([row['crypto_currency_id'] for row in user2_accounts])
        assert user2_coins == {825, 826}

    def test_zero_balance_accounts_are_skipped(self, spark_session):
        """Test that zero balance accounts are skipped."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, 10.0),  # Positive balance
            (2, 1002, 102, 1, 1, 826, 0.0, 0.0),     # Zero balance
            (3, 1003, 103, 1, 1, 827, 50.0, 0.0),    # Positive balance
            (4, 1004, 104, 1, 1, 828, 0.0, 10.0),    # Positive unstaking balance
            (5, 1005, 105, 1, 1, 829, 0.0, 0.0)      # Zero balance
        ]
        
        staking_accounts_df = spark_session.createDataFrame(test_data, self._create_staking_accounts_schema())
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        
        result = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
        result_data = result.collect()
        
        assert len(result_data) == 3
        account_ids = [row['account_id'] for row in result_data]
        assert set(account_ids) == {1001, 1003, 1004}

    def test_negative_invalid_balances_are_filtered_out(self, spark_session):
        """Test that negative/invalid balances are filtered out."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, 10.0),   # Positive balance
            (2, 1002, 102, 1, 1, 826, -50.0, 0.0),    # Negative staked balance
            (3, 1003, 103, 1, 1, 827, 0.0, -25.0),    # Negative unstaking balance
            (4, 1004, 104, 1, 1, 828, -10.0, -5.0),   # Both negative
            (5, 1005, 105, 1, 1, 829, 75.0, -25.0)    # Net positive but has negative component
        ]
        
        staking_accounts_df = spark_session.createDataFrame(test_data, self._create_staking_accounts_schema())
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        
        result = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
        result_data = result.collect()
        
        assert len(result_data) == 2
        account_ids = [row['account_id'] for row in result_data]
        assert set(account_ids) == {1001, 1005}
        assert 1002 not in account_ids  # Account 2 - negative result
        assert 1003 not in account_ids  # Account 3 - negative result
        assert 1004 not in account_ids  # Account 4 - negative result

    def test_ref_id_generation_and_consistency(self, spark_session):
        """Test ref_id generation ensures no duplicates and consistency across runs."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, 10.0),
            (2, 1002, 102, 1, 1, 826, 200.0, 20.0),
            (3, 1003, 103, 1, 1, 825, 50.0, 5.0)
        ]
        
        staking_accounts_df = spark_session.createDataFrame(test_data, self._create_staking_accounts_schema())
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        
        # Test first run
        result1 = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
        result1_data = result1.collect()
        ref_ids1 = [row['ref_id'] for row in result1_data]
        
        # Test second run (consistency)
        result2 = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
        result2_data = result2.collect()
        ref_ids2 = [row['ref_id'] for row in result2_data]
        
        # Verify uniqueness and consistency
        assert len(ref_ids1) == len(set(ref_ids1))
        assert sorted(ref_ids1) == sorted(ref_ids2)
        
        expected_ref_ids = {"1001_825_20250115", "1002_826_20250115", "1003_825_20250115"}
        assert set(ref_ids1) == expected_ref_ids

    def test_get_staking_snapshot_data_integration(self, spark_session):
        """Test get_staking_snapshot_data method integration."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual

        processor = Mock()
        processor.bucket_path = "s3a://test-bucket"
        processor.t_1 = "2025-01-15"
        processor.logger = Mock()
        processor.io_utils = Mock()

        accounts_data = [(1, 1001, 101, 1, 1, 825, 100.0, 10.0), (2, 1002, 102, 1, 1, 826, 200.0, 20.0)]
        assets_data = [(825, '{"interval": "WEEKLY"}', "0 0 * * 1"), (826, '{"interval": "MONTHLY"}', "0 0 1 * *")]

        accounts_df = spark_session.createDataFrame(accounts_data, self._create_staking_accounts_schema())
        assets_df = spark_session.createDataFrame(assets_data, self._create_staking_assets_schema())

        def mock_read_parquet_data(path):
            if "crypto_currency_staking_accounts" in path:
                return accounts_df
            elif "crypto_currency_staking_assets" in path:
                return assets_df
            return None

        processor.io_utils.read_parquet_data.side_effect = mock_read_parquet_data

        accounts_result, assets_result = StakingRewardsAccrual.get_staking_snapshot_data(processor)

        assert accounts_result.count() == 2
        assert assets_result.count() == 2

        call_args = processor.io_utils.read_parquet_data.call_args_list
        called_paths = [call[0][0] for call in call_args]
        assert "crypto_currency_staking_accounts" in called_paths[0]
        assert "crypto_currency_staking_assets" in called_paths[1]

    def test_create_kafka_events_structure(self, spark_session):
        """Test create_kafka_events method structure."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual

        test_data = [
            (1, 1001, 101, 1, 1, 825, 110.0, "1001_825_20250115", "WEEKLY", "0 0 * * 1"),
            (2, 1002, 102, 1, 1, 826, 220.0, "1002_826_20250115", "MONTHLY", "0 0 1 * *")
        ]

        staked_balance_df = spark_session.createDataFrame(test_data, self._create_staked_balance_schema())

        trigger_time = datetime(2025, 1, 15, 10, 0, 0)

        processor = Mock()
        processor.config = {"crypto_staking_topic_actor_prop": "accrued_rewards_actor"}
        processor.ops = Mock()

        with patch('src.jobs.crypto_staking.staking_rewards_accrual.DateUtils') as mock_date_utils:
            mock_date_utils.get_utc_timestamp.return_value = trigger_time

            result = StakingRewardsAccrual.create_kafka_events(processor, staked_balance_df)

            columns = result.columns
            assert all(col in columns for col in ["key", "value", "headers"])
            assert len(result.collect()) == 2

    def test_write_reward_accrual_history(self, spark_session):
        """Test write_reward_accrual_history method."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual

        test_data = [
            (1, 1001, 101, 1, 1, 825, 110.0, "1001_825_20250115", "WEEKLY", "0 0 * * 1"),
            (2, 1002, 102, 1, 1, 826, 220.0, "1002_826_20250115", "MONTHLY", "0 0 1 * *")
        ]

        staked_balance_df = spark_session.createDataFrame(test_data, self._create_staked_balance_schema())

        processor = Mock()
        processor.bucket_path = "s3a://test-bucket"
        processor.t_1 = "2025-01-15"
        processor.io_utils = Mock()

        StakingRewardsAccrual.write_reward_accrual_history(processor, staked_balance_df)

        expected_path = "s3a://test-bucket/crypto_staking/rewards_accrual_history/dt=2025-01-15/"
        processor.io_utils.write_parquet_file.assert_called_once_with(staked_balance_df, expected_path, 3)

    def test_edge_case_very_small_balances(self, spark_session):
        """Test edge case with very small balances."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual

        test_data = [
            (1, 1001, 101, 1, 1, 825, 0.********, 0.0),      # Very small positive
            (2, 1002, 102, 1, 1, 826, 0.0, 0.********),      # Very small unstaking
            (3, 1003, 103, 1, 1, 827, 0.********, 0.********), # Both very small
            (4, 1004, 104, 1, 1, 828, 0.0, 0.0)              # Zero
        ]

        staking_accounts_df = spark_session.createDataFrame(test_data, self._create_staking_accounts_schema())

        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()

        result = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
        result_data = result.collect()

        assert len(result_data) == 3
        account_ids = [row['account_id'] for row in result_data]
        assert set(account_ids) == {1001, 1002, 1003}

    def test_precision_handling_in_calculations(self, spark_session):
        """Test precision handling in eligible_staked_quantity calculations."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual

        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.********, 10.********),  # High precision
            (2, 1002, 102, 1, 1, 826, 0.********, 0.********),     # Edge precision
        ]

        staking_accounts_df = spark_session.createDataFrame(test_data, self._create_staking_accounts_schema())

        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()

        result = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
        result_data = result.collect()

        expected_values = [111.0, 1.0]  # 100.******** + 10.********, 0.******** + 0.********
        for i, record in enumerate(result_data):
            assert abs(record['eligible_staked_quantity'] - expected_values[i]) < 0.********
