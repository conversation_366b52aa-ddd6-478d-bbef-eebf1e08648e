"""
Unit tests for BaseCohortProcessorInterface.
"""

import pytest
from unittest.mock import Mock
from abc import ABC
from pyspark.sql.types import StructType, StructField, StringType, IntegerType
from src.jobs.cohorting.processors.base_cohort_processor_interface import BaseCohortProcessorInterface


class Concrete<PERSON>ohortProcessor(BaseCohortProcessorInterface):
    """Concrete implementation of BaseCohortProcessorInterface for testing."""
    
    def process_cohort(self, cohort_id, cohort_config, cohort_params=None):
        """Concrete implementation of process_cohort."""
        # Create a simple test DataFrame
        data = [{"user_id": "user1", "account_id": 1001}]
        schema = StructType([
            StructField("user_id", StringType(), True),
            StructField("account_id", IntegerType(), True)
        ])
        return self.spark.createDataFrame(data, schema)


class IncompleteCohortProcessor(BaseCohortProcessorInterface):
    """Incomplete implementation that doesn't implement abstract methods."""
    pass


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        "bucket_path": "s3://test-bucket",
        "t_1": "2025-10-14",
        "database_config": {
            "host": "test-host",
            "port": 27017
        }
    }


@pytest.fixture
def mock_parameter_registry():
    """Mock parameter registry for testing."""
    registry = Mock()
    registry.get_parameter_configs.return_value = []
    return registry


class TestBaseCohortProcessorInterface:
    """Test cases for BaseCohortProcessorInterface."""

    def test_abstract_class_cannot_be_instantiated(self):
        """Test that BaseCohortProcessorInterface cannot be instantiated directly."""
        with pytest.raises(TypeError):
            BaseCohortProcessorInterface(Mock(), {}, Mock())

    def test_incomplete_implementation_cannot_be_instantiated(self):
        """Test that incomplete implementations cannot be instantiated."""
        with pytest.raises(TypeError):
            IncompleteCohortProcessor(Mock(), {}, Mock())

    def test_concrete_implementation_initialization(self, mock_config, spark_session):
        """Test concrete implementation initialization."""
        mock_logger = Mock()
        mock_parameter_registry = Mock()
        
        processor = ConcreteCohortProcessor(
            spark_session, 
            mock_config, 
            mock_logger, 
            mock_parameter_registry
        )
        
        # Verify initialization
        assert processor.spark == spark_session
        assert processor.config == mock_config
        assert processor.logger == mock_logger
        assert processor.parameter_registry == mock_parameter_registry

    def test_concrete_implementation_initialization_without_parameter_registry(self, mock_config, spark_session):
        """Test concrete implementation initialization without parameter registry."""
        mock_logger = Mock()
        
        processor = ConcreteCohortProcessor(spark_session, mock_config, mock_logger)
        
        # Verify initialization
        assert processor.spark == spark_session
        assert processor.config == mock_config
        assert processor.logger == mock_logger
        assert processor.parameter_registry is None

    def test_process_cohort_abstract_method_implementation(self, mock_config, spark_session):
        """Test that process_cohort is properly implemented in concrete class."""
        mock_logger = Mock()
        processor = ConcreteCohortProcessor(spark_session, mock_config, mock_logger)
        
        cohort_config = {"cohort_id": "test_cohort", "name": "Test Cohort"}
        cohort_params = {"param1": "value1"}
        
        result = processor.process_cohort("test_cohort", cohort_config, cohort_params)
        
        # Verify result is a DataFrame
        assert result is not None
        assert hasattr(result, 'columns')  # DataFrame-like object
        assert result.count() == 1

    def test_process_cohort_with_minimal_parameters(self, mock_config, spark_session):
        """Test process_cohort with minimal parameters."""
        mock_logger = Mock()
        processor = ConcreteCohortProcessor(spark_session, mock_config, mock_logger)
        
        result = processor.process_cohort("test_cohort", {})
        
        # Verify result
        assert result is not None
        assert result.count() == 1

    def test_process_cohort_with_none_cohort_params(self, mock_config, spark_session):
        """Test process_cohort with None cohort_params."""
        mock_logger = Mock()
        processor = ConcreteCohortProcessor(spark_session, mock_config, mock_logger)
        
        result = processor.process_cohort("test_cohort", {}, None)
        
        # Verify result
        assert result is not None
        assert result.count() == 1

    def test_inheritance_structure(self):
        """Test that BaseCohortProcessorInterface properly inherits from ABC."""
        assert issubclass(BaseCohortProcessorInterface, ABC)
        
        # Verify abstract methods are defined
        abstract_methods = BaseCohortProcessorInterface.__abstractmethods__
        assert "process_cohort" in abstract_methods

    def test_interface_contract_method_signature(self):
        """Test that the interface defines the correct method signature."""
        # Get the abstract method
        process_cohort_method = BaseCohortProcessorInterface.process_cohort
        
        # Verify it's an abstract method
        assert hasattr(process_cohort_method, '__isabstractmethod__')
        assert process_cohort_method.__isabstractmethod__ is True

    def test_concrete_implementation_method_signature(self, mock_config, spark_session):
        """Test that concrete implementation has correct method signature."""
        mock_logger = Mock()
        processor = ConcreteCohortProcessor(spark_session, mock_config, mock_logger)
        
        # Test with all parameters
        result1 = processor.process_cohort("cohort1", {"config": "value"}, {"param": "value"})
        assert result1 is not None
        
        # Test with optional parameter as None
        result2 = processor.process_cohort("cohort2", {"config": "value"}, None)
        assert result2 is not None
        
        # Test with optional parameter omitted (default None)
        result3 = processor.process_cohort("cohort3", {"config": "value"})
        assert result3 is not None

    def test_initialization_parameter_types(self, mock_config, spark_session):
        """Test initialization with different parameter types."""
        mock_logger = Mock()
        mock_parameter_registry = Mock()
        
        # Test with all parameters
        processor1 = ConcreteCohortProcessor(
            spark_session, 
            mock_config, 
            mock_logger, 
            mock_parameter_registry
        )
        assert processor1.parameter_registry == mock_parameter_registry
        
        # Test with parameter_registry as None
        processor2 = ConcreteCohortProcessor(spark_session, mock_config, mock_logger, None)
        assert processor2.parameter_registry is None
        
        # Test without parameter_registry (defaults to None)
        processor3 = ConcreteCohortProcessor(spark_session, mock_config, mock_logger)
        assert processor3.parameter_registry is None

    def test_interface_documentation(self):
        """Test that the interface has proper documentation."""
        # Check class docstring
        assert BaseCohortProcessorInterface.__doc__ is not None
        assert "Abstract interface for all cohort processors" in BaseCohortProcessorInterface.__doc__
        
        # Check method docstring
        assert BaseCohortProcessorInterface.process_cohort.__doc__ is not None
        assert "Process a cohort and return the result DataFrame" in BaseCohortProcessorInterface.process_cohort.__doc__

    def test_multiple_concrete_implementations(self, mock_config, spark_session):
        """Test that multiple concrete implementations can coexist."""
        class AlternateCohortProcessor(BaseCohortProcessorInterface):
            def process_cohort(self, cohort_id, cohort_config, cohort_params=None):
                data = [{"user_id": "alt_user", "value": 999}]
                schema = StructType([
                    StructField("user_id", StringType(), True),
                    StructField("value", IntegerType(), True)
                ])
                return self.spark.createDataFrame(data, schema)
        
        mock_logger = Mock()
        
        # Create instances of both implementations
        processor1 = ConcreteCohortProcessor(spark_session, mock_config, mock_logger)
        processor2 = AlternateCohortProcessor(spark_session, mock_config, mock_logger)
        
        # Test both work independently
        result1 = processor1.process_cohort("test1", {})
        result2 = processor2.process_cohort("test2", {})
        
        assert result1.columns != result2.columns  # Different schemas
        assert result1.collect()[0]["user_id"] != result2.collect()[0]["user_id"]  # Different data

    def test_error_handling_in_concrete_implementation(self, mock_config, spark_session):
        """Test error handling in concrete implementation."""
        class ErrorCohortProcessor(BaseCohortProcessorInterface):
            def process_cohort(self, cohort_id, cohort_config, cohort_params=None):
                raise ValueError("Test error in process_cohort")
        
        mock_logger = Mock()
        processor = ErrorCohortProcessor(spark_session, mock_config, mock_logger)
        
        with pytest.raises(ValueError, match="Test error in process_cohort"):
            processor.process_cohort("error_cohort", {})
