"""
Unit tests for CohortConfigReader.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pyspark.sql.types import StructType, StructField, StringType, ArrayType, TimestampType, DoubleType
from src.jobs.cohorting.config_readers.cohort_config_reader import CohortConfigReader


class TestCohortConfigReader:
    """Test cases for CohortConfigReader."""

    @pytest.fixture
    def mock_config(self):
        """Mock configuration."""
        return {
            "t_1": "2025-10-15",
            "t_2": "2025-10-16",
            "data_store": {
                "reporting_mongo": {
                    "host": "localhost",
                    "port": 27017,
                    "database": "test_db",
                    "username": "test_user",
                    "password": "test_pass"
                }
            }
        }

    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.IOUtils')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.ParameterRegistry')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.get_logger')
    def test_initialization(self, mock_get_logger, mock_parameter_registry, mock_io_utils, mock_config, spark_session):
        """Test CohortConfigReader initialization."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        mock_param_registry = Mock()
        mock_parameter_registry.return_value = mock_param_registry
        
        reader = CohortConfigReader(spark_session, mock_config, mock_logger)
        
        assert reader.spark == spark_session
        assert reader.config == mock_config
        assert reader.logger == mock_logger
        assert reader.t_1 == "2025-10-15"
        assert reader.t_2 == "2025-10-16"
        assert reader.cohorts_collection == "pluang_data_eng.cohorting_engine_user_cohorts"
        assert reader.parameters_collection == "pluang_data_eng.cohorting_engine_parameters"
        
        mock_logger.info.assert_called()

    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.IOUtils')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.ParameterRegistry')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.get_logger')
    def test_get_active_cohorts_success(self, mock_get_logger, mock_parameter_registry, mock_io_utils, mock_config, spark_session):
        """Test successful retrieval of active cohorts."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        # Create mock DataFrame
        mock_df = Mock()
        mock_df.count.return_value = 2
        
        mock_io_instance = Mock()
        mock_io_instance.read_from_mongodb.return_value = mock_df
        mock_io_utils.return_value = mock_io_instance
        
        mock_param_registry = Mock()
        mock_parameter_registry.return_value = mock_param_registry
        
        reader = CohortConfigReader(spark_session, mock_config, mock_logger)
        result = reader.get_active_cohorts("2025-10-15")
        
        assert result == mock_df
        mock_io_instance.read_from_mongodb.assert_called_once()
        
        # Verify the pipeline was constructed correctly
        call_args = mock_io_instance.read_from_mongodb.call_args
        assert call_args[1]['collection'] == "pluang_data_eng.cohorting_engine_user_cohorts"
        assert '"is_active": true' in call_args[1]['pipeline']
        assert '"start_date": {"$lte": "2025-10-15"}' in call_args[1]['pipeline']
        assert '"end_date": {"$gte": "2025-10-15"}' in call_args[1]['pipeline']

    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.IOUtils')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.ParameterRegistry')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.get_logger')
    def test_get_active_cohorts_error(self, mock_get_logger, mock_parameter_registry, mock_io_utils, mock_config, spark_session):
        """Test error handling in get_active_cohorts."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        mock_io_instance = Mock()
        mock_io_instance.read_from_mongodb.side_effect = Exception("MongoDB connection failed")
        mock_io_utils.return_value = mock_io_instance
        
        mock_param_registry = Mock()
        mock_parameter_registry.return_value = mock_param_registry
        
        reader = CohortConfigReader(spark_session, mock_config, mock_logger)
        
        # Mock the _create_empty_cohorts_dataframe method
        mock_empty_df = Mock()
        with patch.object(reader, '_create_empty_cohorts_dataframe', return_value=mock_empty_df):
            result = reader.get_active_cohorts("2025-10-15")
        
        assert result == mock_empty_df
        mock_logger.error.assert_called_with("Error fetching active cohorts: MongoDB connection failed")

    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.IOUtils')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.ParameterRegistry')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.get_logger')
    def test_get_all_parameters_for_cohorts_success(self, mock_get_logger, mock_parameter_registry, mock_io_utils, mock_config, spark_session):
        """Test successful parameter extraction from cohorts."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        # Create mock rows with parameters
        mock_row1 = Mock()
        mock_row1.parameters = [{"parameter_id": "param1"}, {"parameter_id": "param2"}]
        mock_row1.__getitem__ = lambda self, key: getattr(self, key)
        
        mock_row2 = Mock()
        mock_row2.parameters = [{"parameter_id": "param2"}, {"parameter_id": "param3"}]
        mock_row2.__getitem__ = lambda self, key: getattr(self, key)
        
        mock_df = Mock()
        mock_df.collect.return_value = [mock_row1, mock_row2]
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        mock_param_registry = Mock()
        mock_parameter_registry.return_value = mock_param_registry
        
        reader = CohortConfigReader(spark_session, mock_config, mock_logger)
        
        # Mock get_parameter_configs_by_ids
        expected_configs = {"param1": {}, "param2": {}, "param3": {}}
        with patch.object(reader, 'get_parameter_configs_by_ids', return_value=expected_configs) as mock_get_configs:
            result = reader.get_all_parameters_for_cohorts(mock_df)
        
        assert result == expected_configs
        # Check that the method was called with the correct parameter IDs (order doesn't matter)
        call_args = mock_get_configs.call_args[0][0]
        assert set(call_args) == {'param1', 'param2', 'param3'}

    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.IOUtils')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.ParameterRegistry')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.get_logger')
    def test_get_all_parameters_for_cohorts_no_parameters(self, mock_get_logger, mock_parameter_registry, mock_io_utils, mock_config, spark_session):
        """Test parameter extraction when no parameters exist."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        # Create mock row with empty parameters
        mock_row = Mock()
        mock_row.parameters = []  # Empty parameters list
        mock_row.__getitem__ = lambda self, key: getattr(self, key)
        
        mock_df = Mock()
        mock_df.collect.return_value = [mock_row]
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        mock_param_registry = Mock()
        mock_parameter_registry.return_value = mock_param_registry
        
        reader = CohortConfigReader(spark_session, mock_config, mock_logger)
        result = reader.get_all_parameters_for_cohorts(mock_df)
        
        assert result == {}
        # Check that the specific log message was called (it might not be the last call)
        expected_message = "No parameters found in any cohorts (custom processors don't use parameters)"
        info_calls = [call.args[0] for call in mock_logger.info.call_args_list]
        assert expected_message in info_calls

    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.IOUtils')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.ParameterRegistry')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.get_logger')
    def test_get_parameter_configs_by_ids_success(self, mock_get_logger, mock_parameter_registry, mock_io_utils, mock_config, spark_session):
        """Test successful parameter config retrieval."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        expected_configs = {"param1": {"name": "Test Param 1"}, "param2": {"name": "Test Param 2"}}
        mock_param_registry = Mock()
        mock_param_registry.get_parameter_configs.return_value = expected_configs
        mock_parameter_registry.return_value = mock_param_registry
        
        reader = CohortConfigReader(spark_session, mock_config, mock_logger)
        result = reader.get_parameter_configs_by_ids(["param1", "param2"])
        
        assert result == expected_configs
        mock_param_registry.get_parameter_configs.assert_called_once_with(["param1", "param2"])

    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.IOUtils')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.ParameterRegistry')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.get_logger')
    def test_get_parameter_configs_by_ids_empty(self, mock_get_logger, mock_parameter_registry, mock_io_utils, mock_config, spark_session):
        """Test parameter config retrieval with empty list."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        mock_param_registry = Mock()
        mock_parameter_registry.return_value = mock_param_registry
        
        reader = CohortConfigReader(spark_session, mock_config, mock_logger)
        result = reader.get_parameter_configs_by_ids([])
        
        assert result == {}
        mock_param_registry.get_parameter_configs.assert_not_called()

    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.IOUtils')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.ParameterRegistry')
    @patch('src.jobs.cohorting.config_readers.cohort_config_reader.get_logger')
    def test_create_empty_cohorts_dataframe(self, mock_get_logger, mock_parameter_registry, mock_io_utils, mock_config, spark_session):
        """Test creation of empty cohorts DataFrame."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        mock_param_registry = Mock()
        mock_parameter_registry.return_value = mock_param_registry
        
        reader = CohortConfigReader(spark_session, mock_config, mock_logger)
        result = reader._create_empty_cohorts_dataframe()
        
        # Verify it's a DataFrame with the expected schema
        assert hasattr(result, 'schema')
        assert hasattr(result, 'count')
        
        # Check that it has the expected columns
        column_names = [field.name for field in result.schema.fields]
        expected_columns = [
            "cohort_id", "name", "description", "start_date", "end_date",
            "data_sinks", "assimilation_schedule", "cohort_processor",
            "data_sources", "parameters", "cohort_logic", "created_at", "updated_at"
        ]
        for col in expected_columns:
            assert col in column_names
