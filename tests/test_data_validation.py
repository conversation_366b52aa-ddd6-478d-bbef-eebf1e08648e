import pytest
import os
import sys
from datetime import datetime
from unittest.mock import Mock, patch
from pyspark.sql.types import StructType, StructField, LongType, StringType, TimestampType, DoubleType

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.jobs.data_validation.forex_validation import ForexValidation, ValidationConfig


class TestForexValidation:
    """Test cases for ForexValidation class."""

    @pytest.fixture
    def mock_forex_config(self):
        """Mock configuration for ForexValidation."""
        return {
            "bucket_path": "s3a://test-bucket",
            "env": "test",
            "t_1": "2025-01-15",
            "t_2": "2025-01-14"
        }

    @pytest.fixture
    def sample_forex_transactions_data(self, spark_session):
        """Sample forex transactions data for testing."""
        data = [
            (1001, 101, "wallet_1", 1, datetime(2025, 1, 15, 10, 0, 0), 5.0, 
             "forex_1", "id_1", 1, "price_1", 100.0, "ref_1", "COMPLETED", 
             15000.0, "config_1", datetime(2025, 1, 15, 10, 0, 0), "BUY", 
             150.0, datetime(2025, 1, 15, 10, 5, 0)),
            (1002, 102, "wallet_2", 1, datetime(2025, 1, 15, 11, 0, 0), 3.0,
             "forex_2", "id_2", 1, "price_2", 200.0, "ref_2", "COMPLETED",
             15200.0, "config_2", datetime(2025, 1, 15, 11, 0, 0), "SELL",
             152.0, datetime(2025, 1, 15, 11, 5, 0)),
            (1003, 103, "wallet_3", 1, datetime(2025, 1, 15, 12, 0, 0), 2.0,
             "forex_3", "id_3", 1, "price_3", 50.0, "ref_3", "PENDING",
             15100.0, "config_3", datetime(2025, 1, 15, 12, 0, 0), "BUY",
             151.0, datetime(2025, 1, 15, 12, 5, 0))
        ]

        schema = StructType([
            StructField("user_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("centralized_wallet_id", StringType(), True),
            StructField("client_id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("fee", DoubleType(), True),
            StructField("forex_id", StringType(), True),
            StructField("id", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("partner_price_id", StringType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("ref_id", StringType(), True),
            StructField("status", StringType(), True),
            StructField("total_price", DoubleType(), True),
            StructField("transaction_fee_config_id", StringType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("unit_price", DoubleType(), True),
            StructField("updated", TimestampType(), True)
        ])

        return spark_session.createDataFrame(data, schema)

    @pytest.fixture
    def sample_forex_unhedged_data(self, spark_session):
        """Sample forex unhedged transactions data for testing."""
        data = [
            ("unhedged_1", "id_1", "forex_transactions", "forex_1", 100.0, 150.0,
             datetime(2025, 1, 15, 10, 0, 0), "COMPLETED", "order_1",
             datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 15, 10, 5, 0)),
            ("unhedged_2", "id_2", "forex_transactions", "forex_2", 200.0, 152.0,
             datetime(2025, 1, 15, 11, 0, 0), "COMPLETED", "order_2",
             datetime(2025, 1, 15, 11, 0, 0), datetime(2025, 1, 15, 11, 5, 0)),
            ("unhedged_4", "id_4", "forex_transactions", "forex_4", 75.0, 148.0,
             datetime(2025, 1, 15, 13, 0, 0), "HEDGED", "order_4",
             datetime(2025, 1, 15, 13, 0, 0), datetime(2025, 1, 15, 13, 5, 0))
        ]

        schema = StructType([
            StructField("id", StringType(), True),
            StructField("transaction_id", StringType(), True),
            StructField("table_name", StringType(), True),
            StructField("forex_id", StringType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("unit_price", DoubleType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("status", StringType(), True),
            StructField("inventory_order_id", StringType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True)
        ])

        return spark_session.createDataFrame(data, schema)

    @pytest.fixture
    def mock_validation_results(self):
        """Mock validation results from DataValidatorService."""
        return {
            'status': 'success',
            'results': {
                'matched_df': Mock(),
                'missing_in_df1': Mock(),
                'missing_in_df2': Mock(),
                'summary': {
                    'total_records': 1000,
                    'matched_count': 800,
                    'total_missing': 200,
                    'match_percentage': 80.0,
                    'coverage_percentage': 85.0,
                    'total_records_df1': 900,
                    'total_records_df2': 850,
                    'missing_in_df1_count': 50,
                    'missing_in_df2_count': 100
                }
            },
            'processing_time': 45.5
        }

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_forex_validation_initialization(self, mock_logger, mock_validator_service, 
                                           mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test ForexValidation initialization with proper configuration."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Assertions
        assert forex_validation.config == mock_forex_config
        assert forex_validation.bucket_path == "s3a://test-bucket"
        assert forex_validation.t_1 == "2025-01-15"
        assert forex_validation.t_2 == "2025-01-14"
        assert forex_validation.join_keys == "id transaction_id"
        assert forex_validation.output_format == "csv"
        assert forex_validation.estimate_time == True
        
        # Verify Spark session creation
        mock_spark_utils_instance.create_spark_session.assert_called_once()
        
        # Verify logger initialization
        mock_logger.assert_called_once_with(env="test", tag="Forex Transaction Validation")

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_forex_validation_with_custom_parameters(self, mock_logger, mock_validator_service,
                                                   mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test ForexValidation initialization with custom parameters."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        # Custom parameters
        custom_params = {
            "file1_path": "custom/path1",
            "file2_path": "custom/path2",
            "output_path": "custom/output",
            "join_keys": "custom_id1 custom_id2",
            "output_format": "parquet"
        }
        
        # Initialize ForexValidation with custom parameters
        forex_validation = ForexValidation(mock_forex_config, **custom_params)
        
        # Assertions
        assert forex_validation.file1_path == "custom/path1"
        assert forex_validation.file2_path == "custom/path2"
        assert forex_validation.output_path == "custom/output"
        assert forex_validation.join_keys == "custom_id1 custom_id2"
        assert forex_validation.output_format == "parquet"

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_load_data_csv_format(self, mock_logger, mock_validator_service,
                                 mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test data loading for CSV format."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        mock_io_utils_instance = Mock()
        mock_sample_df = Mock()
        mock_io_utils_instance.read_csv_file.return_value = mock_sample_df
        mock_io_utils.return_value = mock_io_utils_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Test CSV file loading
        result_df = forex_validation._load_data("test_file.csv")
        
        # Assertions
        mock_io_utils_instance.read_csv_file.assert_called_once_with("test_file.csv")
        assert result_df == mock_sample_df

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_load_data_parquet_format(self, mock_logger, mock_validator_service,
                                     mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test data loading for Parquet format."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        mock_io_utils_instance = Mock()
        mock_sample_df = Mock()
        mock_io_utils_instance.read_parquet_data.return_value = mock_sample_df
        mock_io_utils.return_value = mock_io_utils_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Test Parquet file loading
        result_df = forex_validation._load_data("test_file.parquet")
        
        # Assertions
        mock_io_utils_instance.read_parquet_data.assert_called_once_with("test_file.parquet")
        assert result_df == mock_sample_df

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_load_s3_data_parquet_success(self, mock_logger, mock_validator_service,
                                         mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test S3 data loading with successful Parquet read."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        mock_io_utils_instance = Mock()
        mock_sample_df = Mock()
        mock_io_utils_instance.read_parquet_data.return_value = mock_sample_df
        mock_io_utils.return_value = mock_io_utils_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Test S3 Parquet loading
        result_df = forex_validation._load_s3_data("s3a://bucket/path")
        
        # Assertions
        mock_io_utils_instance.read_parquet_data.assert_called_once_with("s3a://bucket/path")
        assert result_df == mock_sample_df

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_load_s3_data_fallback_to_csv(self, mock_logger, mock_validator_service,
                                         mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test S3 data loading with Parquet failure and CSV fallback."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        mock_io_utils_instance = Mock()
        # Parquet read fails, CSV succeeds
        mock_sample_df = Mock()
        mock_io_utils_instance.read_parquet_data.side_effect = Exception("Parquet read failed")
        mock_io_utils_instance.read_csv_file.return_value = mock_sample_df
        mock_io_utils.return_value = mock_io_utils_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Test S3 CSV fallback
        result_df = forex_validation._load_s3_data("s3a://bucket/path")
        
        # Assertions
        mock_io_utils_instance.read_parquet_data.assert_called_once_with("s3a://bucket/path")
        mock_io_utils_instance.read_csv_file.assert_called_once_with("s3a://bucket/path")
        assert result_df == mock_sample_df

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_perform_validation_with_service(self, mock_logger, mock_validator_service_class,
                                           mock_io_utils, mock_spark_utils, mock_forex_config,
                                           mock_validation_results):
        """Test the complete validation workflow using DataValidatorService."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        mock_io_utils_instance = Mock()
        mock_io_utils.return_value = mock_io_utils_instance
        
        # Mock DataValidatorService
        mock_validator_service_instance = Mock()
        mock_validator_service_instance.perform_validation.return_value = mock_validation_results
        mock_validator_service_class.return_value = mock_validator_service_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Mock the _load_data method to return our sample data
        mock_df1 = Mock()
        mock_df2 = Mock()
        forex_validation._load_data = Mock()
        forex_validation._load_data.side_effect = [mock_df1, mock_df2]
        
        # Mock the _write_results method
        forex_validation._write_results = Mock()
        forex_validation._write_results.return_value = {
            'matched_records': '/path/to/matched',
            'missing_in_forex_transactions': '/path/to/missing1',
            'missing_in_forex_unhedged_transactions': '/path/to/missing2'
        }
        
        # Execute validation
        result = forex_validation._perform_validation_with_service()
        
        # Assertions
        assert result['status'] == 'success'
        assert result['processing_time'] == 45.5
        assert 'output_files' in result
        
        # Verify DataValidatorService was called with correct parameters
        mock_validator_service_instance.perform_validation.assert_called_once()
        call_args = mock_validator_service_instance.perform_validation.call_args
        
        # Check that join keys were parsed correctly
        assert call_args[1]['join_keys'] == ('id', 'transaction_id')
        
        # Check validation config
        validation_config = call_args[1]['validation_config']
        assert validation_config['include_data_quality'] == True
        assert validation_config['include_missing_analysis'] == True
        assert validation_config['include_field_comparison'] == True
        assert validation_config['target_fields'] == ["quantity", "unit_price"]

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_write_results_csv_format(self, mock_logger, mock_validator_service,
                                     mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test writing validation results in CSV format."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        # Mock DataFrames
        mock_matched_df = Mock()
        mock_missing_df1 = Mock()
        mock_missing_df1.count.return_value = 5
        mock_missing_df2 = Mock()
        mock_missing_df2.count.return_value = 3
        
        validation_results = {
            'matched_df': mock_matched_df,
            'missing_in_df1': mock_missing_df1,
            'missing_in_df2': mock_missing_df2
        }
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Mock _write_dataframe method
        forex_validation._write_dataframe = Mock()
        
        # Execute write results
        output_files = forex_validation._write_results(validation_results)
        
        # Assertions
        assert 'matched_records' in output_files
        assert 'missing_in_forex_transactions' in output_files
        assert 'missing_in_forex_unhedged_transactions' in output_files
        
        # Verify _write_dataframe was called for each result type
        assert forex_validation._write_dataframe.call_count == 3

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_validation_config_validation_error(self, mock_logger, mock_validator_service,
                                              mock_io_utils, mock_spark_utils):
        """Test validation configuration error handling."""
        # Invalid config - missing required fields
        invalid_config = {
            "bucket_path": "s3a://test-bucket",
            # Missing t_1
        }
        
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        # Initialize ForexValidation with invalid config
        with pytest.raises(KeyError):
            ForexValidation(invalid_config)

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_validate_config_missing_arguments(self, mock_logger, mock_validator_service,
                                             mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test configuration validation with missing arguments."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Clear required fields to simulate missing arguments
        forex_validation.file1_path = None
        
        # Test validation
        with pytest.raises(ValueError, match="Missing required arguments"):
            forex_validation._validate_config()

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_validate_config_invalid_output_format(self, mock_logger, mock_validator_service,
                                                  mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test configuration validation with invalid output format."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        # Initialize ForexValidation with invalid output format
        forex_validation = ForexValidation(mock_forex_config, output_format="invalid_format")
        
        # Test validation
        with pytest.raises(ValueError, match="Invalid output format"):
            forex_validation._validate_config()

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_data_loading_error_handling(self, mock_logger, mock_validator_service,
                                        mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test error handling during data loading."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        mock_io_utils_instance = Mock()
        mock_io_utils_instance.read_csv_file.side_effect = Exception("File not found")
        mock_io_utils.return_value = mock_io_utils_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Test data loading with error
        with pytest.raises(Exception, match="File not found"):
            forex_validation._load_data("non_existent_file.csv")

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_validation_workflow_error_handling(self, mock_logger, mock_validator_service_class,
                                               mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test error handling in validation workflow."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        # Mock DataValidatorService to raise an exception
        mock_validator_service_instance = Mock()
        mock_validator_service_instance.perform_validation.side_effect = Exception("Validation failed")
        mock_validator_service_class.return_value = mock_validator_service_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Mock _load_data to avoid file loading issues
        forex_validation._load_data = Mock()
        forex_validation._load_data.return_value = Mock()
        
        # Execute validation workflow
        result = forex_validation._perform_validation_with_service()
        
        # Assertions
        assert result['status'] == 'error'
        assert 'Validation workflow failed' in result['error']
        assert result['processing_time'] == 0

    def test_validation_config_dataclass(self):
        """Test ValidationConfig dataclass functionality."""
        # Test default initialization
        config = ValidationConfig()
        assert config.include_data_quality == True
        assert config.include_missing_analysis == True
        assert config.include_field_comparison == True
        assert config.target_fields == ["quantity", "unit_price"]
        
        # Test custom initialization
        custom_config = ValidationConfig(
            include_data_quality=False,
            target_fields=["price", "amount"]
        )
        assert custom_config.include_data_quality == False
        assert custom_config.include_missing_analysis == True
        assert custom_config.target_fields == ["price", "amount"]

    @patch('src.jobs.data_validation.forex_validation.SparkUtils')
    @patch('src.jobs.data_validation.forex_validation.IOUtils')
    @patch('src.jobs.data_validation.forex_validation.DataValidatorService')
    @patch('src.jobs.data_validation.forex_validation.get_logger')
    def test_spark_session_cleanup(self, mock_logger, mock_validator_service,
                                  mock_io_utils, mock_spark_utils, mock_forex_config):
        """Test Spark session cleanup functionality."""
        # Setup mocks
        mock_spark_session = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark_session
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        mock_logger_instance = Mock()
        mock_logger.return_value = mock_logger_instance
        
        # Initialize ForexValidation
        forex_validation = ForexValidation(mock_forex_config)
        
        # Test Spark session cleanup
        forex_validation._stop_spark_session()
        
        # Assertions
        mock_spark_utils_instance.stop_spark.assert_called_once_with(mock_spark_session)
        assert forex_validation.spark is None
