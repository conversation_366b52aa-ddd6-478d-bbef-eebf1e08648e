"""
Helper functions for cohorting tests to reduce code duplication.
"""

from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, LongType


def validate_data_integrity(result_data, expected_mappings):
    """
    Validate data integrity with expected mappings.

    Args:
        result_data: List of Row objects from DataFrame.collect()
        expected_mappings: Dict mapping row identifiers to expected values
    """
    data_by_key = {}
    for row in result_data:
        # Use first column as key (usually user_id or account_id)
        row_dict = row.asDict()
        key = list(row_dict.values())[0]  # First column value
        data_by_key[key] = row_dict

    for key, expected in expected_mappings.items():
        row = data_by_key[key]
        for column, expected_value in expected.items():
            actual_value = row[column]
            if isinstance(expected_value, set):
                # For comma-separated lists
                if actual_value:
                    assert isinstance(actual_value, str)
                    assert set(actual_value.split(",")) == expected_value
                else:
                    assert not expected_value  # Both should be empty/None
            elif expected_value is None:
                assert actual_value is None or actual_value == ""
            else:
                assert actual_value == expected_value


def validate_positive_integers(result_data, columns):
    """Validate that specified columns contain positive integers."""
    for row in result_data:
        row_dict = row.asDict() if hasattr(row, 'asDict') else row
        for column in columns:
            if column in row_dict:
                value = row_dict[column]
                assert isinstance(value, int) and value > 0


def validate_positive_numbers(result_data, columns):
    """Validate that specified columns contain positive numbers."""
    for row in result_data:
        row_dict = row.asDict() if hasattr(row, 'asDict') else row
        for column in columns:
            if column in row_dict:
                value = row_dict[column]
                assert isinstance(value, (int, float)) and value > 0


def validate_non_empty_strings(result_data, columns):
    """Validate that specified columns contain non-empty strings."""
    for row in result_data:
        for column in columns:
            value = row[column]
            assert isinstance(value, str) and len(value) > 0


def validate_comma_separated_lists(result_data, column_expectations):
    """
    Validate comma-separated list columns.
    
    Args:
        result_data: List of Row objects
        column_expectations: Dict mapping column names to expected sets of values
    """
    for row in result_data:
        for column, expected_set in column_expectations.items():
            value = row[column]
            if expected_set:
                assert isinstance(value, str)
                actual_set = set(value.split(","))
                assert actual_set == expected_set
            else:
                assert value is None or value == ""


def create_test_schema(fields):
    """Create a StructType schema from field definitions."""
    type_mapping = {
        'string': StringType(),
        'int': IntegerType(),
        'double': DoubleType(),
        'long': LongType()
    }
    
    struct_fields = []
    for field_name, field_type in fields.items():
        struct_fields.append(StructField(field_name, type_mapping[field_type], True))
    
    return StructType(struct_fields)


def assert_basic_dataframe_properties(result, expected_count, expected_columns):
    """Assert basic DataFrame properties."""
    assert result is not None
    assert result.count() == expected_count
    for column in expected_columns:
        assert column in result.columns


def create_mock_setup(mock_classes, return_values=None):
    """Create standardized mock setup for common patterns."""
    mocks = {}
    for mock_class in mock_classes:
        mock_instance = mock_class.return_value
        mocks[mock_class.__name__] = mock_instance
        
        if return_values and mock_class.__name__ in return_values:
            for method_name, return_value in return_values[mock_class.__name__].items():
                getattr(mock_instance, method_name).return_value = return_value
    
    return mocks
