import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch, call
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestStakingRewardsAccrualIntegration:
    """Integration tests for StakingRewardsAccrual functionality."""

    def _setup_mocks(self, mock_date_utils, mock_operations, mock_io_utils, mock_spark_utils, mock_logger, spark_session):
        """Helper to setup common mocks for integration tests."""
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 10, 0, 0)
        return mock_io_utils.return_value, mock_operations.return_value

    def _convert_snake_to_camel(self, df):
        """Helper to convert snake_case to camelCase - no longer used, kept for compatibility."""
        # No conversion needed - column names remain in snake_case
        return df

    def _create_staking_accounts_schema(self):
        """Helper to create staking accounts schema."""
        return StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("staked_balance", DoubleType(), True),
            StructField("unstaking_requested_balance", DoubleType(), True)
        ])

    def _create_staking_assets_schema(self):
        """Helper to create staking assets schema."""
        return StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("reward_frequency", StringType(), True),
            StructField("reward_cron_schedule", StringType(), True)
        ])

    def _create_staked_balance_schema(self):
        """Helper to create staked balance schema."""
        return StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("eligible_staked_quantity", DoubleType(), True),
            StructField("ref_id", StringType(), True),
            StructField("reward_frequency", StringType(), True),
            StructField("reward_cron_schedule", StringType(), True)
        ])

    @patch('src.jobs.crypto_staking.staking_rewards_accrual.get_logger')
    @patch('src.jobs.crypto_staking.staking_rewards_accrual.SparkUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_accrual.IOUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_accrual.Operations')
    @patch('src.jobs.crypto_staking.staking_rewards_accrual.DateUtils')
    def test_full_processing_workflow(self, mock_date_utils, mock_operations,
                                     mock_io_utils, mock_spark_utils, mock_logger,
                                     mock_crypto_staking_config, spark_session,
                                     sample_staking_accounts_data, sample_staking_assets_data):
        """Test the complete processing workflow integration."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        io_utils, ops = self._setup_mocks(mock_date_utils, mock_operations, mock_io_utils, mock_spark_utils, mock_logger, spark_session)
        
        def mock_read_parquet_data(path):
            if "crypto_currency_staking_accounts" in path:
                return sample_staking_accounts_data
            elif "crypto_currency_staking_assets" in path:
                return sample_staking_assets_data
            return None

        io_utils.read_parquet_data.side_effect = mock_read_parquet_data
        io_utils.write_data_in_kafka = Mock()
        io_utils.write_parquet_file = Mock()
        
        processor = StakingRewardsAccrual(mock_crypto_staking_config)
        processor.io_utils = io_utils
        processor.ops = ops
        
        processor.start_processing()
        
        assert io_utils.read_parquet_data.call_count == 2
        io_utils.write_data_in_kafka.assert_called_once()
        io_utils.write_parquet_file.assert_called_once()

    def test_end_to_end_data_flow_with_multiple_scenarios(self, spark_session):
        """Test end-to-end data flow with multiple user/coin scenarios."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        accounts_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, 10.0),  # User 1, BTC
            (2, 1002, 102, 1, 1, 825, 200.0, 20.0),  # User 2, BTC
            (3, 1003, 103, 1, 1, 825, 50.0, 5.0),    # User 3, BTC
            (4, 1001, 101, 1, 1, 826, 150.0, 0.0),   # User 1, ETH
            (5, 1001, 101, 1, 1, 827, 75.0, 25.0),   # User 1, ADA
            (6, 1004, 104, 1, 1, 828, 0.0, 0.0),     # User 4, DOT - zero
            (7, 1005, 105, 1, 1, 829, -10.0, 0.0),   # User 5, MATIC - negative
        ]
        
        assets_data = [
            (825, "WEEKLY", "0 0 * * 1"),    # BTC
            (826, "MONTHLY", "0 0 1 * *"),   # ETH
            (827, "WEEKLY", "0 0 * * 0"),    # ADA
            (828, "MONTHLY", "0 0 15 * *"),  # DOT
            (829, "WEEKLY", "0 0 * * 2")     # MATIC
        ]
        
        accounts_df = spark_session.createDataFrame(accounts_data, self._create_staking_accounts_schema())
        assets_df = spark_session.createDataFrame(assets_data, self._create_staking_assets_schema())
        joined_data = accounts_df.join(assets_df, on="crypto_currency_id", how="inner")
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        processor.config = {"crypto_staking_topic_actor_prop": "accrued_rewards_actor"}
        processor.ops = Mock()

        result = StakingRewardsAccrual.get_staked_balance_for_today(processor, joined_data)
        result_data = result.collect()
        
        assert len(result_data) == 5
        
        # Verify multiple users with same coin (BTC - 825)
        btc_accounts = [row for row in result_data if row['crypto_currency_id'] == 825]
        assert len(btc_accounts) == 3
        assert set([row['user_id'] for row in btc_accounts]) == {101, 102, 103}
        
        # Verify multiple coins per user (User 101)
        user1_accounts = [row for row in result_data if row['user_id'] == 101]
        assert len(user1_accounts) == 3
        assert set([row['crypto_currency_id'] for row in user1_accounts]) == {825, 826, 827}
        
        # Verify zero and negative balance accounts are excluded
        account_ids = [row['account_id'] for row in result_data]
        assert set(account_ids) == {1001, 1002, 1003}

    def test_kafka_event_creation_integration(self, spark_session):
        """Test Kafka event creation with realistic data."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        test_data = [
            (1, 1001, 101, 1, 1, 825, 110.0, "1001_825_20250115", "WEEKLY", "0 0 * * 1"),
            (2, 1001, 101, 1, 1, 826, 220.0, "1001_826_20250115", "MONTHLY", "0 0 1 * *"),
            (3, 1002, 102, 1, 1, 825, 55.0, "1002_825_20250115", "WEEKLY", "0 0 * * 1"),
        ]
        
        staked_balance_df = spark_session.createDataFrame(test_data, self._create_staked_balance_schema())
        
        processor = Mock()
        processor.config = {"crypto_staking_topic_actor_prop": "accrued_rewards_actor"}
        processor.ops = Mock()
        
        with patch('src.jobs.crypto_staking.staking_rewards_accrual.DateUtils') as mock_date_utils:
            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 10, 0, 0)
            
            result = StakingRewardsAccrual.create_kafka_events(processor, staked_balance_df)
            result_data = result.collect()
            
            assert len(result_data) == 2
            
            for row in result_data:
                assert all(col in row.asDict() for col in ["key", "value", "headers"])
                assert row["key"] in ["1001", "1002"]

    def test_history_writing_integration(self, spark_session):
        """Test reward accrual history writing integration."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        test_data = [
            (1, 1001, 101, 1, 1, 825, 110.0, "1001_825_20250115", "WEEKLY", "0 0 * * 1"),
            (2, 1002, 102, 1, 1, 826, 220.0, "1002_826_20250115", "MONTHLY", "0 0 1 * *")
        ]
        
        staked_balance_df = spark_session.createDataFrame(test_data, self._create_staked_balance_schema())
        
        processor = Mock()
        processor.bucket_path = "s3a://test-bucket"
        processor.t_1 = "2025-01-15"
        processor.io_utils = Mock()
        
        StakingRewardsAccrual.write_reward_accrual_history(processor, staked_balance_df)
        
        expected_path = "s3a://test-bucket/crypto_staking/rewards_accrual_history/dt=2025-01-15/"
        processor.io_utils.write_parquet_file.assert_called_once_with(staked_balance_df, expected_path, 3)

    def test_duplicate_prevention_across_runs(self, spark_session):
        """Test that duplicate ref_id entries are prevented across multiple runs."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, 10.0),
            (2, 1002, 102, 1, 1, 826, 200.0, 20.0)
        ]
        
        staking_accounts_df = spark_session.createDataFrame(test_data, self._create_staking_accounts_schema())
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        
        # Simulate multiple runs on the same date
        results = []
        for run in range(3):
            result = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
            results.append(result.collect())
        
        # Verify all runs produce identical ref_ids
        for i in range(1, len(results)):
            ref_ids_current = sorted([row['ref_id'] for row in results[i]])
            ref_ids_previous = sorted([row['ref_id'] for row in results[i-1]])
            assert ref_ids_current == ref_ids_previous
        
        # Verify ref_ids are deterministic
        expected_ref_ids = {"1001_825_20250115", "1002_826_20250115"}
        actual_ref_ids = set([row['ref_id'] for row in results[0]])
        assert actual_ref_ids == expected_ref_ids

    def test_error_handling_integration(self, spark_session):
        """Test error handling in integration scenarios."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        problematic_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, 10.0),     # Valid
            (2, 1002, 102, 1, 1, 826, None, 20.0),      # Null staked_balance
            (3, 1003, 103, 1, 1, 827, 50.0, None),      # Null unstaking_balance
        ]
        
        staking_accounts_df = spark_session.createDataFrame(problematic_data, self._create_staking_accounts_schema())
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        
        try:
            result = StakingRewardsAccrual.get_staked_balance_for_today(processor, staking_accounts_df)
            result_data = result.collect()
            assert len(result_data) >= 0
        except Exception as e:
            assert "null" in str(e).lower() or "none" in str(e).lower()

    def test_performance_with_large_dataset_simulation(self, spark_session):
        """Test performance characteristics with simulated large dataset."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        # Create larger test dataset
        large_data = []
        for i in range(1000):  # Simulate 1000 accounts
            account_id = 1000 + i
            user_id = 100 + (i % 100)  # 100 unique users
            crypto_id = 825 + (i % 5)  # 5 different crypto currencies
            staked_balance = float(100 + (i % 1000))
            unstaking_balance = float(i % 50)
            
            large_data.append((i+1, account_id, user_id, 1, 1, crypto_id, staked_balance, unstaking_balance))
        
        large_df = spark_session.createDataFrame(large_data, self._create_staking_accounts_schema())
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        
        import time
        start_time = time.time()
        
        result = StakingRewardsAccrual.get_staked_balance_for_today(processor, large_df)
        result_count = result.count()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert result_count == 1000
        assert execution_time < 30
        
        # Verify ref_id uniqueness even with large dataset
        ref_ids = [row['ref_id'] for row in result.collect()]
        assert len(ref_ids) == len(set(ref_ids))
