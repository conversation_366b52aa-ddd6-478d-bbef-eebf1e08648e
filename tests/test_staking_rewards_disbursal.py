import pytest
import sys
import os
from datetime import datetime, timedelta, date
from unittest.mock import Mock, MagicMock, patch, call
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestStakingRewardsDisbursal:
    """Test class for StakingRewardsDisbursal functionality."""

    def _setup_mocks(self, mock_date_utils, mock_asset_utils, mock_operations, 
                     mock_io_utils, mock_spark_utils, mock_logger, spark_session):
        """Helper to setup common mocks."""
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
        return mock_io_utils.return_value, mock_asset_utils.return_value, mock_operations.return_value

    def _create_assets_schema(self):
        """Helper to create assets schema."""
        return StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("reward_frequency", StringType(), True),
            StructField("reward_cron_schedule", StringType(), True),
            StructField("reward_disbursal_window", StringType(), True)
        ])

    def _create_rewards_schema(self):
        """Helper to create rewards schema."""
        return StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("ref_id", StringType(), True),
            StructField("eligible_staked_quantity", DoubleType(), True),
            StructField("gross_reward_quantity", DoubleType(), True),
            StructField("reward_trigger_time", TimestampType(), True),
            StructField("created", TimestampType(), True)
        ])

    def _create_history_schema(self):
        """Helper to create history schema."""
        return StructType([
            StructField("account_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("last_reward_disbursal_date", DateType(), True)
        ])

    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.get_logger')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.SparkUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.IOUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.Operations')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.AssetUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.DateUtils')
    def test_init(self, mock_date_utils, mock_asset_utils, mock_operations,
                  mock_io_utils, mock_spark_utils, mock_logger,
                  mock_crypto_staking_config, spark_session):
        """Test StakingRewardsDisbursal initialization."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal
        
        self._setup_mocks(mock_date_utils, mock_asset_utils, mock_operations, 
                         mock_io_utils, mock_spark_utils, mock_logger, spark_session)
        
        processor = StakingRewardsDisbursal(mock_crypto_staking_config)
        
        assert processor.config == mock_crypto_staking_config
        assert processor.bucket_path == "s3a://test-bucket"
        assert processor.offset == 0
        assert processor.execution_time == "jkt_day_end"
        assert processor.t_1 == "2025-01-15"
        assert processor.t_2 == "2025-01-14"
        
        mock_spark_utils.assert_called_once_with("staking_rewards_accrual")
        mock_io_utils.assert_called_once()
        mock_operations.assert_called_once()
        mock_asset_utils.assert_called_once()

    def test_multiple_assets_having_rewards_due_today(self, spark_session):
        """Test multiple assets having rewards due today."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal
        
        current_date = datetime(2025, 1, 20).date()  # Monday
        
        assets_data = [
            (825, "WEEKLY", "0 0 * * 1", "CALENDAR"),    # BTC - Weekly Monday (due today)
            (826, "WEEKLY", "0 0 * * 1", "CALENDAR"),    # ETH - Weekly Monday (due today)
            (827, "WEEKLY", "0 0 * * 2", "CALENDAR"),    # ADA - Weekly Tuesday (not due)
            (828, "MONTHLY", "0 0 20 * *", "CALENDAR"),  # DOT - Monthly 20th (due today if 20th)
        ]
        
        rewards_data = [
            (1, 1001, 101, 1, 1, 825, "1001_825_20250120", 100.0, 0.5, 
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 12, 0, 0)),
            (2, 1002, 102, 1, 1, 826, "1002_826_20250120", 200.0, 1.0,
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 14, 0, 0)),
            (3, 1003, 103, 1, 1, 827, "1003_827_20250120", 50.0, 0.25,
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 16, 0, 0)),
        ]
        
        assets_df = spark_session.createDataFrame(assets_data, self._create_assets_schema())
        rewards_df = spark_session.createDataFrame(rewards_data, self._create_rewards_schema())
        joined_data = assets_df.join(rewards_df, on="crypto_currency_id", how="inner")
        
        processor = Mock()
        processor.current_date = current_date

        result = joined_data.withColumn("is_cron_valid", F.lit(True)) \
                           .withColumn("cron_run_date",
                                     F.when(F.col("reward_cron_schedule") == "0 0 * * 1", F.lit(current_date))
                                      .when(F.col("reward_cron_schedule") == "0 0 * * 2", F.lit(datetime(2025, 1, 21).date()))
                                      .when(F.col("reward_cron_schedule") == "0 0 20 * *", F.lit(current_date))
                                      .otherwise(F.lit(None)))
        
        due_today = result.filter(F.col("cron_run_date") == current_date)
        due_today_data = due_today.collect()

        crypto_ids_due = [row['crypto_currency_id'] for row in due_today_data]
        assert set(crypto_ids_due) == {825, 826}  # BTC and ETH due on Monday

    def test_different_cron_types_and_validation(self, spark_session):
        """Test different cron types and validation logic."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal
        
        test_data = [
            (825, "WEEKLY", "0 0 * * 1", "CALENDAR"),     # Day-of-week: Monday
            (826, "WEEKLY", "0 0 * * 0", "CALENDAR"),     # Day-of-week: Sunday
            (827, "MONTHLY", "0 0 1 * *", "CALENDAR"),    # Date-of-month: 1st
            (828, "MONTHLY", "0 0 15 * *", "CALENDAR"),   # Date-of-month: 15th
            (829, "INVALID", "invalid cron", "CALENDAR"),  # Invalid cron
        ]
        
        test_df = spark_session.createDataFrame(test_data, self._create_assets_schema())
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()  # Wednesday, 15th

        result = test_df.withColumn("is_cron_valid",
                                   F.when(F.col("reward_cron_schedule") == "invalid cron", F.lit(False))
                                    .otherwise(F.lit(True))) \
                        .withColumn("cron_run_date",
                                   F.when(F.col("reward_cron_schedule") == "0 0 * * 1", F.lit(datetime(2025, 1, 20).date()))
                                    .when(F.col("reward_cron_schedule") == "0 0 * * 0", F.lit(datetime(2025, 1, 19).date()))
                                    .when(F.col("reward_cron_schedule") == "0 0 1 * *", F.lit(datetime(2025, 2, 1).date()))
                                    .when(F.col("reward_cron_schedule") == "0 0 15 * *", F.lit(processor.current_date))
                                    .otherwise(F.lit(None)))
        result_data = result.collect()
        
        valid_crons = [row for row in result_data if row['is_cron_valid']]
        invalid_crons = [row for row in result_data if not row['is_cron_valid']]
        
        assert len(valid_crons) == 4
        assert len(invalid_crons) == 1
        
        # Check specific cron run dates for valid ones
        for row in valid_crons:
            if row['crypto_currency_id'] == 828:  # Monthly 15th
                if processor.current_date.day == 15:
                    assert row['cron_run_date'] == processor.current_date

    def test_scenario_where_no_assets_match_today_criteria(self, spark_session):
        """Test scenario where no assets match today's criteria."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal
        
        current_date = datetime(2025, 1, 15).date()  # Wednesday
        
        assets_data = [
            (825, "WEEKLY", "0 0 * * 1", "CALENDAR"),    # Monday only
            (826, "WEEKLY", "0 0 * * 2", "CALENDAR"),    # Tuesday only
            (827, "MONTHLY", "0 0 1 * *", "CALENDAR"),   # 1st of month only
            (828, "MONTHLY", "0 0 30 * *", "CALENDAR"),  # 30th of month only
        ]
        
        assets_df = spark_session.createDataFrame(assets_data, self._create_assets_schema())
        
        processor = Mock()
        processor.current_date = current_date
        
        result = StakingRewardsDisbursal.transform_accrued_rewards_data(processor, assets_df)
        
        due_today = result.filter(F.col("cron_run_date") == current_date)
        due_today_count = due_today.count()
        
        assert due_today_count == 0

    def test_first_time_disbursal_scenarios(self, spark_session):
        """Test first-time disbursal scenarios."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal
        
        current_date = datetime(2025, 1, 20).date()  # Monday
        
        assets_data = [
            (825, "WEEKLY", "0 0 * * 1", "CALENDAR"),
            (826, "WEEKLY", "0 0 * * 1", "CALENDAR")
        ]
        
        rewards_data = [
            (1, 1001, 101, 1, 1, 825, "1001_825_20250120", 100.0, 0.5,
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 12, 0, 0)),
            (2, 1002, 102, 1, 1, 826, "1002_826_20250120", 200.0, 1.0,
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 14, 0, 0)),
        ]
        
        assets_df = spark_session.createDataFrame(assets_data, self._create_assets_schema())
        rewards_df = spark_session.createDataFrame(rewards_data, self._create_rewards_schema())
        empty_history_df = spark_session.createDataFrame([], self._create_history_schema())
        
        joined_data = assets_df.join(rewards_df, on="crypto_currency_id", how="inner")

        transformed_data = joined_data.withColumn("is_cron_valid", F.lit(True)) \
                                     .withColumn("cron_run_date", F.lit(current_date))
        
        with_history = transformed_data.join(
            empty_history_df, 
            on=["account_id", "crypto_currency_id"], 
            how="left"
        )
        
        processor = Mock()
        processor.current_date = current_date
        processor.logger = Mock()
        
        filtered_data = with_history.filter(
            (F.col("cron_run_date") == current_date) &
            (F.col("last_reward_disbursal_date").isNull())
        )
        
        result_data = filtered_data.collect()
        
        assert len(result_data) == 2
        
        for row in result_data:
            assert row['last_reward_disbursal_date'] is None

    def test_rewards_added_at_cutoff_exclusion(self, spark_session):
        """Test rewards with created_at matching last disbursal timestamp are excluded."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal
        
        current_date = datetime(2025, 1, 20).date()  # Monday
        last_disbursal_date = datetime(2025, 1, 13).date()  # Previous Monday
        
        rewards_data = [
            (1, 1001, 101, 1, 1, 825, "1001_825_20250120", 100.0, 0.5,
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 14, 12, 0, 0)),
            (2, 1001, 101, 1, 1, 825, "1001_825_20250120", 100.0, 0.5,
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 13, 0, 0, 0)),
            (3, 1001, 101, 1, 1, 825, "1001_825_20250120", 100.0, 0.5,
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 15, 8, 0, 0)),
        ]
        
        rewards_df = spark_session.createDataFrame(rewards_data, self._create_rewards_schema())
        
        history_data = [(1001, 825, last_disbursal_date)]
        history_df = spark_session.createDataFrame(history_data, self._create_history_schema())
        
        with_history = rewards_df.join(
            history_df,
            on=["account_id", "crypto_currency_id"],
            how="left"
        )
        
        filtered_rewards = with_history.filter(
            (F.col("last_reward_disbursal_date").isNull()) |
            (F.date_format(F.col("created"), "yyyy-MM-dd") > F.col("last_reward_disbursal_date"))
        )
        
        result_data = filtered_rewards.collect()
        
        assert len(result_data) == 2
        
        included_ids = [row['id'] for row in result_data]
        assert set(included_ids) == {1, 3}

    def test_rerunning_job_no_duplicates_or_double_disbursals(self, spark_session):
        """Test that re-running the job with identical inputs does not create duplicates."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal

        current_date = datetime(2025, 1, 20).date()  # Monday

        test_data = [
            (1, 1001, 101, 1, 1, 825, "1001_825_20250120", 100.0, 0.5,
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 12, 0, 0)),
            (2, 1002, 102, 1, 1, 826, "1002_826_20250120", 200.0, 1.0,
             datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 14, 0, 0)),
        ]

        test_df = spark_session.createDataFrame(test_data, self._create_rewards_schema())

        processor = Mock()
        processor.current_date = current_date

        # Simulate first run - generate aggregated rewards
        first_run_result = test_df.groupBy("account_id", "crypto_currency_id").agg(
            F.sum("gross_reward_quantity").alias("gross_reward_quantity"),
            F.min("id").alias("first_accrued_reward_id"),
            F.max("id").alias("last_accrued_reward_id"),
            F.first("user_id").alias("user_id"),
            F.first("client_id").alias("client_id"),
            F.first("partner_id").alias("partner_id")
        )

        current_date_formatted = current_date.strftime("%Y%m%d")
        first_run_with_ref = first_run_result.withColumn(
            "ref_id",
            F.concat(
                F.col("account_id"),
                F.lit("_"),
                F.col("crypto_currency_id"),
                F.lit("_"),
                F.lit(current_date_formatted)
            )
        )

        # Simulate second run with same data
        second_run_result = test_df.groupBy("account_id", "crypto_currency_id").agg(
            F.sum("gross_reward_quantity").alias("gross_reward_quantity"),
            F.min("id").alias("first_accrued_reward_id"),
            F.max("id").alias("last_accrued_reward_id"),
            F.first("user_id").alias("user_id"),
            F.first("client_id").alias("client_id"),
            F.first("partner_id").alias("partner_id")
        )

        second_run_with_ref = second_run_result.withColumn(
            "ref_id",
            F.concat(
                F.col("account_id"),
                F.lit("_"),
                F.col("crypto_currency_id"),
                F.lit("_"),
                F.lit(current_date_formatted)
            )
        )

        first_run_data = first_run_with_ref.collect()
        second_run_data = second_run_with_ref.collect()

        assert len(first_run_data) == len(second_run_data)

        first_ref_ids = sorted([row['ref_id'] for row in first_run_data])
        second_ref_ids = sorted([row['ref_id'] for row in second_run_data])
        assert first_ref_ids == second_ref_ids

        first_amounts = sorted([row['gross_reward_quantity'] for row in first_run_data])
        second_amounts = sorted([row['gross_reward_quantity'] for row in second_run_data])
        assert first_amounts == second_amounts

    @patch('requests.get')
    def test_get_crypto_price_with_retries_success_and_failure(self, mock_get):
        """Test crypto price retrieval success and failure scenarios."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal

        # Test success scenario
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": {
                "sellPrice": "50000.00"
            }
        }
        mock_get.return_value = mock_response

        processor = Mock()
        processor.config = {"crypto_currency_price_api": "https://api.test.com/price"}
        processor.logger = Mock()

        price = StakingRewardsDisbursal.get_crypto_price_with_retries(processor, "BTC")

        assert price == 50000.0
        mock_get.assert_called_once_with("https://api.test.com/price?cryptocurrency=BTC")

        # Test failure scenario
        mock_response.status_code = 500
        mock_get.reset_mock()

        with pytest.raises(Exception):
            StakingRewardsDisbursal.get_crypto_price_with_retries(processor, "BTC", max_retries=2, retry_delay=0.1)

        assert mock_get.call_count == 2

    def test_validate_accrued_rewards_data_and_cron_expression_validation(self, spark_session):
        """Test validation of accrued rewards data and cron expression validation."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal

        # Test validation with mismatches
        transformed_data = [
            (1001, 825, "1001_825_20250120", 100.0),
            (1002, 826, "1002_826_20250120", 200.0),
            (1003, 827, "1003_827_20250120", 50.0)  # This will have mismatch
        ]

        transformed_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("ref_id", StringType(), True),
            StructField("eligible_staked_quantity", DoubleType(), True)
        ])

        transformed_df = spark_session.createDataFrame(transformed_data, transformed_schema) \
                                       .withColumn("is_cron_valid", F.lit(True))

        history_data = [
            (1001, 825, "1001_825_20250120", 100.0),  # Match
            (1002, 826, "1002_826_20250120", 200.0),  # Match
            (1003, 827, "1003_827_20250120", 75.0)    # Mismatch - different quantity
        ]

        history_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("ref_id", StringType(), True),
            StructField("eligible_staked_quantity", DoubleType(), True)
        ])

        history_df = spark_session.createDataFrame(history_data, history_schema)

        processor = Mock()
        processor.bucket_path = "s3a://test-bucket"
        processor.t_1 = "2025-01-20"
        processor.logger = Mock()
        processor.io_utils = Mock()
        processor.io_utils.read_parquet_data.return_value = history_df

        StakingRewardsDisbursal.validate_accrued_rewards_data(processor, transformed_df)

        processor.logger.error.assert_called()

        # Test cron expression validation
        test_data = [
            (825, "WEEKLY", "0 0 * * 1", "CALENDAR"),      # Valid weekly
            (826, "MONTHLY", "0 0 1 * *", "CALENDAR"),     # Valid monthly
            (827, "WEEKLY", "0 0 * * 8", "CALENDAR"),      # Invalid day of week
            (828, "MONTHLY", "0 0 32 * *", "CALENDAR"),    # Invalid day of month
            (829, "WEEKLY", "invalid", "CALENDAR"),        # Invalid format
            (830, "MONTHLY", "", "CALENDAR"),              # Empty cron
        ]

        test_df = spark_session.createDataFrame(test_data, self._create_assets_schema())

        processor.current_date = datetime(2025, 1, 20).date()

        result = StakingRewardsDisbursal.transform_accrued_rewards_data(processor, test_df)

        result_data = result.collect()

        valid_count = len([row for row in result_data if row['is_cron_valid']])
        invalid_count = len([row for row in result_data if not row['is_cron_valid']])

        assert valid_count == 2
        assert invalid_count == 4

    def test_cutoff_date_calculation_logic(self, spark_session):
        """Test cutoff date calculation for different window types."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal

        current_date = datetime(2025, 1, 20).date()  # Monday

        # Test different scenarios
        test_cases = [
            ("WEEKLY", "ROLLING", current_date),  # Should use current date
            ("WEEKLY", "CALENDAR", current_date - timedelta(days=1)),  # Should use last Sunday
            ("MONTHLY", "ROLLING", current_date),  # Should use current date
            ("MONTHLY", "CALENDAR", datetime(2024, 12, 31).date()),  # Last day of prev month
        ]

        for frequency, window_type, expected_cutoff in test_cases:
            # Mock the cutoff calculation logic
            if frequency == "WEEKLY":
                if window_type == "ROLLING":
                    calculated_cutoff = current_date
                elif window_type == "CALENDAR":
                    days_since_sunday = current_date.weekday() + 1
                    calculated_cutoff = current_date - timedelta(days=days_since_sunday)
            elif frequency == "MONTHLY":
                if window_type == "ROLLING":
                    calculated_cutoff = current_date
                elif window_type == "CALENDAR":
                    calculated_cutoff = current_date.replace(day=1) - timedelta(days=1)

            assert calculated_cutoff == expected_cutoff
