"""
Unit tests for BaseSchemaFactory.
"""

import pytest
from unittest.mock import Mock, patch
from abc import ABC
from pyspark.sql.types import StructType, StructField, StringType, IntegerType
from src.jobs.cohorting.schema_factories.base_schema_factory import BaseSchemaFactory


class ConcreteSchemaFactory(BaseSchemaFactory):
    """Concrete implementation of BaseSchemaFactory for testing."""
    
    def get_schema(self):
        """Return a test schema."""
        return {
            "user_id": "string",
            "account_id": "long",
            "risk_rating": "string"
        }
    
    def transform_data(self, df, cohort_id, date, hour):
        """Transform data for testing."""
        return df.withColumn("transformed", df["user_id"])


class InvalidSchemaFactory(BaseSchemaFactory):
    """Invalid implementation that raises errors for testing."""
    
    def get_schema(self):
        """Raise an error for testing."""
        raise ValueError("Schema error")
    
    def transform_data(self, df, cohort_id, date, hour):
        """Transform data for testing."""
        return df


@pytest.fixture
def sample_dataframe(spark_session):
    """Sample DataFrame for testing."""
    data = [
        {"user_id": "user1", "account_id": 1001, "risk_rating": "low"},
        {"user_id": "user2", "account_id": 1002, "risk_rating": "medium"},
        {"user_id": "user3", "account_id": 1003, "risk_rating": "high"}
    ]
    
    schema = StructType([
        StructField("user_id", StringType(), True),
        StructField("account_id", IntegerType(), True),
        StructField("risk_rating", StringType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def incomplete_dataframe(spark_session):
    """DataFrame missing required columns for testing."""
    data = [
        {"user_id": "user1", "account_id": 1001},
        {"user_id": "user2", "account_id": 1002}
    ]
    
    schema = StructType([
        StructField("user_id", StringType(), True),
        StructField("account_id", IntegerType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)


class TestBaseSchemaFactory:
    """Test cases for BaseSchemaFactory."""

    def test_abstract_class_cannot_be_instantiated(self):
        """Test that BaseSchemaFactory cannot be instantiated directly."""
        with pytest.raises(TypeError):
            BaseSchemaFactory()

    def test_concrete_implementation_initialization_default_logger(self):
        """Test concrete implementation initialization with default logger."""
        with patch('src.jobs.cohorting.schema_factories.base_schema_factory.get_logger') as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger
            
            factory = ConcreteSchemaFactory()
            
            assert factory.logger == mock_logger
            mock_get_logger.assert_called_once()

    def test_concrete_implementation_initialization_custom_logger(self):
        """Test concrete implementation initialization with custom logger."""
        mock_logger = Mock()
        factory = ConcreteSchemaFactory(logger=mock_logger)
        
        assert factory.logger == mock_logger

    def test_get_schema_abstract_method(self):
        """Test that get_schema is properly implemented in concrete class."""
        factory = ConcreteSchemaFactory()
        schema = factory.get_schema()
        
        expected_schema = {
            "user_id": "string",
            "account_id": "long",
            "risk_rating": "string"
        }
        assert schema == expected_schema

    def test_transform_data_abstract_method(self, sample_dataframe):
        """Test that transform_data is properly implemented in concrete class."""
        factory = ConcreteSchemaFactory()
        
        result = factory.transform_data(sample_dataframe, "test_cohort", "2025-10-14", "12")
        
        # Verify transformation was applied
        assert "transformed" in result.columns
        assert result.count() == 3

    def test_validate_schema_success(self, sample_dataframe):
        """Test successful schema validation."""
        mock_logger = Mock()
        factory = ConcreteSchemaFactory(logger=mock_logger)
        
        is_valid = factory.validate_schema(sample_dataframe)
        
        assert is_valid is True
        mock_logger.info.assert_called_with("Schema validation passed")

    def test_validate_schema_missing_columns(self, incomplete_dataframe):
        """Test schema validation with missing columns."""
        mock_logger = Mock()
        factory = ConcreteSchemaFactory(logger=mock_logger)
        
        is_valid = factory.validate_schema(incomplete_dataframe)
        
        assert is_valid is False
        mock_logger.error.assert_called_once()
        error_call_args = mock_logger.error.call_args[0][0]
        assert "Missing required columns for schema" in error_call_args
        assert "risk_rating" in error_call_args

    def test_validate_schema_error_handling(self, sample_dataframe):
        """Test schema validation error handling."""
        mock_logger = Mock()
        factory = InvalidSchemaFactory(logger=mock_logger)
        
        is_valid = factory.validate_schema(sample_dataframe)
        
        assert is_valid is False
        mock_logger.error.assert_called_once()
        error_call_args = mock_logger.error.call_args[0][0]
        assert "Error validating schema" in error_call_args

    def test_validate_schema_all_columns_present(self, sample_dataframe):
        """Test schema validation when all required columns are present."""
        mock_logger = Mock()
        factory = ConcreteSchemaFactory(logger=mock_logger)
        
        # Add extra column to DataFrame
        df_with_extra = sample_dataframe.withColumn("extra_column", sample_dataframe["user_id"])
        
        is_valid = factory.validate_schema(df_with_extra)
        
        # Should still be valid as all required columns are present
        assert is_valid is True
        mock_logger.info.assert_called_with("Schema validation passed")

    def test_validate_schema_empty_schema(self, sample_dataframe):
        """Test schema validation with empty schema."""
        class EmptySchemaFactory(BaseSchemaFactory):
            def get_schema(self):
                return {}
            
            def transform_data(self, df, cohort_id, date, hour):
                return df
        
        mock_logger = Mock()
        factory = EmptySchemaFactory(logger=mock_logger)
        
        is_valid = factory.validate_schema(sample_dataframe)
        
        # Should be valid as no columns are required
        assert is_valid is True
        mock_logger.info.assert_called_with("Schema validation passed")

    def test_inheritance_structure(self):
        """Test that BaseSchemaFactory properly inherits from ABC."""
        assert issubclass(BaseSchemaFactory, ABC)
        
        # Verify abstract methods are defined
        abstract_methods = BaseSchemaFactory.__abstractmethods__
        assert "get_schema" in abstract_methods
        assert "transform_data" in abstract_methods

    def test_concrete_factory_method_signatures(self, sample_dataframe):
        """Test that concrete factory implements methods with correct signatures."""
        factory = ConcreteSchemaFactory()
        
        # Test get_schema signature (no parameters)
        schema = factory.get_schema()
        assert isinstance(schema, dict)
        
        # Test transform_data signature (df, cohort_id, date, hour)
        result = factory.transform_data(sample_dataframe, "cohort1", "2025-10-14", "12")
        assert result is not None
        
        # Test validate_schema signature (df)
        is_valid = factory.validate_schema(sample_dataframe)
        assert isinstance(is_valid, bool)

    def test_logger_usage_in_validation(self, sample_dataframe):
        """Test that logger is properly used in validation methods."""
        mock_logger = Mock()
        factory = ConcreteSchemaFactory(logger=mock_logger)
        
        # Test successful validation logging
        factory.validate_schema(sample_dataframe)
        mock_logger.info.assert_called_with("Schema validation passed")
        
        # Reset mock
        mock_logger.reset_mock()
        
        # Test error logging with invalid schema factory
        invalid_factory = InvalidSchemaFactory(logger=mock_logger)
        invalid_factory.validate_schema(sample_dataframe)
        mock_logger.error.assert_called_once()
