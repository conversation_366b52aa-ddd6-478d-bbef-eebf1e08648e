import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestStakingRequestsProcessingIntegration:
    """Integration tests for StakingRequestsProcessing functionality."""

    def _setup_mocks(self, mock_date_utils, mock_asset_utils, mock_operations, mock_io_utils, mock_spark_utils, mock_logger, spark_session):
        """Helper to setup common mocks for integration tests."""
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 10, 0, 0)
        mock_io_utils.return_value.write_data_in_kafka = Mock()
        return mock_io_utils.return_value, mock_asset_utils.return_value, mock_operations.return_value

    def _convert_snake_to_camel(self, df):
        """Helper to convert snake_case to camelCase - no longer used, kept for compatibility."""
        # No conversion needed - column names remain in snake_case
        return df

    @patch('src.jobs.crypto_staking.staking_requests_processing.get_logger')
    @patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.Operations')
    @patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils')
    def test_full_processing_workflow(self, mock_date_utils, mock_asset_utils, mock_operations,
                                     mock_io_utils, mock_spark_utils, mock_logger,
                                     mock_crypto_staking_config, spark_session,
                                     sample_staking_requests_data, sample_crypto_assets_data):
        """Test the complete processing workflow integration."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        io_utils, asset_utils, ops = self._setup_mocks(mock_date_utils, mock_asset_utils, mock_operations, mock_io_utils, mock_spark_utils, mock_logger, spark_session)
        io_utils.read_parquet_data.return_value = sample_staking_requests_data
        asset_utils.get_crypto_assets.return_value = sample_crypto_assets_data
        ops.check_non_null_values.return_value = []
        
        processor = StakingRequestsProcessing(mock_crypto_staking_config)
        processor.io_utils = io_utils
        processor.asset_utils = asset_utils
        processor.ops = ops
        
        processor.start_processing()
        
        io_utils.read_parquet_data.assert_called_once()
        assert io_utils.write_data_in_kafka.call_count == 2
        asset_utils.get_crypto_assets.assert_called()

    @patch('src.jobs.crypto_staking.staking_requests_processing.get_logger')
    @patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.Operations')
    @patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils')
    def test_end_to_end_data_flow(self, mock_date_utils, mock_asset_utils, mock_operations,
                                 mock_io_utils, mock_spark_utils, mock_logger,
                                 mock_crypto_staking_config, spark_session):
        """Test end-to-end data flow with realistic data."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        # Create comprehensive test data
        staking_requests_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, "STAKING_REQUESTED", "ACTIVE", 
             datetime(2025, 1, 14, 10, 0, 0), datetime(2025, 1, 14, 10, 0, 0),
             datetime(2025, 1, 15, 10, 0, 0)),
            (2, 1002, 102, 1, 1, 826, 200.0, "UNSTAKING_IN_WAITING", "ACTIVE",
             datetime(2025, 1, 14, 15, 0, 0), datetime(2025, 1, 14, 15, 0, 0),
             datetime(2025, 1, 15, 15, 0, 0)),
            (3, 1003, 103, 1, 1, 825, 50.0, "STAKED", "ACTIVE",
             datetime(2025, 1, 13, 12, 0, 0), datetime(2025, 1, 13, 12, 0, 0), None),
            (4, 1004, 104, 1, 1, 827, 75.0, "STAKING_IN_PROGRESS", "ACTIVE",
             datetime(2025, 1, 14, 8, 0, 0), datetime(2025, 1, 14, 8, 0, 0),
             datetime(2025, 1, 16, 8, 0, 0))
        ]
        
        requests_schema = StructType([
            StructField("id", LongType(), True), StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True), StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True), StructField("crypto_currency_id", LongType(), True),
            StructField("quantity", DoubleType(), True), StructField("system_status", StringType(), True),
            StructField("user_status", StringType(), True), StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True), StructField("next_transition_time", TimestampType(), True)
        ])
        
        staking_requests_df = spark_session.createDataFrame(staking_requests_data, requests_schema)
        crypto_assets_data = [(825, "BTC"), (826, "ETH"), (827, "ADA")]
        crypto_schema = StructType([StructField("id", LongType(), True), StructField("symbol", StringType(), True)])
        crypto_assets_df = spark_session.createDataFrame(crypto_assets_data, crypto_schema)
        
        io_utils, asset_utils, ops = self._setup_mocks(mock_date_utils, mock_asset_utils, mock_operations, mock_io_utils, mock_spark_utils, mock_logger, spark_session)
        io_utils.read_parquet_data.return_value = staking_requests_df
        asset_utils.get_crypto_assets.return_value = crypto_assets_df
        ops.check_non_null_values.return_value = []
        
        processor = StakingRequestsProcessing(mock_crypto_staking_config)
        processor.io_utils = io_utils
        processor.asset_utils = asset_utils
        processor.ops = ops
        
        processor.start_processing()
        
        kafka_calls = io_utils.write_data_in_kafka.call_args_list
        assert len(kafka_calls) == 2
        assert kafka_calls[0][0][1] == "staking_user_transactions"
        assert kafka_calls[1][0][1] == "crypto_net_staking_requests"

    def test_multiple_days_data_processing(self, spark_session):
        """Test processing multiple days of data with correct time filtering."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        multi_day_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, "STAKING_REQUESTED", "ACTIVE",
             datetime(2025, 1, 13, 8, 0, 0), datetime(2025, 1, 13, 8, 0, 0), datetime(2025, 1, 16, 8, 0, 0)),
            (2, 1002, 102, 1, 1, 826, 200.0, "STAKING_REQUESTED", "ACTIVE",
             datetime(2025, 1, 14, 10, 0, 0), datetime(2025, 1, 14, 10, 0, 0), datetime(2025, 1, 15, 10, 0, 0)),
            (3, 1003, 103, 1, 1, 827, 150.0, "UNSTAKING_IN_WAITING", "ACTIVE",
             datetime(2025, 1, 15, 8, 0, 0), datetime(2025, 1, 15, 8, 0, 0), datetime(2025, 1, 15, 15, 0, 0)),
            (4, 1004, 104, 1, 1, 828, 75.0, "STAKING_REQUESTED", "ACTIVE",
             datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 16, 10, 0, 0))
        ]
        
        schema = StructType([
            StructField("id", LongType(), True), StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True), StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True), StructField("crypto_currency_id", LongType(), True),
            StructField("quantity", DoubleType(), True), StructField("system_status", StringType(), True),
            StructField("user_status", StringType(), True), StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True), StructField("next_transition_time", TimestampType(), True)
        ])
        
        staking_requests_df = spark_session.createDataFrame(multi_day_data, schema)
        crypto_assets_data = [(825, "BTC"), (826, "ETH"), (827, "ADA"), (828, "DOT")]
        crypto_schema = StructType([StructField("crypto_currency_id", LongType(), True), StructField("symbol", StringType(), True)])
        crypto_assets_df = spark_session.createDataFrame(crypto_assets_data, crypto_schema)
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        processor.logger = Mock()
        processor.get_crypto_codes.return_value = crypto_assets_df
        
        with patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils') as mock_date_utils:
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 14).date()
            pending_requests = StakingRequestsProcessing.get_pending_requests_for_today(processor, staking_requests_df)
            pending_data = pending_requests.collect()
            
            assert len(pending_data) == 2
            transaction_ids = [row['transaction_id'] for row in pending_data]
            assert all(tid in transaction_ids for tid in [2, 3])

    def test_error_handling_and_validation_integration(self, spark_session):
        """Test error handling and validation in integration scenario."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        problematic_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, "STAKING_REQUESTED", "ACTIVE", 
             datetime(2025, 1, 14, 10, 0, 0), datetime(2025, 1, 14, 10, 0, 0),
             datetime(2025, 1, 15, 10, 0, 0)),
            (2, 1002, 102, 1, 1, 826, 200.0, "STAKED", "ACTIVE",
             datetime(2025, 1, 14, 15, 0, 0), datetime(2025, 1, 14, 15, 0, 0),
             datetime(2025, 1, 15, 15, 0, 0)),
            (3, 1003, 103, 1, 1, 827, 150.0, "STAKING_IN_PROGRESS", "ACTIVE",
             datetime(2025, 1, 14, 8, 0, 0), datetime(2025, 1, 14, 8, 0, 0),
             datetime(2025, 1, 14, 8, 0, 0))
        ]
        
        schema = StructType([
            StructField("id", LongType(), True), StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True), StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True), StructField("crypto_currency_id", LongType(), True),
            StructField("quantity", DoubleType(), True), StructField("system_status", StringType(), True),
            StructField("user_status", StringType(), True), StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True), StructField("next_transition_time", TimestampType(), True)
        ])
        
        problematic_df = spark_session.createDataFrame(problematic_data, schema)
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        processor.logger = Mock()
        processor.ops = Mock()
        processor.ops.check_non_null_values.return_value = [2]
        
        StakingRequestsProcessing.validate_requests_snapshot_data(processor, problematic_df)
        
        assert processor.logger.error.call_count >= 2
        processor.ops.check_non_null_values.assert_called_once()

    def test_kafka_event_structure_integration(self, spark_session):
        """Test the complete Kafka event structure in integration scenario."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        pending_data = [(1, "STAKING_REQUESTED", "ACTIVE"), (2, "UNSTAKING_IN_WAITING", "ACTIVE")]
        pending_schema = StructType([StructField("transaction_id", LongType(), True), StructField("system_status", StringType(), True), StructField("user_status", StringType(), True)])
        pending_requests_df = spark_session.createDataFrame(pending_data, pending_schema)
        
        net_staking_data = [(825, 100.0, "STAKING", "BTC", "825_BTC_20250115"), (826, -50.0, "UNSTAKING", "ETH", "826_ETH_20250115")]
        net_staking_schema = StructType([StructField("crypto_currency_id", LongType(), True), StructField("quantity", DoubleType(), True), StructField("transaction_type", StringType(), True), StructField("symbol", StringType(), True), StructField("ref_id", StringType(), True)])
        net_staking_df = spark_session.createDataFrame(net_staking_data, net_staking_schema)
        
        processor = Mock()
        processor.config = {"crypto_staking_topic_actor_prop": "test_actor"}
        processor.ops = Mock()
        
        with patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils') as mock_date_utils:
            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 10, 0, 0)
            
            user_events = StakingRequestsProcessing.create_user_transaction_updates_kafka_events(processor, pending_requests_df)
            net_events = StakingRequestsProcessing.create_net_staking_amount_kafka_events(processor, net_staking_df)
            
            for events in [user_events, net_events]:
                assert all(col in events.columns for col in ["key", "value", "headers"])
                assert events.count() > 0
