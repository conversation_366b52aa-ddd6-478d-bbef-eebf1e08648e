"""
Unit tests for SQLQueryEngine.
"""

import unittest
from unittest.mock import Mock, patch
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType
from src.jobs.cohorting.engine.sql_query_engine import SQLQueryEngine


class TestSQLQueryEngine(unittest.TestCase):
    """Test cases for SQLQueryEngine."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_spark = Mock()
        self.mock_logger = Mock()

        # Create SQLQueryEngine instance
        self.engine = SQLQueryEngine(self.mock_spark, self.mock_logger)

        # Mock available columns
        self.available_columns = ["account_id", "user_id", "asset_symbol", "event_timestamp", "asset_type", "quantity"]


    def test_execute_sql_with_config_filter(self):
        """Test SQL execution with config-based filtering."""
        mock_df = Mock()
        mock_filtered_df = Mock()
        mock_filtered_df.count.return_value = 5

        config = {
            "where_condition": "event_timestamp >= TIMESTAMP('{t_1} 00:00:00') - INTERVAL 24 HOURS"
        }
        variable_substitutions = {"{t_1}": "2024-01-01"}

        with patch.object(self.engine, '_execute_config_query', return_value=mock_filtered_df) as mock_execute:
            result = self.engine.execute_sql(mock_df, config, variable_substitutions)

            mock_execute.assert_called_once()
            self.assertEqual(result, mock_filtered_df)

    def test_execute_sql_empty_config(self):
        """Test SQL execution with empty config."""
        mock_df = Mock()

        result = self.engine.execute_sql(mock_df, {}, {})

        # Should call _execute_config_query
        self.assertIsInstance(result, Mock)


    def test_join_parameter_dataframes(self):
        """Test joining parameter DataFrames."""
        mock_df1 = Mock()
        mock_df2 = Mock()
        mock_joined_df = Mock()
        mock_joined_df.count.return_value = 10

        parameter_dfs = {
            "param1": mock_df1,
            "param2": mock_df2
        }

        join_configs = [
            {"parameter_id": "param1", "join_keys": ["account_id"]},
            {"parameter_id": "param2", "join_type": "left", "join_keys": ["account_id"]}
        ]

        # Mock the DataFrame join method
        mock_df1.join.return_value = mock_joined_df

        with patch.object(self.engine, 'join_parameter_dataframes') as mock_join:
            mock_join.return_value = mock_joined_df

            result = self.engine.join_parameter_dataframes(parameter_dfs, join_configs)

            self.assertEqual(result, mock_joined_df)

    def test_validate_sql_valid(self):
        """Test SQL validation for valid SQL."""
        valid_sql = "SELECT * FROM table WHERE column = 'value'"

        is_valid = self.engine.validate_sql(valid_sql)

        self.assertTrue(is_valid)

    def test_validate_sql_invalid(self):
        """Test SQL validation for invalid SQL."""
        invalid_sql = "SELECT * FROM table WHERE column = 'value"  # Unmatched quote

        is_valid = self.engine.validate_sql(invalid_sql)

        self.assertFalse(is_valid)



if __name__ == '__main__':
    unittest.main()
