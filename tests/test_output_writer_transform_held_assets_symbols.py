"""
Unit tests for CohortOutputWriter._transform_held_assets_symbols.
"""

import pytest
from unittest.mock import Mock
from pyspark.sql.types import StructType, StructField, StringType, LongType

from src.jobs.cohorting.writers.cohort_output_writer import CohortOutputWriter


@pytest.fixture
def mock_config_small_limit():
    # Use a very small limit to force segmentation in test
    return {
        "cohorting": {
            "clevertap_max_char_limit": 10
        }
    }


@pytest.fixture
def input_positions_df(spark_session):
    # Build positions that will require multiple segments when joined with commas
    data = [
        {"account_id": 1, "user_id": 10, "asset_type": "global_stocks", "asset_symbol": "AAPL"},
        {"account_id": 1, "user_id": 10, "asset_type": "global_stocks", "asset_symbol": "MSFT"},
        {"account_id": 1, "user_id": 10, "asset_type": "global_stocks", "asset_symbol": "NVDA"},
        {"account_id": 1, "user_id": 10, "asset_type": "crypto_currency", "asset_symbol": "BTC"},
        {"account_id": 1, "user_id": 10, "asset_type": "crypto_currency", "asset_symbol": "ETH"},
        {"account_id": 1, "user_id": 10, "asset_type": "crypto_currency", "asset_symbol": "SOL"},
    ]
    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("asset_type", StringType(), True),
        StructField("asset_symbol", StringType(), True),
    ])
    return spark_session.createDataFrame(data, schema)


def test_transform_segments_respect_limit(spark_session, mock_config_small_limit, input_positions_df):
    logger = Mock()
    writer = CohortOutputWriter(spark_session, mock_config_small_limit, logger)

    out = writer._transform_held_assets_cohort_data(input_positions_df, max_segments=4)
    cols = out.columns
    # Validate required columns exist
    for c in [
        "user_id", "account_id",
        "global_stocks_held_asset_symbols_1",
        "global_stocks_held_asset_symbols_2",
        "crypto_currency_held_asset_symbols_1",
        "crypto_currency_held_asset_symbols_2",
    ]:
        assert c in cols

    rows = out.collect()
    assert len(rows) == 1
    row = rows[0]

    # Ensure no segment exceeds the configured limit
    limit = mock_config_small_limit["cohorting"]["clevertap_max_char_limit"]
    for c in cols:
        val = getattr(row, c)
        if isinstance(val, str):
            assert len(val) <= limit

    # Validate that combined tokens are covered across segments (order-insensitive)
    gss_parts = [getattr(row, f"global_stocks_held_asset_symbols_{i}") for i in range(1, 5)]
    gss_tokens = set()
    for p in gss_parts:
        if p:
            gss_tokens.update(p.split(","))
    assert gss_tokens == {"AAPL", "MSFT", "NVDA"}

    crypto_parts = [getattr(row, f"crypto_currency_held_asset_symbols_{i}") for i in range(1, 5)]
    crypto_tokens = set()
    for p in crypto_parts:
        if p:
            crypto_tokens.update(p.split(","))
    assert crypto_tokens == {"BTC", "ETH", "SOL"}


