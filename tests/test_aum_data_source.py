"""
Unit tests for AUMDataSource.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType
from src.jobs.cohorting.data_sources.aum_data_source import AUMDataSource
from tests.helpers.cohorting_test_helpers import (
    validate_positive_numbers, create_test_schema, assert_basic_dataframe_properties
)


class TestAUMDataSource:
    """Test cases for AUMDataSource."""

    @pytest.fixture
    def mock_config(self):
        """Mock configuration."""
        return {
            "bucket_path": "s3a://test-bucket",
            "t_1": "2025-10-15",
            "t_2": "2025-10-16"
        }

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_initialization(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test AUMDataSource initialization."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        mock_ops_instance = Mock()
        mock_operations.return_value = mock_ops_instance
        
        mock_asset_utils_instance = Mock()
        mock_asset_utils.return_value = mock_asset_utils_instance
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        
        assert data_source.spark == spark_session
        assert data_source.config == mock_config
        assert data_source.logger == mock_logger
        assert data_source.name == "AUMDataSource"
        assert data_source.asset_utils == mock_asset_utils_instance

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_read_success_single_date(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test successful read with single partition date."""
        mock_logger = Mock()
        
        # Create test DataFrame with realistic AUM data
        test_data = [
            ("ACC001", "USER001", "TXN001", "AAPL", 100.0),
            ("ACC002", "USER002", "TXN002", "GOOGL", 250.5),
            ("ACC003", "USER001", "TXN003", "MSFT", 75.25)
        ]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("transaction_id", StringType(), True),
            StructField("asset_symbol", StringType(), True),
            StructField("executed_total_price", DoubleType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        
        # Mock the private methods
        with patch.object(data_source, '_get_partition_dates', return_value=["2025-10-15"]), \
             patch.object(data_source, '_read_and_union_partitions', return_value=test_df), \
             patch.object(data_source, '_enforce_required_columns', return_value=test_df):
            
            params = {"partition_date": "2025-10-15", "assets": ["gss"]}
            required_columns = ["account_id", "user_id"]
            
            result = data_source.read(params, required_columns)

            # Validate result structure and data content
            assert result.count() == 3
            result_data = result.collect()

            # Validate data integrity
            account_ids = [row["account_id"] for row in result_data]
            user_ids = [row["user_id"] for row in result_data]
            asset_symbols = [row["asset_symbol"] for row in result_data]
            prices = [row["executed_total_price"] for row in result_data]

            # Verify expected accounts are present
            expected_accounts = {"ACC001", "ACC002", "ACC003"}
            actual_accounts = set(account_ids)
            assert actual_accounts == expected_accounts

            # Validate data using helpers
            validate_positive_numbers(result_data, ["executed_total_price"])

            # Verify asset symbols and specific mappings
            asset_symbols = {row["asset_symbol"] for row in result_data}
            assert asset_symbols == {"AAPL", "GOOGL", "MSFT"}

            # Verify specific account-user-asset mappings
            acc001_row = next(row for row in result_data if row["account_id"] == "ACC001")
            assert acc001_row["user_id"] == "USER001" and acc001_row["asset_symbol"] == "AAPL"

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_read_no_partition_dates(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test read with no partition dates."""
        mock_logger = Mock()
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        
        # Mock _get_partition_dates to return empty list
        with patch.object(data_source, '_get_partition_dates', return_value=[]), \
             patch.object(data_source, '_create_empty_dataframe') as mock_create_empty:
            
            mock_empty_df = Mock()
            mock_create_empty.return_value = mock_empty_df
            
            params = {"assets": ["gss"]}
            required_columns = ["account_id", "user_id"]
            
            result = data_source.read(params, required_columns)
            
            assert result == mock_empty_df
            mock_logger.warning.assert_called_with("No partition_date or date_range specified; returning empty dataframe")

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_read_empty_result(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test read with empty result from partitions."""
        mock_logger = Mock()
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        
        # Mock empty DataFrame
        empty_df = spark_session.createDataFrame([], StructType([]))
        
        with patch.object(data_source, '_get_partition_dates', return_value=["2025-10-15"]), \
             patch.object(data_source, '_read_and_union_partitions', return_value=empty_df), \
             patch.object(data_source, '_create_empty_dataframe') as mock_create_empty:
            
            mock_empty_df = Mock()
            mock_create_empty.return_value = mock_empty_df
            
            params = {"partition_date": "2025-10-15", "assets": ["gss"]}
            required_columns = ["account_id", "user_id"]
            
            result = data_source.read(params, required_columns)
            
            assert result == mock_empty_df

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_read_and_union_partitions(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test reading and unioning multiple partitions."""
        mock_logger = Mock()
        
        # Create test DataFrames
        test_data1 = [("ACC001", "USER001")]
        test_data2 = [("ACC002", "USER002")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        test_df1 = spark_session.createDataFrame(test_data1, test_schema)
        test_df2 = spark_session.createDataFrame(test_data2, test_schema)
        
        mock_ops_instance = Mock()
        mock_ops_instance.get_union.side_effect = [test_df1, test_df1.union(test_df2)]
        mock_operations.return_value = mock_ops_instance
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        
        with patch.object(data_source, '_get_asset_data', side_effect=[test_df1, test_df2]):
            result = data_source._read_and_union_partitions(["2025-10-15", "2025-10-16"], ["gss"])
            
            assert result.count() == 2
            mock_ops_instance.get_union.assert_called()

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_get_crypto_assets(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test getting crypto assets."""
        mock_logger = Mock()
        
        # Create mock crypto assets DataFrame
        crypto_data = [("BTC", "bitcoin"), ("ETH", "ethereum")]
        crypto_schema = StructType([
            StructField("symbol", StringType(), True),
            StructField("id", StringType(), True)
        ])
        crypto_df = spark_session.createDataFrame(crypto_data, crypto_schema)
        
        mock_asset_utils_instance = Mock()
        mock_asset_utils_instance.get_crypto_assets.return_value = crypto_df
        mock_asset_utils.return_value = mock_asset_utils_instance
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        result = data_source._get_crypto_assets()
        
        # Should have renamed columns
        assert "crypto_currency_id" in result.columns
        assert "asset_symbol" in result.columns
        assert result.count() == 2

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_get_global_stock_assets(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test getting global stock assets."""
        mock_logger = Mock()
        
        # Create mock global stock assets DataFrame
        gss_data = [("AAPL", "apple"), ("GOOGL", "google")]
        gss_schema = StructType([
            StructField("pluang_company_code", StringType(), True),
            StructField("id", StringType(), True)
        ])
        gss_df = spark_session.createDataFrame(gss_data, gss_schema)
        
        mock_asset_utils_instance = Mock()
        mock_asset_utils_instance.get_global_stock_assets.return_value = gss_df
        mock_asset_utils.return_value = mock_asset_utils_instance
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        result = data_source._get_global_stock_assets()
        
        # Should have renamed columns
        assert "global_stock_id" in result.columns
        assert "asset_symbol" in result.columns
        assert result.count() == 2

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_get_asset_data_gss_only(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test getting asset data for GSS only."""
        mock_logger = Mock()

        # Create mock GSS transaction data with all required columns
        from datetime import datetime
        gss_txn_data = [("ACC001", "USER001", "TXN001", "AAPL_ID", datetime.now(), datetime.now(),
                        10.0, 100.0, 1000.0, 5.0, "BUY", "SUCCESS", 15000.0, datetime.now())]
        gss_txn_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("id", StringType(), True),
            StructField("global_stock_id", StringType(), True),
            StructField("created", StringType(), True),
            StructField("updated", StringType(), True),
            StructField("executed_quantity", DoubleType(), True),
            StructField("executed_unit_price", DoubleType(), True),
            StructField("executed_total_price", DoubleType(), True),
            StructField("transaction_fee", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("usd_to_idr", DoubleType(), True),
            StructField("transaction_time", StringType(), True)
        ])
        gss_txn_df = spark_session.createDataFrame(gss_txn_data, gss_txn_schema)

        # Create mock GSS assets data
        gss_assets_data = [("AAPL_ID", "AAPL")]
        gss_assets_schema = StructType([
            StructField("global_stock_id", StringType(), True),
            StructField("asset_symbol", StringType(), True)
        ])
        gss_assets_df = spark_session.createDataFrame(gss_assets_data, gss_assets_schema)

        mock_io_instance = Mock()
        mock_io_instance.read_parquet_data.return_value = gss_txn_df
        mock_io_utils.return_value = mock_io_instance

        data_source = AUMDataSource(spark_session, mock_config, mock_logger)

        with patch.object(data_source, '_get_global_stock_assets', return_value=gss_assets_df):
            result = data_source._get_asset_data("2025-10-15", ["gss"])

            assert result is not None
            assert result.count() == 1
            # Should have joined with assets and dropped global_stock_id
            assert "global_stock_id" not in result.columns
            assert "asset_symbol" in result.columns

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_enforce_required_columns(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test enforcing required columns."""
        mock_logger = Mock()
        
        # Create test DataFrame with some columns
        test_data = [("ACC001", "USER001", "extra_data")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("extra_column", StringType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        
        # Test with required columns that exist and don't exist
        required_columns = ["account_id", "user_id", "missing_column"]
        result = data_source._enforce_required_columns(test_df, required_columns)
        
        # Should have all required columns
        assert set(result.columns) == set(required_columns)
        assert result.count() == 1
        
        # Missing column should be null
        row = result.collect()[0]
        assert row["account_id"] == "ACC001"
        assert row["user_id"] == "USER001"
        assert row["missing_column"] is None

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_enforce_required_columns_empty_list(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test enforcing required columns with empty list."""
        mock_logger = Mock()
        
        # Create test DataFrame
        test_data = [("ACC001", "USER001")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        
        # Test with empty required columns
        result = data_source._enforce_required_columns(test_df, [])
        
        # Should return original DataFrame unchanged
        assert result.columns == test_df.columns
        assert result.count() == test_df.count()

    @patch('src.jobs.cohorting.data_sources.aum_data_source.AssetUtils')
    @patch('src.jobs.cohorting.data_sources.base_data_source.Operations')
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    def test_read_error_handling(self, mock_io_utils, mock_operations, mock_asset_utils, mock_config, spark_session):
        """Test error handling in read method."""
        mock_logger = Mock()
        
        data_source = AUMDataSource(spark_session, mock_config, mock_logger)
        
        # Mock _get_partition_dates to raise an exception
        with patch.object(data_source, '_get_partition_dates', side_effect=Exception("Test error")), \
             patch.object(data_source, '_create_empty_dataframe') as mock_create_empty:
            
            mock_empty_df = Mock()
            mock_create_empty.return_value = mock_empty_df
            
            params = {"partition_date": "2025-10-15", "assets": ["gss"]}
            required_columns = ["account_id", "user_id"]
            
            result = data_source.read(params, required_columns)
            
            assert result == mock_empty_df
            mock_logger.error.assert_called_with("Error reading AUM data: Test error")
