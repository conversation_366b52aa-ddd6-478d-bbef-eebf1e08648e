"""
Unit tests for Cohorting<PERSON>ob.
"""

import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock
from pyspark.sql.types import StructType, <PERSON>ructField, StringType, ArrayType, MapType
from src.jobs.cohorting.cohorting_job import CohortingJob, ProcessorType, PROCESSOR_REGISTRY


@pytest.fixture
def sample_cohort_config_data(spark_session):
    """Sample cohort configuration data."""
    data = [
        {
            "cohort_id": "test_cohort_1",
            "name": "Test Cohort 1",
            "description": "Test cohort for unit testing",
            "start_date": "2025-10-01",
            "end_date": "2025-12-31",
            "data_sinks": ["S3.test_sink"],
            "assimilation_schedule": "daily",
            "cohort_processor": "BaseCohortProcessor",
            "data_sources": ["AUMDataSource"],
            "parameters": [
                {"parameter_id": "test_param_1", "join_type": "inner"}
            ],
            "cohort_logic": {
                "select_clause": "account_id, user_id",
                "where_clause": "asset_type = 'crypto'",
                "group_by_columns": ["account_id"]
            }
        },
        {
            "cohort_id": "corporate_action_dividends",
            "name": "Corporate Action Dividends",
            "description": "Accounts eligible for stock dividends",
            "start_date": "2025-10-01",
            "end_date": "2025-12-31",
            "data_sinks": [],
            "assimilation_schedule": "daily",
            "cohort_processor": "CorporateActionDividendsCohortProcessor",
            "data_sources": [],
            "parameters": [],
            "cohort_logic": {}
        }
    ]
    
    schema = StructType([
        StructField("cohort_id", StringType(), True),
        StructField("name", StringType(), True),
        StructField("description", StringType(), True),
        StructField("start_date", StringType(), True),
        StructField("end_date", StringType(), True),
        StructField("data_sinks", ArrayType(StringType()), True),
        StructField("assimilation_schedule", StringType(), True),
        StructField("cohort_processor", StringType(), True),
        StructField("data_sources", ArrayType(StringType()), True),
        StructField("parameters", ArrayType(MapType(StringType(), StringType())), True),
        StructField("cohort_logic", MapType(StringType(), StringType()), True)
    ])
    
    return spark_session.createDataFrame(data, schema)


class TestCohortingJob:
    """Test cases for CohortingJob."""
    
    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    def test_initialization(self, mock_get_logger, mock_io_utils, mock_spark_utils, mock_cohorting_config):
        """Test CohortingJob initialization."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        job = CohortingJob(mock_cohorting_config)
        
        assert job.config == mock_cohorting_config
        assert job.logger == mock_logger
        assert job.spark == mock_spark
        assert job.t_1 == "2025-10-15"
        assert job.t_2 == "2025-10-14"
        assert job.bucket_path == "s3a://test-bucket"
        
        # Verify components are initialized
        assert job.parameter_registry is not None
        assert job.cohort_config_reader is not None
    
    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    @patch('src.jobs.cohorting.cohorting_job.CohortConfigReader')
    def test_get_cohort_configurations_success(self, mock_config_reader, mock_get_logger, 
                                             mock_io_utils, mock_spark_utils, 
                                             mock_cohorting_config, sample_cohort_config_data):
        """Test successful cohort configuration retrieval."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        # Create mock rows that match the expected structure
        mock_rows = []
        for i, row_data in enumerate([
            {"cohort_id": "test_cohort_1", "name": "Test Cohort 1", "start_date": "2025-10-01",
             "end_date": "2025-12-31", "assimilation_schedule": "daily", "created_at": "2025-10-01", "updated_at": "2025-10-01"},
            {"cohort_id": "corporate_action_dividends", "name": "Corporate Action Dividends", "start_date": "2025-10-01",
             "end_date": "2025-12-31", "assimilation_schedule": "daily", "created_at": "2025-10-01", "updated_at": "2025-10-01"}
        ]):
            mock_row = Mock()
            for key, value in row_data.items():
                setattr(mock_row, key, value)
                mock_row.__getitem__ = lambda self, k: getattr(self, k)
            mock_rows.append(mock_row)

        # Mock DataFrame
        mock_df = Mock()
        mock_df.count.return_value = 2
        mock_df.collect.return_value = mock_rows

        # Mock config reader
        mock_reader_instance = Mock()
        mock_reader_instance.get_active_cohorts.return_value = mock_df
        mock_reader_instance.get_all_parameters_for_cohorts.return_value = None
        mock_config_reader.return_value = mock_reader_instance

        job = CohortingJob(mock_cohorting_config)
        cohorts = job.get_cohort_configurations()

        assert len(cohorts) == 2
        assert cohorts[0]["cohort_id"] == "test_cohort_1"
        assert cohorts[1]["cohort_id"] == "corporate_action_dividends"
        
        # Verify methods were called
        mock_reader_instance.get_active_cohorts.assert_called_once_with("2025-10-15")
        mock_reader_instance.get_all_parameters_for_cohorts.assert_called_once()
    
    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    @patch('src.jobs.cohorting.cohorting_job.CohortConfigReader')
    def test_get_cohort_configurations_empty(self, mock_config_reader, mock_get_logger, 
                                           mock_io_utils, mock_spark_utils, 
                                           mock_cohorting_config, spark_session):
        """Test cohort configuration retrieval with no active cohorts."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance
        
        # Create empty DataFrame
        empty_df = spark_session.createDataFrame([], StructType([]))
        empty_df.count = Mock(return_value=0)
        
        # Mock config reader
        mock_reader_instance = Mock()
        mock_reader_instance.get_active_cohorts.return_value = empty_df
        mock_config_reader.return_value = mock_reader_instance
        
        job = CohortingJob(mock_cohorting_config)
        cohorts = job.get_cohort_configurations()
        
        assert cohorts == []
        mock_logger.warning.assert_called_with("No active cohorts found in MongoDB")
    
    def test_processor_type_enum(self):
        """Test ProcessorType enum functionality."""
        assert ProcessorType.BASE_COHORT_PROCESSOR.value == "BaseCohortProcessor"
        assert ProcessorType.CORPORATE_ACTION_DIVIDENDS.value == "CorporateActionDividendsCohortProcessor"
        
        # Test is_custom_processor method
        assert not ProcessorType.BASE_COHORT_PROCESSOR.is_custom_processor()
        assert ProcessorType.CORPORATE_ACTION_DIVIDENDS.is_custom_processor()
    
    def test_processor_registry(self):
        """Test processor registry contains expected processors."""
        assert ProcessorType.BASE_COHORT_PROCESSOR in PROCESSOR_REGISTRY
        assert ProcessorType.CORPORATE_ACTION_DIVIDENDS in PROCESSOR_REGISTRY
        
        # Verify processor classes are correctly mapped
        from src.jobs.cohorting.processors.base_cohort_processor import BaseCohortProcessor
        from src.jobs.cohorting.processors.corporate_action_dividends_processor import CorporateActionDividendsCohortProcessor
        
        assert PROCESSOR_REGISTRY[ProcessorType.BASE_COHORT_PROCESSOR] == BaseCohortProcessor
        assert PROCESSOR_REGISTRY[ProcessorType.CORPORATE_ACTION_DIVIDENDS] == CorporateActionDividendsCohortProcessor
    
    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    def test_get_processor_info_base(self, mock_get_logger, mock_io_utils, mock_spark_utils, mock_cohorting_config):
        """Test getting base processor info."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance

        job = CohortingJob(mock_cohorting_config)

        processor_class, processor_type = job._get_processor_info("BaseCohortProcessor")

        from src.jobs.cohorting.processors.base_cohort_processor import BaseCohortProcessor
        assert processor_class == BaseCohortProcessor
        assert processor_type == ProcessorType.BASE_COHORT_PROCESSOR
    
    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    def test_get_processor_info_custom(self, mock_get_logger, mock_io_utils, mock_spark_utils, mock_cohorting_config):
        """Test getting custom processor info."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance

        job = CohortingJob(mock_cohorting_config)

        processor_class, processor_type = job._get_processor_info("CorporateActionDividendsCohortProcessor")

        from src.jobs.cohorting.processors.corporate_action_dividends_processor import CorporateActionDividendsCohortProcessor
        assert processor_class == CorporateActionDividendsCohortProcessor
        assert processor_type == ProcessorType.CORPORATE_ACTION_DIVIDENDS
    
    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    def test_get_processor_info_unknown(self, mock_get_logger, mock_io_utils, mock_spark_utils, mock_cohorting_config):
        """Test getting unknown processor info defaults to base."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance

        job = CohortingJob(mock_cohorting_config)

        processor_class, processor_type = job._get_processor_info("UnknownProcessor")

        from src.jobs.cohorting.processors.base_cohort_processor import BaseCohortProcessor
        assert processor_class == BaseCohortProcessor
        assert processor_type == ProcessorType.BASE_COHORT_PROCESSOR
        mock_logger.warning.assert_called_with("Unknown processor 'UnknownProcessor', using default BaseCohortProcessor")

    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    @patch('src.jobs.cohorting.cohorting_job.CohortOutputWriter')
    def test_process_cohort_base_processor(self, mock_output_writer, mock_get_logger,
                                         mock_io_utils, mock_spark_utils,
                                         mock_cohorting_config, spark_session):
        """Test processing cohort with base processor."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance

        # Create mock result DataFrame
        result_data = [("ACC001", "USER001"), ("ACC002", "USER002")]
        result_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        result_df = spark_session.createDataFrame(result_data, result_schema)

        # Mock output writer
        mock_writer_instance = Mock()
        mock_writer_instance.write_individual_cohort_output.return_value = "s3://test-path"
        mock_writer_instance.write_cohort_to_default_location.return_value = None
        mock_output_writer.return_value = mock_writer_instance

        job = CohortingJob(mock_cohorting_config)

        # Mock processor
        with patch.object(job, '_get_processor_info') as mock_get_processor:
            mock_processor_class = Mock()
            mock_processor_instance = Mock()
            mock_processor_instance.process_cohort.return_value = result_df
            mock_processor_class.return_value = mock_processor_instance
            mock_get_processor.return_value = (mock_processor_class, ProcessorType.BASE_COHORT_PROCESSOR)

            cohort_config = {
                "cohort_id": "test_cohort",
                "cohort_processor": "BaseCohortProcessor",
                "data_sinks": ["S3.test_sink"]
            }

            result = job._process_cohort(cohort_config, "2025-10-15", "14")

            # Verify processor was called
            mock_processor_instance.process_cohort.assert_called_once_with(
                "test_cohort", cohort_config, {"parameters": []}
            )

            # Verify output writer was called
            mock_writer_instance.write_individual_cohort_output.assert_called_once()
            mock_writer_instance.write_cohort_to_default_location.assert_called_once()

            assert result == result_df

    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    @patch('src.jobs.cohorting.cohorting_job.CohortOutputWriter')
    def test_process_cohort_custom_processor(self, mock_output_writer, mock_get_logger,
                                           mock_io_utils, mock_spark_utils,
                                           mock_cohorting_config, spark_session):
        """Test processing cohort with custom processor."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance

        # Create mock result DataFrame
        result_data = [("ACC001", "AAPL"), ("ACC002", "GOOGL")]
        result_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("global_stock_id", StringType(), True)
        ])
        result_df = spark_session.createDataFrame(result_data, result_schema)

        # Mock output writer
        mock_writer_instance = Mock()
        mock_writer_instance.write_cohort_to_default_location.return_value = None
        mock_output_writer.return_value = mock_writer_instance

        job = CohortingJob(mock_cohorting_config)

        # Mock processor
        with patch.object(job, '_get_processor_info') as mock_get_processor:
            mock_processor_class = Mock()
            mock_processor_instance = Mock()
            mock_processor_instance.process_cohort.return_value = result_df  # Changed from process_custom_cohort
            mock_processor_class.return_value = mock_processor_instance
            mock_get_processor.return_value = (mock_processor_class, ProcessorType.CORPORATE_ACTION_DIVIDENDS)

            cohort_config = {
                "cohort_id": "corporate_action_dividends",
                "cohort_processor": "CorporateActionDividendsCohortProcessor",
                "data_sinks": []
            }

            result = job._process_cohort(cohort_config, "2025-10-15", "14")

            # Verify processor was called
            mock_processor_instance.process_cohort.assert_called_once_with(
                "corporate_action_dividends", cohort_config, {"parameters": []}
            )

            # Verify only default location write was called (no data sinks)
            mock_writer_instance.write_individual_cohort_output.assert_not_called()
            mock_writer_instance.write_cohort_to_default_location.assert_called_once()

            assert result == result_df

    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    @patch('src.jobs.cohorting.cohorting_job.DateUtils')
    def test_run_success(self, mock_date_utils, mock_get_logger, mock_io_utils,
                        mock_spark_utils, mock_cohorting_config):
        """Test successful job run."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance

        # Mock DateUtils
        mock_date_utils.get_jkt_timestamp.return_value.strftime.return_value = "14"

        job = CohortingJob(mock_cohorting_config)

        # Mock methods
        with patch.object(job, 'get_cohort_configurations') as mock_get_configs, \
             patch.object(job, '_process_cohort') as mock_process_cohort, \
             patch.object(job, '_generate_cohort_matrix') as mock_generate_matrix, \
             patch('src.jobs.cohorting.cohorting_job.CohortOutputWriter') as mock_output_writer:

            mock_get_configs.return_value = [
                {"cohort_id": "test_cohort", "cohort_processor": "BaseCohortProcessor"}
            ]

            # Create a proper mock DataFrame
            mock_df = Mock()
            mock_df.columns = ["account_id", "user_id"]
            mock_df.count.return_value = 10
            mock_process_cohort.return_value = mock_df

            mock_writer_instance = Mock()
            mock_output_writer.return_value = mock_writer_instance

            job.run()

            # Verify methods were called
            mock_get_configs.assert_called_once()
            mock_process_cohort.assert_called_once()
            mock_logger.info.assert_any_call("Starting cohorting job")

    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    @patch('src.jobs.cohorting.cohorting_job.DateUtils')
    def test_run_no_cohorts(self, mock_date_utils, mock_get_logger, mock_io_utils,
                           mock_spark_utils, mock_cohorting_config):
        """Test job run with no active cohorts."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance

        # Mock DateUtils
        mock_date_utils.get_jkt_timestamp.return_value.strftime.return_value = "14"

        job = CohortingJob(mock_cohorting_config)

        # Mock methods
        with patch.object(job, 'get_cohort_configurations') as mock_get_configs, \
             patch.object(job, '_process_cohort') as mock_process_cohort:

            mock_get_configs.return_value = []

            job.run()

            # Verify warning was logged and processing was skipped
            mock_logger.warning.assert_called_with("No active cohorts found, exiting gracefully")
            mock_process_cohort.assert_not_called()

    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    def test_get_cached_cohort_parameters(self, mock_get_logger, mock_io_utils,
                                        mock_spark_utils, mock_cohorting_config):
        """Test getting cached cohort parameters."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance

        job = CohortingJob(mock_cohorting_config)

        # Mock parameter registry cache
        job.parameter_registry._parameter_cache = {
            "param1": {"parameter_id": "param1", "name": "Test Param 1"},
            "param2": {"parameter_id": "param2", "name": "Test Param 2"}
        }

        cohort_config = {
            "parameters": [
                {"parameter_id": "param1", "join_type": "inner"},
                {"parameter_id": "param3", "join_type": "left"}  # Not in cache
            ]
        }

        result = job._get_cached_cohort_parameters(cohort_config)

        # Should only return cached parameters
        assert len(result["parameters"]) == 1
        assert result["parameters"][0]["parameter_id"] == "param1"

    @patch('src.jobs.cohorting.cohorting_job.SparkUtils')
    @patch('src.jobs.cohorting.cohorting_job.IOUtils')
    @patch('src.jobs.cohorting.cohorting_job.get_logger')
    def test_get_cached_cohort_parameters_empty(self, mock_get_logger, mock_io_utils,
                                              mock_spark_utils, mock_cohorting_config):
        """Test getting cached cohort parameters with no parameters."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger

        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance

        job = CohortingJob(mock_cohorting_config)

        cohort_config = {"parameters": []}

        result = job._get_cached_cohort_parameters(cohort_config)

        assert result == {"parameters": []}
