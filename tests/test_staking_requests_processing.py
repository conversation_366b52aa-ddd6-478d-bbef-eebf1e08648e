import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestStakingRequestsProcessing:
    """Test class for StakingRequestsProcessing functionality."""

    def _setup_mocks(self, mock_date_utils, mock_asset_utils, mock_operations, 
                     mock_io_utils, mock_spark_utils, mock_logger, spark_session):
        """Helper to setup common mocks."""
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
        return mock_io_utils.return_value, mock_asset_utils.return_value, mock_operations.return_value

    @patch('src.jobs.crypto_staking.staking_requests_processing.get_logger')
    @patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.Operations')
    @patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils')
    def test_init(self, mock_date_utils, mock_asset_utils, mock_operations, 
                  mock_io_utils, mock_spark_utils, mock_logger, 
                  mock_crypto_staking_config, spark_session):
        """Test StakingRequestsProcessing initialization."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        self._setup_mocks(mock_date_utils, mock_asset_utils, mock_operations, 
                         mock_io_utils, mock_spark_utils, mock_logger, spark_session)
        
        processor = StakingRequestsProcessing(mock_crypto_staking_config)
        
        # Verify configuration and utility objects
        assert processor.config == mock_crypto_staking_config
        assert processor.bucket_path == "s3a://test-bucket"
        assert processor.t_1 == "2025-01-15"
        assert processor.t_2 == "2025-01-14"
        mock_spark_utils.assert_called_once_with("staking_requests_processing")

    @patch('src.jobs.crypto_staking.staking_requests_processing.get_logger')
    @patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.Operations')
    @patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils')
    def test_get_crypto_codes(self, mock_date_utils, mock_asset_utils, mock_operations,
                             mock_io_utils, mock_spark_utils, mock_logger,
                             mock_crypto_staking_config, spark_session, sample_crypto_assets_data):
        """Test get_crypto_codes method."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        io_utils, asset_utils, ops = self._setup_mocks(mock_date_utils, mock_asset_utils, mock_operations, 
                                                      mock_io_utils, mock_spark_utils, mock_logger, spark_session)
        asset_utils.get_crypto_assets.return_value = sample_crypto_assets_data
        
        processor = StakingRequestsProcessing(mock_crypto_staking_config)
        processor.asset_utils = asset_utils
        
        result = processor.get_crypto_codes()
        
        asset_utils.get_crypto_assets.assert_called_once()
        assert result.count() == 4
        assert all(col in result.columns for col in ["id", "symbol"])

    @patch('src.jobs.crypto_staking.staking_requests_processing.get_logger')
    @patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.Operations')
    @patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils')
    @patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils')
    def test_get_requests_snapshot_data(self, mock_date_utils, mock_asset_utils, mock_operations,
                                       mock_io_utils, mock_spark_utils, mock_logger,
                                       mock_crypto_staking_config, spark_session, sample_staking_requests_data):
        """Test get_requests_snapshot_data method."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        io_utils, asset_utils, ops = self._setup_mocks(mock_date_utils, mock_asset_utils, mock_operations, 
                                                      mock_io_utils, mock_spark_utils, mock_logger, spark_session)
        io_utils.read_parquet_data.return_value = sample_staking_requests_data
        
        processor = StakingRequestsProcessing(mock_crypto_staking_config)
        processor.io_utils = io_utils
        
        result = processor.get_requests_snapshot_data()
        
        expected_path = "s3a://test-bucket/snapshots/crypto_currency_staking_transactions/dt=2025-01-15/"
        io_utils.read_parquet_data.assert_called_once_with(expected_path)
        assert result.count() == 4

    def test_get_pending_requests_for_today(self, spark_session, sample_staking_requests_data):
        """Test get_pending_requests_for_today method with actual Spark operations."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        
        result = StakingRequestsProcessing.get_pending_requests_for_today(processor, sample_staking_requests_data)
        result_data = result.collect()
        
        assert len(result_data) == 2
        transaction_ids = [row['transaction_id'] for row in result_data]
        assert all(tid in transaction_ids for tid in [2, 4])
        assert all(col in result.columns for col in ["transaction_id", "system_status", "user_status"])

    def test_validate_requests_snapshot_data_terminal_status_validation(self, spark_session):
        """Test validation of terminal status records with non-null next_transition_time."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        # Test data with terminal status and non-null next_transition_time (invalid)
        invalid_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, "STAKED", "ACTIVE", 
             datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 15, 10, 0, 0),
             datetime(2025, 1, 16, 10, 0, 0)),  # Invalid: terminal status with next_transition_time
            (2, 1002, 102, 1, 1, 826, 200.0, "UNSTAKED", "ACTIVE",
             datetime(2025, 1, 14, 15, 0, 0), datetime(2025, 1, 14, 15, 0, 0),
             None)  # Valid: terminal status with null next_transition_time
        ]
        
        schema = self._create_staking_requests_schema()
        test_data = spark_session.createDataFrame(invalid_data, schema)
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        processor.logger = Mock()
        processor.ops = Mock()
        processor.ops.check_non_null_values.return_value = [1]
        
        StakingRequestsProcessing.validate_requests_snapshot_data(processor, test_data)
        
        processor.logger.error.assert_called()
        processor.ops.check_non_null_values.assert_called_once()

    def _create_staking_requests_schema(self):
        """Helper to create staking requests schema."""
        return StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("system_status", StringType(), True),
            StructField("user_status", StringType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("next_transition_time", TimestampType(), True)
        ])

    def test_time_based_filtering_previous_day_9am_to_today_9am(self, spark_session):
        """Test that only transactions created from previous day 9AM to today 9AM are fetched."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        # Test data with various creation times
        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, "STAKING_REQUESTED", "ACTIVE",
             datetime(2025, 1, 14, 0, 0, 0), datetime(2025, 1, 14, 0, 0, 0), None),  # Before window
            (2, 1002, 102, 1, 1, 826, 200.0, "STAKING_REQUESTED", "ACTIVE",
             datetime(2025, 1, 14, 3, 0, 0), datetime(2025, 1, 14, 3, 0, 0), None),  # In window
            (3, 1003, 103, 1, 1, 827, 150.0, "UNSTAKING_IN_WAITING", "ACTIVE",
             datetime(2025, 1, 14, 8, 0, 0), datetime(2025, 1, 14, 8, 0, 0), None),  # In window
            (4, 1004, 104, 1, 1, 828, 75.0, "STAKING_REQUESTED", "ACTIVE",
             datetime(2025, 1, 13, 3, 0, 0), datetime(2025, 1, 13, 3, 0, 0), None)   # Before window
        ]
        
        staking_requests_df = spark_session.createDataFrame(test_data, self._create_staking_requests_schema())
        
        processor = Mock()
        processor.current_date = datetime(2025, 1, 15).date()
        processor.logger = Mock()
        
        crypto_codes_data = [(825, "BTC"), (826, "ETH"), (827, "ADA"), (828, "DOT")]
        crypto_schema = StructType([StructField("crypto_currency_id", LongType(), True), StructField("symbol", StringType(), True)])
        processor.get_crypto_codes.return_value = spark_session.createDataFrame(crypto_codes_data, crypto_schema)

        with patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils') as mock_date_utils:
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 14).date()
            result = StakingRequestsProcessing.get_net_staking_amount(processor, staking_requests_df)
            result_data = result.collect()
            
            crypto_ids = [row['crypto_currency_id'] for row in result_data]
            assert all(cid in crypto_ids for cid in [826, 827])  # In window
            assert 828 not in crypto_ids  # Before window
            assert len(result_data) >= 2

    def test_next_transition_time_calculation_for_duration_gte_2_days(self, spark_session):
        """Test next_transition_time calculation for transactions with duration >= 2 days."""
        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, "STAKING_IN_PROGRESS", "ACTIVE",
             datetime(2025, 1, 13, 10, 0, 0), datetime(2025, 1, 13, 10, 0, 0),
             datetime(2025, 1, 15, 10, 0, 0)),  # 2 days duration
            (2, 1002, 102, 1, 1, 826, 200.0, "UNSTAKING_IN_PROGRESS", "ACTIVE",
             datetime(2025, 1, 12, 15, 0, 0), datetime(2025, 1, 12, 15, 0, 0),
             datetime(2025, 1, 15, 15, 0, 0)),  # 3 days duration
            (3, 1003, 103, 1, 1, 827, 150.0, "STAKING_REQUESTED", "ACTIVE",
             datetime(2025, 1, 14, 12, 0, 0), datetime(2025, 1, 14, 12, 0, 0),
             datetime(2025, 1, 15, 12, 0, 0))   # 1 day duration
        ]

        staking_requests_df = spark_session.createDataFrame(test_data, self._create_staking_requests_schema())
        result = staking_requests_df.filter(F.datediff(F.col("next_transition_time"), F.col("created")) >= 2)
        result_data = result.collect()
        
        assert len(result_data) == 2
        transaction_ids = [row['id'] for row in result_data]
        assert all(tid in transaction_ids for tid in [1, 2])
        assert 3 not in transaction_ids

    def test_handling_transactions_with_terminal_status(self, spark_session):
        """Test handling of transactions with terminal status."""
        from src.enums.crypto_staking import StakingStatus

        test_data = [
            (1, 1001, 101, 1, 1, 825, 100.0, "STAKED", "ACTIVE",
             datetime(2025, 1, 13, 10, 0, 0), datetime(2025, 1, 13, 10, 0, 0), None),
            (2, 1002, 102, 1, 1, 826, 200.0, "UNSTAKED", "ACTIVE",
             datetime(2025, 1, 14, 15, 0, 0), datetime(2025, 1, 14, 15, 0, 0), None),
            (3, 1003, 103, 1, 1, 827, 150.0, "STAKING_CANCELLED", "CANCELLED",
             datetime(2025, 1, 12, 12, 0, 0), datetime(2025, 1, 12, 12, 0, 0), None),
            (4, 1004, 104, 1, 1, 828, 75.0, "STAKING_REQUESTED", "ACTIVE",
             datetime(2025, 1, 15, 8, 0, 0), datetime(2025, 1, 15, 8, 0, 0),
             datetime(2025, 1, 16, 8, 0, 0))
        ]

        staking_requests_df = spark_session.createDataFrame(test_data, self._create_staking_requests_schema())
        terminal_statuses = [s.value for s in StakingStatus.terminal_status()]
        terminal_transactions = staking_requests_df.filter(F.col("system_status").isin(terminal_statuses))
        result_data = terminal_transactions.collect()
        
        assert len(result_data) == 3
        assert all(row['next_transition_time'] is None for row in result_data)

    def test_create_user_transaction_updates_kafka_events(self, spark_session):
        """Test creation of Kafka events for user transaction updates."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing

        pending_data = [(1, "STAKING_REQUESTED", "ACTIVE"), (2, "UNSTAKING_IN_WAITING", "ACTIVE")]
        schema = StructType([StructField("transaction_id", LongType(), True), StructField("system_status", StringType(), True), StructField("user_status", StringType(), True)])
        pending_requests_df = spark_session.createDataFrame(pending_data, schema)

        processor = Mock()
        processor.config = {"crypto_staking_topic_actor_prop": "test_actor"}
        processor.ops = Mock()

        with patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils') as mock_date_utils:
            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 10, 0, 0)
            result = StakingRequestsProcessing.create_user_transaction_updates_kafka_events(processor, pending_requests_df)
            
            assert all(col in result.columns for col in ["key", "value", "headers"])
            assert len(result.collect()) == 2

    def test_create_net_staking_amount_kafka_events(self, spark_session):
        """Test creation of Kafka events for net staking amount."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing

        net_staking_data = [(825, 100.0, "STAKING", "BTC", "825_BTC_20250115"), (826, -50.0, "UNSTAKING", "ETH", "826_ETH_20250115")]
        schema = StructType([StructField("crypto_currency_id", LongType(), True), StructField("quantity", DoubleType(), True), StructField("transaction_type", StringType(), True), StructField("symbol", StringType(), True), StructField("ref_id", StringType(), True)])
        net_staking_df = spark_session.createDataFrame(net_staking_data, schema)

        processor = Mock()
        processor.config = {}
        processor.ops = Mock()

        result = StakingRequestsProcessing.create_net_staking_amount_kafka_events(processor, net_staking_df)
        
        assert all(col in result.columns for col in ["key", "value", "headers"])
        assert len(result.collect()) == 2
