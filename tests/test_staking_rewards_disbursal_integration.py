import pytest
import sys
import os
from datetime import datetime, timedelta, date
from unittest.mock import Mock, MagicMock, patch, call
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestStakingRewardsDisbursalIntegration:
    """Integration tests for StakingRewardsDisbursal functionality."""

    def _setup_mocks(self, mock_date_utils, mock_asset_utils, mock_operations, 
                     mock_io_utils, mock_spark_utils, mock_logger, spark_session):
        """Helper to setup common mocks."""
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 20).date()
        return mock_io_utils.return_value, mock_asset_utils.return_value, mock_operations.return_value

    def _create_assets_schema(self):
        """Helper to create assets schema."""
        return StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("reward_frequency", StringType(), True),
            StructField("reward_cron_schedule", StringType(), True),
            StructField("reward_disbursal_window", StringType(), True)
        ])

    def _create_rewards_schema(self):
        """Helper to create rewards schema."""
        return StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("ref_id", StringType(), True),
            StructField("eligible_staked_quantity", DoubleType(), True),
            StructField("gross_reward_quantity", DoubleType(), True),
            StructField("reward_trigger_time", TimestampType(), True),
            StructField("created", TimestampType(), True)
        ])

    def _create_history_schema(self):
        """Helper to create history schema."""
        return StructType([
            StructField("account_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("last_reward_disbursal_date", DateType(), True)
        ])

    def _convert_snake_to_camel(self, df):
        """Helper to convert snake_case to camelCase - no longer used, kept for compatibility."""
        # No conversion needed - column names remain in snake_case
        return df

    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.get_logger')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.SparkUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.IOUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.Operations')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.AssetUtils')
    @patch('src.jobs.crypto_staking.staking_rewards_disbursal.DateUtils')
    @patch('requests.get')
    def test_full_processing_workflow(self, mock_get, mock_date_utils, mock_asset_utils,
                                     mock_operations, mock_io_utils, mock_spark_utils, mock_logger,
                                     mock_crypto_staking_config, spark_session):
        """Test the complete processing workflow integration."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal
        
        # Setup mocks
        io_utils, asset_utils, ops = self._setup_mocks(mock_date_utils, mock_asset_utils, mock_operations, 
                                                       mock_io_utils, mock_spark_utils, mock_logger, spark_session)
        
        # Mock crypto price API
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"data": {"sellPrice": "50000.00"}}
        mock_get.return_value = mock_response
        
        # Create sample data
        assets_data = [(825, "WEEKLY", "0 0 * * 1", "CALENDAR"), (826, "WEEKLY", "0 0 * * 1", "CALENDAR")]
        assets_df = spark_session.createDataFrame(assets_data, self._create_assets_schema())
        
        rewards_data = [
            (1, 1001, 101, 1, 1, 825, "1001_825_20250120", 100.0, 0.5, datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 12, 0, 0)),
            (2, 1002, 102, 1, 1, 826, "1002_826_20250120", 200.0, 1.0, datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 14, 0, 0))
        ]
        rewards_df = spark_session.createDataFrame(rewards_data, self._create_rewards_schema())
        
        history_df = spark_session.createDataFrame([], self._create_history_schema())
        
        crypto_assets_data = [(825, "BTC"), (826, "ETH")]
        crypto_schema = StructType([StructField("id", LongType(), True), StructField("symbol", StringType(), True)])
        crypto_assets_df = spark_session.createDataFrame(crypto_assets_data, crypto_schema)
        
        # Mock IO operations
        def mock_read_parquet_data(path, schema=None, allow_empty=False):
            if "crypto_currency_staking_assets" in path:
                return assets_df.withColumn("reward_disbursal_frequency", F.lit('{"interval": "WEEKLY", "cron": "0 0 * * 1", "windowType": "CALENDAR"}'))
            elif "crypto_currency_staking_accrued_rewards" in path:
                return rewards_df
            elif "rewards_disbursal_history" in path:
                return spark_session.createDataFrame([], StructType([
                    StructField("account_id", LongType(), False),
                    StructField("crypto_currency_id", LongType(), False),
                    StructField("last_reward_disbursal_date", DateType(), True)
                ]))
            elif "rewards_accrual_history" in path:
                return rewards_df.select("account_id", "crypto_currency_id", "ref_id", "eligible_staked_quantity")
            else:
                return spark_session.createDataFrame([], StructType([
                    StructField("account_id", LongType(), True),
                    StructField("crypto_currency_id", LongType(), True),
                    StructField("ref_id", StringType(), True),
                    StructField("eligible_staked_quantity", DoubleType(), True)
                ]))
        
        io_utils.read_parquet_data.side_effect = mock_read_parquet_data
        io_utils.write_data_in_kafka = Mock()
        io_utils.write_parquet_file = Mock()
        
        asset_utils.get_crypto_assets.return_value = crypto_assets_df
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 20, 10, 0, 0)
        
        # Initialize processor
        processor = StakingRewardsDisbursal(mock_crypto_staking_config)
        processor.io_utils = io_utils
        processor.asset_utils = asset_utils
        processor.ops = ops
        
        # Execute full processing
        processor.start_processing()
        
        # Verify IO operations were called
        assert io_utils.read_parquet_data.call_count >= 2
        io_utils.write_data_in_kafka.assert_called_once()
        io_utils.write_parquet_file.assert_called_once()

    def test_multiple_assets_cron_types_and_disbursal_scenarios(self, spark_session):
        """Test multiple assets, cron types, and disbursal scenarios integration."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal
        
        current_date = datetime(2025, 1, 20).date()  # Monday, 20th
        
        # Test multiple assets and cron types
        assets_data = [
            (825, "WEEKLY", "0 0 * * 1", "CALENDAR"),     # BTC - Weekly Monday (due today)
            (826, "WEEKLY", "0 0 * * 2", "CALENDAR"),     # ETH - Weekly Tuesday (not due)
            (827, "MONTHLY", "0 0 20 * *", "CALENDAR"),   # ADA - Monthly 20th (due today)
            (828, "MONTHLY", "0 0 15 * *", "CALENDAR"),   # DOT - Monthly 15th (not due)
            (829, "WEEKLY", "0 0 * * 1", "ROLLING"),      # MATIC - Weekly Monday Rolling (due today)
        ]
        
        assets_df = spark_session.createDataFrame(assets_data, self._create_assets_schema())
        
        # Test first-time vs subsequent disbursal scenarios
        rewards_data = [
            # First-time disbursal (no history)
            (1, 1001, 101, 1, 1, 825, "1001_825_20250120", 100.0, 0.5, datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 12, 0, 0)),
            # Subsequent disbursal (has history)
            (2, 1002, 102, 1, 1, 826, "1002_826_20250120", 200.0, 1.0, datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 14, 0, 0)),
            # Should be excluded (created before last disbursal)
            (3, 1002, 102, 1, 1, 826, "1002_826_20250120", 50.0, 0.25, datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 12, 10, 0, 0)),
            (4, 1003, 103, 1, 1, 827, "1003_827_20250120", 150.0, 0.75, datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 16, 0, 0)),
            (5, 1004, 104, 1, 1, 828, "1004_828_20250120", 75.0, 0.375, datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 18, 0, 0)),
            (6, 1005, 105, 1, 1, 829, "1005_829_20250120", 50.0, 0.25, datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 19, 20, 0, 0)),
        ]
        
        rewards_df = spark_session.createDataFrame(rewards_data, self._create_rewards_schema())
        
        # Create disbursal history (only for account 1002)
        history_data = [(1002, 826, datetime(2025, 1, 13).date())]  # Last Monday
        history_df = spark_session.createDataFrame(history_data, self._create_history_schema())
        
        # Test cron types
        assets_with_frequency = assets_df.withColumn("reward_disbursal_frequency", F.lit('{"interval": "WEEKLY", "cron": "0 0 * * 1", "windowType": "CALENDAR"}'))
        joined_data = assets_with_frequency.join(rewards_df, on="crypto_currency_id", how="inner")
        
        with patch('src.jobs.crypto_staking.staking_rewards_disbursal.croniter') as mock_croniter:
            def mock_croniter_instance(cron_expr, now):
                mock_instance = Mock()
                if cron_expr == "0 0 * * 1":  # Weekly Monday
                    mock_instance.get_current.return_value = datetime(2025, 1, 20, 0, 0, 0)
                elif cron_expr == "0 0 * * 2":  # Weekly Tuesday  
                    mock_instance.get_current.return_value = datetime(2025, 1, 21, 0, 0, 0)
                elif cron_expr == "0 0 20 * *":  # Monthly 20th
                    mock_instance.get_current.return_value = datetime(2025, 1, 20, 0, 0, 0)
                elif cron_expr == "0 0 15 * *":  # Monthly 15th
                    mock_instance.get_current.return_value = datetime(2025, 1, 15, 0, 0, 0)
                else:
                    mock_instance.get_current.return_value = datetime(2025, 1, 20, 0, 0, 0)
                return mock_instance
            
            mock_croniter.side_effect = mock_croniter_instance
            
            processor = Mock()
            processor.current_date = current_date
            
            transformed_data = StakingRewardsDisbursal.transform_accrued_rewards_data(processor, joined_data)
        
        # Test cron filtering
        due_today = transformed_data.filter(F.col("cron_run_date") == current_date)
        due_today_data = due_today.collect()
        
        crypto_ids_due = [row['crypto_currency_id'] for row in due_today_data]
        assert 825 in crypto_ids_due  # BTC - Weekly Monday
        assert 827 in crypto_ids_due  # ADA - Monthly 20th
        assert 829 in crypto_ids_due  # MATIC - Weekly Monday Rolling
        assert 826 not in crypto_ids_due  # ETH - Weekly Tuesday
        assert 828 not in crypto_ids_due  # DOT - Monthly 15th
        
        # Test disbursal filtering
        with_history = rewards_df.join(history_df, on=["account_id", "crypto_currency_id"], how="left")
        filtered_rewards = with_history.filter(
            (F.col("last_reward_disbursal_date").isNull()) |  # First time
            (F.date_format(F.col("created"), "yyyy-MM-dd") > F.col("last_reward_disbursal_date"))  # After last disbursal
        )
        
        result_data = filtered_rewards.collect()
        
        assert len(result_data) == 5  # All records should pass the filter
        
        account_1001 = [row for row in result_data if row['account_id'] == 1001]
        assert len(account_1001) == 1
        assert account_1001[0]['last_reward_disbursal_date'] is None
        
        account_1002 = [row for row in result_data if row['account_id'] == 1002]
        assert len(account_1002) == 1
        assert account_1002[0]['created'] == datetime(2025, 1, 19, 14, 0, 0)

    @patch('requests.get')
    def test_crypto_price_integration_and_aggregation_kafka_events(self, mock_get, spark_session):
        """Test crypto price integration, aggregation, and Kafka event creation."""
        from src.jobs.crypto_staking.staking_rewards_disbursal import StakingRewardsDisbursal
        
        # Mock different prices for different symbols
        def mock_price_response(url):
            mock_response = Mock()
            mock_response.status_code = 200
            
            if "BTC" in url:
                mock_response.json.return_value = {"data": {"sellPrice": "50000.00"}}
            elif "ETH" in url:
                mock_response.json.return_value = {"data": {"sellPrice": "3000.00"}}
            elif "ADA" in url:
                mock_response.json.return_value = {"data": {"sellPrice": "0.50"}}
            else:
                mock_response.json.return_value = {"data": {"sellPrice": "1.00"}}
            
            return mock_response
        
        mock_get.side_effect = mock_price_response
        
        # Test crypto price integration
        staking_data = [
            (1001, 825, "1001_825_20250120", 100.0, 0.5),
            (1002, 826, "1002_826_20250120", 200.0, 1.0),
            (1003, 827, "1003_827_20250120", 1000.0, 5.0),
        ]
        
        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("ref_id", StringType(), True),
            StructField("eligible_staked_quantity", DoubleType(), True),
            StructField("gross_reward_quantity", DoubleType(), True)
        ])
        
        staking_df = spark_session.createDataFrame(staking_data, schema)
        
        crypto_data = [(825, "BTC"), (826, "ETH"), (827, "ADA")]
        crypto_schema = StructType([StructField("id", LongType(), True), StructField("symbol", StringType(), True)])
        crypto_df = spark_session.createDataFrame(crypto_data, crypto_schema)
        
        processor = Mock()
        processor.config = {"crypto_currency_price_api": "https://api.test.com/price"}
        processor.logger = Mock()
        processor.spark = spark_session
        
        processor.get_crypto_codes = lambda: crypto_df.withColumnRenamed("id", "crypto_currency_id")
        
        def mock_get_crypto_price_with_retries(symbol):
            if symbol == "BTC":
                return "50000.00"
            elif symbol == "ETH":
                return "3000.00"
            elif symbol == "ADA":
                return "0.50"
            else:
                return "1.00"
        
        processor.get_crypto_price_with_retries = mock_get_crypto_price_with_retries
        
        result = StakingRewardsDisbursal.get_crypto_currency_unit_price(processor, staking_df)
        result_data = result.collect()
        
        assert len(result_data) == 3
        for row in result_data:
            assert "unit_price" in row.asDict()
            assert row["unit_price"] > 0
        
        # Test aggregation and Kafka events
        rewards_data = [
            (1, 1001, 101, 1, 1, 825, 50000.0, "1001_825_20250120", 100.0, 0.5, "WEEKLY", datetime(2025, 1, 19, 12, 0, 0), datetime(2025, 1, 20, 8, 0, 0)),
            (2, 1001, 101, 1, 1, 825, 50000.0, "1001_825_20250120", 50.0, 0.25, "WEEKLY", datetime(2025, 1, 19, 14, 0, 0), datetime(2025, 1, 20, 8, 0, 0)),
            (3, 1002, 102, 1, 1, 826, 3000.0, "1002_826_20250120", 200.0, 1.0, "WEEKLY", datetime(2025, 1, 19, 16, 0, 0), datetime(2025, 1, 20, 8, 0, 0)),
        ]
        
        rewards_schema = StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("client_id", LongType(), True),
            StructField("partner_id", LongType(), True),
            StructField("crypto_currency_id", LongType(), True),
            StructField("unit_price", DoubleType(), True),
            StructField("ref_id", StringType(), True),
            StructField("eligible_staked_quantity", DoubleType(), True),
            StructField("gross_reward_quantity", DoubleType(), True),
            StructField("reward_frequency", StringType(), True),
            StructField("first_accrued_reward_timestamp", TimestampType(), True),
            StructField("last_accrued_reward_timestamp", TimestampType(), True)
        ])
        
        rewards_df = spark_session.createDataFrame(rewards_data, rewards_schema)
        
        # Execute aggregation
        aggregated_rewards = rewards_df.groupBy("account_id", "crypto_currency_id").agg(
            F.sum("gross_reward_quantity").alias("gross_reward_quantity"),
            F.min("id").alias("first_accrued_reward_id"),
            F.max("id").alias("last_accrued_reward_id"),
            F.min("first_accrued_reward_timestamp").alias("first_accrued_reward_timestamp"),
            F.max("last_accrued_reward_timestamp").alias("last_accrued_reward_timestamp"),
            F.first("user_id").alias("user_id"),
            F.first("client_id").alias("client_id"),
            F.first("partner_id").alias("partner_id"),
            F.last("reward_frequency").alias("reward_frequency"),
            F.last("unit_price").alias("unit_price")
        )
        
        current_date_formatted = datetime(2025, 1, 20).strftime("%Y%m%d")
        aggregated_with_ref = aggregated_rewards.withColumn(
            "ref_id",
            F.concat(F.col("account_id"), F.lit("_"), F.col("crypto_currency_id"), F.lit("_"), F.lit(current_date_formatted))
        )
        
        processor.config = {"crypto_staking_topic_actor_prop": "disbursed_rewards_actor"}
        processor.ops = Mock()
        
        with patch('src.jobs.crypto_staking.staking_rewards_disbursal.DateUtils') as mock_date_utils:
            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 20, 10, 0, 0)
            
            kafka_events = StakingRewardsDisbursal.create_kafka_events(processor, aggregated_with_ref)
            kafka_data = kafka_events.collect()
            
            aggregated_data = aggregated_with_ref.collect()
            assert len(aggregated_data) == 2
            
            account_1001 = [row for row in aggregated_data if row['account_id'] == 1001][0]
            assert account_1001['gross_reward_quantity'] == 0.75
            assert account_1001['first_accrued_reward_id'] == 1
            assert account_1001['last_accrued_reward_id'] == 2
            
            assert len(kafka_data) == 2
            for event in kafka_data:
                assert "key" in event.asDict()
                assert "value" in event.asDict()
                assert "headers" in event.asDict()
