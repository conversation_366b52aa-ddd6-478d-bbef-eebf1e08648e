"""
Unit tests for CorporateActionDividendsCohortProcessor.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from pyspark.sql.types import StructType, StructField, StringType, LongType, ArrayType, DateType
from src.jobs.cohorting.processors.corporate_action_dividends_processor import CorporateActionDividendsCohortProcessor
from tests.helpers.cohorting_test_helpers import (
    validate_data_integrity, validate_positive_integers,
    create_test_schema, assert_basic_dataframe_properties
)


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    return {
        "bucket_path": "s3://test-bucket",
        "t_1": "2025-10-14",
        "t_2": "2025-10-13"
    }


@pytest.fixture
def sample_dividends_data(spark_session):
    """Sample dividends data for testing."""
    data = [
        {"global_stock_id": 1001, "record_date": "2025-10-15", "updated": "2025-10-14 10:00:00"},
        {"global_stock_id": 1002, "record_date": "2025-10-16", "updated": "2025-10-13 15:30:00"},
        {"global_stock_id": 1003, "record_date": "2025-10-17", "updated": "2025-10-12 09:00:00"},  # Should be filtered out
    ]
    
    schema = StructType([
        StructField("global_stock_id", LongType(), True),
        StructField("record_date", StringType(), True),
        StructField("updated", StringType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_accounts_data(spark_session):
    """Sample accounts data for testing."""
    data = [
        {"account_id": 2001, "user_id": 3001, "global_stock_id": 1001, "quantity": 100.0},
        {"account_id": 2002, "user_id": 3002, "global_stock_id": 1001, "quantity": 50.0},
        {"account_id": 2003, "user_id": 3003, "global_stock_id": 1002, "quantity": 75.0},
    ]
    
    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("global_stock_id", LongType(), True),
        StructField("quantity", StringType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_assets_data(spark_session):
    """Sample assets data for testing."""
    data = [
        {"id": 1001, "pluang_company_code": "AAPL"},
        {"id": 1002, "pluang_company_code": "GOOGL"},
    ]
    
    schema = StructType([
        StructField("id", LongType(), True),
        StructField("pluang_company_code", StringType(), True)
    ])
    
    return spark_session.createDataFrame(data, schema)


class TestCorporateActionDividendsCohortProcessor:
    """Test cases for CorporateActionDividendsCohortProcessor."""

    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.AssetUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.DateUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.Operations')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.IOUtils')
    def test_initialization(self, mock_io_utils, mock_operations, mock_date_utils, mock_asset_utils, mock_config, spark_session):
        """Test CorporateActionDividendsCohortProcessor initialization."""
        mock_logger = Mock()
        
        # Mock the utility classes
        mock_ops_instance = Mock()
        mock_operations.return_value = mock_ops_instance
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        mock_date_instance = Mock()
        mock_date_utils.return_value = mock_date_instance
        
        mock_asset_instance = Mock()
        mock_asset_utils.return_value = mock_asset_instance
        
        processor = CorporateActionDividendsCohortProcessor(spark_session, mock_config, mock_logger)
        
        # Verify initialization
        assert processor.spark == spark_session
        assert processor.config == mock_config
        assert processor.logger == mock_logger
        assert processor.bucket_path == "s3://test-bucket"
        assert processor.t_1 == "2025-10-14"
        assert processor.t_2 == "2025-10-13"
        
        # Verify utility classes were initialized
        mock_operations.assert_called_once_with(spark_session)
        mock_io_utils.assert_called_once_with(spark_session, mock_config)
        # DateUtils is not used in the current implementation, so we don't assert it
        mock_asset_utils.assert_called_once_with(spark_session, mock_config)

    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.AssetUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.DateUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.Operations')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.IOUtils')
    def test_process_cohort_success(self, mock_io_utils, mock_operations, mock_date_utils, mock_asset_utils, 
                                   mock_config, spark_session, sample_dividends_data, sample_accounts_data, sample_assets_data):
        """Test successful cohort processing."""
        mock_logger = Mock()
        
        # Setup mocks
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        mock_io_instance.read_parquet_data.side_effect = [sample_dividends_data, sample_accounts_data]
        
        mock_asset_instance = Mock()
        mock_asset_utils.return_value = mock_asset_instance
        mock_asset_instance.get_global_stock_assets.return_value = sample_assets_data
        
        processor = CorporateActionDividendsCohortProcessor(spark_session, mock_config, mock_logger)
        
        # Mock the private methods
        with patch.object(processor, '_read_and_filter_dividends', return_value=sample_dividends_data.limit(2)), \
             patch.object(processor, '_process_accounts_by_record_date', return_value=sample_accounts_data):
            
            result = processor.process_cohort("test_cohort", {})
            
            # Verify result structure
            assert result is not None
            result_columns = result.columns
            expected_columns = ["account_id", "user_id", "Dividend asset Id list", "Dividend asset list"]
            assert all(col in result_columns for col in expected_columns)

            # Validate actual data content
            result_data = result.collect()
            assert len(result_data) > 0

            # Validate data integrity using helpers
            validate_positive_integers(result_data, ["account_id", "user_id"])

            # Validate dividend asset lists format
            for row in result_data:
                asset_id_list = row["Dividend asset Id list"]
                asset_list = row["Dividend asset list"]

                if asset_id_list:
                    asset_ids = asset_id_list.split(",")
                    assert all(id.strip().isdigit() for id in asset_ids)

                if asset_list:
                    asset_symbols = asset_list.split(",")
                    assert all(symbol.strip().isupper() for symbol in asset_symbols)

    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.AssetUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.DateUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.Operations')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.IOUtils')
    def test_process_cohort_no_dividends(self, mock_io_utils, mock_operations, mock_date_utils, mock_asset_utils, 
                                        mock_config, spark_session):
        """Test cohort processing when no dividends are found."""
        mock_logger = Mock()
        
        # Create empty dividends DataFrame
        empty_dividends = spark_session.createDataFrame([], StructType([
            StructField("global_stock_id", LongType(), True),
            StructField("record_date", StringType(), True)
        ]))
        
        processor = CorporateActionDividendsCohortProcessor(spark_session, mock_config, mock_logger)
        
        with patch.object(processor, '_read_and_filter_dividends', return_value=empty_dividends):
            result = processor.process_cohort("test_cohort", {})
            
            # Verify empty result with correct schema
            assert result is not None
            assert result.count() == 0
            expected_columns = ["account_id", "user_id", "Dividend asset Id list", "Dividend asset list"]
            assert all(col in result.columns for col in expected_columns)

    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.AssetUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.DateUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.Operations')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.IOUtils')
    def test_read_and_filter_dividends(self, mock_io_utils, mock_operations, mock_date_utils, mock_asset_utils, 
                                      mock_config, spark_session, sample_dividends_data):
        """Test reading and filtering dividends data."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        mock_io_instance.read_parquet_data.return_value = sample_dividends_data
        
        processor = CorporateActionDividendsCohortProcessor(spark_session, mock_config, mock_logger)
        
        result = processor._read_and_filter_dividends()
        
        # Verify filtering logic - should only include records with updated dates matching t_1 or t_2
        result_data = result.collect()
        assert len(result_data) == 2  # Should filter out the third record with updated = "2025-10-12"
        
        # Verify correct columns are selected
        assert set(result.columns) == {"global_stock_id", "record_date"}

    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.AssetUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.DateUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.Operations')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.IOUtils')
    def test_process_accounts_by_record_date(self, mock_io_utils, mock_operations, mock_date_utils, mock_asset_utils, 
                                           mock_config, spark_session, sample_dividends_data, sample_accounts_data):
        """Test processing accounts by record date."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        mock_io_instance.read_parquet_data.return_value = sample_accounts_data
        
        processor = CorporateActionDividendsCohortProcessor(spark_session, mock_config, mock_logger)
        
        # Use only first 2 records (filtered dividends)
        filtered_dividends = sample_dividends_data.limit(2)
        
        result = processor._process_accounts_by_record_date(filtered_dividends)
        
        # Verify result structure
        assert result is not None
        expected_columns = ["account_id", "user_id", "global_stock_id"]
        assert all(col in result.columns for col in expected_columns)

    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.AssetUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.DateUtils')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.Operations')
    @patch('src.jobs.cohorting.processors.corporate_action_dividends_processor.IOUtils')
    def test_error_handling(self, mock_io_utils, mock_operations, mock_date_utils, mock_asset_utils, 
                           mock_config, spark_session):
        """Test error handling in process_cohort."""
        mock_logger = Mock()
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        mock_io_instance.read_parquet_data.side_effect = Exception("S3 read error")
        
        processor = CorporateActionDividendsCohortProcessor(spark_session, mock_config, mock_logger)
        
        with pytest.raises(Exception, match="S3 read error"):
            processor.process_cohort("test_cohort", {})
        
        # Verify error was logged
        mock_logger.error.assert_called()
