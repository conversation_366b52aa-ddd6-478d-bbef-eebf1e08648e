import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

@pytest.mark.integration
class TestTransactionTransformerIntegration:
    """Integration tests for TransactionTransformer class."""

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_full_execution_flow(self, mock_date_utils, mock_operations, 
                                mock_io_utils, mock_spark_utils, mock_logger, 
                                spark_session):
        """Test the complete execution flow with realistic data."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        # Setup comprehensive mock config
        config = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {
                "all_transaction_file_path": "all_transactions"
            },
            "trading_competition": {
                "start_time": "2025-01-01 00:00:00.000"
            },
            "aum_tier_upgrade": {
                "tier_snapshot_path": "tier_snapshots"
            },
            "crypto_rebrand_txn_path": "crypto_rebrand_transactions",
            "start_asset_position_path": "start_positions",
            "global_stock_splits_path": "stock_splits",
            "pluang_partner_id": 1,
            "usdt_coin_id": 825,
            "prices": {
                "forex": {"price_path": "forex_prices"},
                "gold": {"price_path": "gold_prices"},
                "global_stock": {"price_path": "global_stock_prices"},
                "crypto_currency": {"price_path": "crypto_prices"},
                "crypto_currency_futures": {"price_path": "crypto_futures_prices"},
                "fund": {"price_path": "fund_prices"},
                "global_stock_options": {
                    "price_path": "options_prices",
                    "price_snapshot_folder": "snapshots"
                }
            },
            "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0)
        }
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Create comprehensive test data
        
        # Price data
        forex_data = [{"forex_id": 10000, "partner_id": 1, "mid_price": 15000}]
        gold_data = [{"partnerId": 1, "closeBuyBack": "1000000", "closeSell": "1010000"}]
        global_stock_data = [{"_id": 1, "mid_price": 100.50}, {"_id": 2, "mid_price": 200.75}]
        crypto_data = [{"_id": 101, "mid_price": 50000.0}, {"_id": 825, "mid_price": 15000.0}]
        crypto_futures_data = [{"_id": 301, "close_price": 45000.0}]
        fund_data = [{"fund_id": 401, "net_asset_value": 1000.0}]
        options_data = [{"optionsContractId": 501, "price": 5.0}]
        
        forex_df = spark_session.createDataFrame(forex_data)
        gold_df = spark_session.createDataFrame(gold_data)
        global_stock_df = spark_session.createDataFrame(global_stock_data)
        crypto_df = spark_session.createDataFrame(crypto_data)
        crypto_futures_df = spark_session.createDataFrame(crypto_futures_data)
        fund_df = spark_session.createDataFrame(fund_data)
        options_df = spark_session.createDataFrame(options_data)
        
        # User tier data
        tier_data = [
            {"account_id": 1001, "tier": "GOLD"},
            {"account_id": 1002, "tier": "SILVER"}
        ]
        tier_df = spark_session.createDataFrame(tier_data)
        
        # Transaction data for different asset types
        crypto_txn_data = [
            {
                "crypto_currency_id": 101, "account_id": 1001, "user_id": 101, "id": 1,
                "created": datetime(2025, 1, 15, 10, 0, 0), "updated": datetime(2025, 1, 15, 10, 0, 0),
                "executed_quantity": 1.0, "executed_unit_price": 50000.0, "executed_total_price": 50000.0,
                "fee": 100.0, "transaction_type": "BUY", "status": "SUCCESS", "partner_id": 1,
                "transaction_time": datetime(2025, 1, 15, 10, 0, 0)
            }
        ]
        
        gss_txn_data = [
            {
                "global_stock_id": 1, "account_id": 1002, "user_id": 102, "id": 2,
                "created": datetime(2025, 1, 15, 11, 0, 0), "updated": datetime(2025, 1, 15, 11, 0, 0),
                "executed_quantity": 100.0, "executed_unit_price": 100.0, "executed_total_price": 10000.0,
                "transaction_fee": 10.0, "transaction_type": "BUY", "status": "SUCCESS", "partner_id": 1,
                "usd_to_idr": 15000.0, "transaction_time": datetime(2025, 1, 15, 11, 0, 0),
                "stock_type": "REGULAR", "trading_hours": "REGULAR"
            }
        ]
        
        # Initial positions data
        initials_data = [
            {
                "asset_type": "wallet", "asset_id": 10000, "account_id": 1001, "user_id": 101,
                "transaction_id": 0, "created": datetime(2025, 1, 1, 0, 0, 0),
                "updated": datetime(2025, 1, 1, 0, 0, 0), "executed_quantity": 1000000.0,
                "executed_unit_price": 1.0, "executed_total_price": 1000000.0, "fees": 0.0,
                "transaction_type": "INITIAL", "status": "SUCCESS", "currency_to_idr": 1.0,
                "transaction_time": datetime(2025, 1, 1, 0, 0, 0), "leverage": 0,
                "asset_sub_type": "wallet", "current_unit_price": 1.0, "current_currency_to_idr": 1
            }
        ]
        
        # Stock splits data
        splits_data = [
            {
                "asset_sub_type": "global_stock_transactions",
                "asset_id": 1,
                "ratio": 2.0,
                "split_updated": datetime(2025, 1, 20, 0, 0, 0)  # After transaction
            }
        ]
        
        crypto_txn_df = spark_session.createDataFrame(crypto_txn_data)
        gss_txn_df = spark_session.createDataFrame(gss_txn_data)
        initials_df = spark_session.createDataFrame(initials_data)
        splits_df = spark_session.createDataFrame(splits_data)
        
        # Define empty schemas for different data types
        empty_crypto_schema = StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("executed_quantity", DoubleType(), True),
            StructField("executed_unit_price", DoubleType(), True),
            StructField("executed_total_price", DoubleType(), True),
            StructField("fee", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])

        empty_wallet_transfers_schema = StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("id", LongType(), True),
            StructField("created_at", TimestampType(), True),
            StructField("updated_at", TimestampType(), True),
            StructField("total_quantity", DoubleType(), True),
            StructField("unit_price", DoubleType(), True),
            StructField("transfer_fee", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])

        empty_rebrand_schema = StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("executed_quantity", DoubleType(), True),
            StructField("executed_unit_price", DoubleType(), True),
            StructField("executed_total_price", DoubleType(), True),
            StructField("fee", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])

        empty_pocket_schema = StructType([
            StructField("crypto_currency_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("executed_quantity", DoubleType(), True),
            StructField("executed_unit_price", DoubleType(), True),
            StructField("executed_total_price", DoubleType(), True),
            StructField("transaction_fee", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])

        # Setup IO utils mock responses
        def mock_read_json_data(path):
            if "forex_prices" in path:
                return forex_df
            elif "gold_prices" in path:
                return gold_df
            elif "fund_prices" in path:
                return fund_df
            return spark_session.createDataFrame([], StructType([]))

        def mock_read_csv_file(path, schema=None, header=False):
            if "global_stock_prices" in path:
                return global_stock_df
            elif "crypto_prices" in path:
                return crypto_df
            elif "crypto_futures_prices" in path:
                return crypto_futures_df
            elif "options_prices" in path:
                return options_df
            elif "tier_snapshots" in path:
                return tier_df
            elif "stock_splits" in path:
                return splits_df
            elif "crypto_rebrand_transactions" in path:
                return spark_session.createDataFrame([], empty_rebrand_schema)
            elif "global_stock_merger_transactions" in path:
                # Mock empty merger transactions with required schema
                merger_schema = StructType([
                    StructField("asset_id", LongType(), True),
                    StructField("account_id", LongType(), True),
                    StructField("user_id", LongType(), True),
                    StructField("transaction_id", LongType(), True),
                    StructField("created", TimestampType(), True),
                    StructField("updated", TimestampType(), True),
                    StructField("executed_quantity", DoubleType(), True),
                    StructField("executed_unit_price", DoubleType(), True),
                    StructField("fees", DoubleType(), True),
                    StructField("transaction_type", StringType(), True),
                    StructField("status", StringType(), True),
                    StructField("currency_to_idr", DoubleType(), True),
                    StructField("transaction_time", TimestampType(), True),
                    StructField("leverage", IntegerType(), True)
                ])
                return spark_session.createDataFrame([], merger_schema)
            return spark_session.createDataFrame([], StructType([]))

        # Additional schemas for other transaction types
        empty_options_schema = StructType([
            StructField("id", LongType(), True),
            StructField("options_contract_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("executed_quantity", DoubleType(), True),
            StructField("executed_unit_price", DoubleType(), True),
            StructField("executed_total_price", DoubleType(), True),
            StructField("transaction_fee", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("usd_to_idr", DoubleType(), True)
        ])

        empty_options_contracts_schema = StructType([
            StructField("id", LongType(), True),
            StructField("shares_per_contract", DoubleType(), True)
        ])

        empty_fund_schema = StructType([
            StructField("id", LongType(), True),
            StructField("fund_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("unit_price", DoubleType(), True),
            StructField("total_price", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])

        empty_forex_schema = StructType([
            StructField("id", LongType(), True),
            StructField("forex_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("unit_price", DoubleType(), True),
            StructField("total_price", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])

        empty_gold_schema = StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("unit_price", DoubleType(), True),
            StructField("total_price", DoubleType(), True),
            StructField("final_amount", DoubleType(), True),
            StructField("fees", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("leverage", IntegerType(), True)
        ])

        empty_gold_withdrawals_schema = StructType([
            StructField("id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("net_amount", DoubleType(), True),
            StructField("unit_price", DoubleType(), True),
            StructField("sell_price", DoubleType(), True),
            StructField("fee", DoubleType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])

        empty_crypto_futures_schema = StructType([
            StructField("id", LongType(), True),
            StructField("crypto_future_instrument_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("price", DoubleType(), True),
            StructField("total_trade_fee", DoubleType(), True),
            StructField("fee", DoubleType(), True),
            StructField("settle_asset_mid_price", DoubleType(), True),
            StructField("transaction_type", StringType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True),
            StructField("traded_at_timestamp", TimestampType(), True)
        ])

        def mock_read_parquet_data(path, schema=None, header=False):
            if "crypto_currency_transactions" in path:
                return crypto_txn_df
            elif "crypto_currency_wallet_transfers" in path:
                return spark_session.createDataFrame([], empty_wallet_transfers_schema)
            elif "crypto_currency_pocket_transactions" in path:
                return spark_session.createDataFrame([], empty_pocket_schema)
            elif "global_stock_transactions" in path:
                return gss_txn_df
            elif "options_contract_transactions" in path:
                return spark_session.createDataFrame([], empty_options_schema)
            elif "options_contracts" in path:
                return spark_session.createDataFrame([], empty_options_contracts_schema)
            elif "fund_transactions" in path:
                return spark_session.createDataFrame([], empty_fund_schema)
            elif "forex_transactions" in path:
                return spark_session.createDataFrame([], empty_forex_schema)
            elif "forex_top_ups" in path:
                return spark_session.createDataFrame([], empty_forex_schema)
            elif "forex_cash_outs" in path:
                return spark_session.createDataFrame([], empty_forex_schema)
            elif "gold_transactions" in path:
                return spark_session.createDataFrame([], empty_gold_schema)
            elif "gold_withdrawals" in path:
                return spark_session.createDataFrame([], empty_gold_withdrawals_schema)
            elif "crypto_future_trades" in path:
                return spark_session.createDataFrame([], empty_crypto_futures_schema)
            elif "crypto_future_funding_transactions" in path:
                return spark_session.createDataFrame([], empty_crypto_futures_schema)
            elif "start_positions" in path:
                return initials_df
            else:
                return spark_session.createDataFrame([], StructType([]))
        
        mock_io_utils.return_value.read_json_data.side_effect = mock_read_json_data
        mock_io_utils.return_value.read_csv_file.side_effect = mock_read_csv_file
        mock_io_utils.return_value.read_parquet_data.side_effect = mock_read_parquet_data
        mock_io_utils.return_value.write_parquet_file = Mock()
        
        # Setup operations mock with proper empty DataFrame handling
        def mock_get_union(df1, df2):
            if df2.count() == 0:  # If second DataFrame is empty, just return the first
                return df1
            return df1.union(df2)
        mock_operations.return_value.get_union.side_effect = mock_get_union
        mock_operations.return_value.check_null_values.return_value = []
        mock_operations.return_value.de_dupe_dataframe.side_effect = lambda df, cols, order_col: df
        
        # Initialize and execute
        transformer = TransactionTransformer(config)
        transformer.execute()
        
        # Verify that write operations were called
        assert mock_io_utils.return_value.write_parquet_file.call_count == 2  # S3 and HDFS
        
        # Verify the calls were made with correct paths
        write_calls = mock_io_utils.return_value.write_parquet_file.call_args_list
        paths_called = [call[0][1] for call in write_calls]  # Second argument is the path
        
        assert any("all_transactions" in path for path in paths_called)
        assert any("hdfs://" in path for path in paths_called)

    @patch('src.jobs.trading_competition.transaction_transformer.get_logger')
    @patch('src.jobs.trading_competition.transaction_transformer.SparkUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.IOUtils')
    @patch('src.jobs.trading_competition.transaction_transformer.Operations')
    @patch('src.jobs.trading_competition.transaction_transformer.DateUtils')
    def test_data_consistency_across_asset_types(self, mock_date_utils, mock_operations, 
                                                mock_io_utils, mock_spark_utils, mock_logger, 
                                                spark_session):
        """Test data consistency across different asset types."""
        from src.jobs.trading_competition.transaction_transformer import TransactionTransformer
        
        config = {
            "bucket_path": "s3a://test-bucket",
            "snapshot_path": "snapshots",
            "batches": {"all_transaction_file_path": "all_transactions"},
            "trading_competition": {"start_time": "2025-01-01 00:00:00.000"},
            "aum_tier_upgrade": {"tier_snapshot_path": "tier_snapshots"},
            "crypto_rebrand_txn_path": "crypto_rebrand_transactions",
            "start_asset_position_path": "start_positions",
            "global_stock_splits_path": "stock_splits",
            "pluang_partner_id": 1,
            "usdt_coin_id": 825,
            "prices": {
                "forex": {"price_path": "forex_prices"},
                "gold": {"price_path": "gold_prices"},
                "global_stock": {"price_path": "global_stock_prices"},
                "crypto_currency": {"price_path": "crypto_prices"},
                "crypto_currency_futures": {"price_path": "crypto_futures_prices"},
                "fund": {"price_path": "fund_prices"},
                "global_stock_options": {
                    "price_path": "options_prices",
                    "price_snapshot_folder": "snapshots"
                }
            },
            "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0)
        }
        
        # Setup mocks with minimal data
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 1, 0, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(), 14, datetime(2025, 1, 14).date(), 14,
            "2025-01-15T14:00:00.000Z", "2025-01-14T17:00:00.000Z"
        )
        
        # Create minimal test data for consistency check
        empty_df = spark_session.createDataFrame([], StructType([]))
        
        mock_io_utils.return_value.read_json_data.return_value = empty_df
        mock_io_utils.return_value.read_csv_file.return_value = empty_df
        mock_io_utils.return_value.read_parquet_data.return_value = empty_df
        mock_io_utils.return_value.write_parquet_file = Mock()
        
        mock_operations.return_value.get_union.side_effect = lambda df1, df2: df1.union(df2)
        mock_operations.return_value.check_null_values.return_value = []
        mock_operations.return_value.de_dupe_dataframe.side_effect = lambda df, cols, order_col: df
        
        # Initialize transformer
        transformer = TransactionTransformer(config)
        
        # Test that all required methods exist and can be called
        assert hasattr(transformer, 'get_all_prices')
        assert hasattr(transformer, 'get_user_tiers')
        assert hasattr(transformer, 'get_crypto_currency_transactions')
        assert hasattr(transformer, 'get_global_stock_transactions')
        assert hasattr(transformer, 'get_crypto_future_transactions')
        assert hasattr(transformer, 'get_options_transactions')
        assert hasattr(transformer, 'get_fund_transactions')
        assert hasattr(transformer, 'get_forex_transactions')
        assert hasattr(transformer, 'get_gold_transactions')
        assert hasattr(transformer, 'update_executed_quantity_splits')
        assert hasattr(transformer, 'add_current_unit_price')
        assert hasattr(transformer, 'cast_fields')
        assert hasattr(transformer, 'execute')
        assert hasattr(transformer, 'run')
        
        # Verify configuration is properly set
        assert transformer.config == config
        assert transformer.partner_id == 1
        assert transformer.usdt_coin_id == 825
