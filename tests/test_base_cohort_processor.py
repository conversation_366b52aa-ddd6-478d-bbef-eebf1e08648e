"""
Unit tests for BaseCohortProcessor.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pyspark.sql.types import StructType, <PERSON>ructField, StringType, IntegerType
from src.jobs.cohorting.processors.base_cohort_processor import BaseCohortProcessor
from tests.helpers.cohorting_test_helpers import (
    validate_data_integrity, validate_positive_integers,
    create_test_schema, assert_basic_dataframe_properties
)


class TestBaseCohortProcessor:
    """Test cases for BaseCohortProcessor."""

    @pytest.fixture
    def mock_config(self):
        """Mock configuration."""
        return {
            "t_1": "2025-10-15",
            "t_2": "2025-10-16",
            "bucket_path": "s3a://test-bucket"
        }

    @patch('src.jobs.cohorting.processors.base_cohort_processor.SQLQueryEngine')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.DataSourceManager')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.ParameterRegistry')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.IOUtils')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.Operations')
    def test_initialization(self, mock_operations, mock_io_utils, mock_parameter_registry, 
                           mock_data_source_manager, mock_sql_query_engine, mock_config, spark_session):
        """Test BaseCohortProcessor initialization."""
        mock_logger = Mock()
        
        mock_ops_instance = Mock()
        mock_operations.return_value = mock_ops_instance
        
        mock_io_instance = Mock()
        mock_io_utils.return_value = mock_io_instance
        
        mock_param_registry = Mock()
        mock_parameter_registry.return_value = mock_param_registry
        
        mock_ds_manager = Mock()
        mock_data_source_manager.return_value = mock_ds_manager
        
        mock_sql_engine = Mock()
        mock_sql_query_engine.return_value = mock_sql_engine
        
        processor = BaseCohortProcessor(spark_session, mock_config, mock_logger)
        
        assert processor.spark == spark_session
        assert processor.config == mock_config
        assert processor.logger == mock_logger
        assert processor.t_1 == "2025-10-15"
        assert processor.ops == mock_ops_instance
        assert processor.io_utils == mock_io_instance
        assert processor.parameter_registry == mock_param_registry
        assert processor.data_source_manager == mock_ds_manager
        assert processor.sql_engine == mock_sql_engine
        
        # Verify data sources were registered (check that the method was called with the correct names)
        call_args_list = mock_ds_manager.register_data_source_class.call_args_list
        assert len(call_args_list) == 3

        # Check the first call (AUMDataSource)
        assert call_args_list[0][0][0] == "AUMDataSource"
        # Check the second call (BigQueryAppEventsDataSource)
        assert call_args_list[1][0][0] == "BigQueryAppEventsDataSource"
        # Check the third call (HeldAssetsDataSource)
        assert call_args_list[2][0][0] == "HeldAssetsDataSource"

    @patch('src.jobs.cohorting.processors.base_cohort_processor.SQLQueryEngine')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.DataSourceManager')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.ParameterRegistry')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.IOUtils')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.Operations')
    def test_process_cohort_no_parameters(self, mock_operations, mock_io_utils, mock_parameter_registry, 
                                         mock_data_source_manager, mock_sql_query_engine, mock_config, spark_session):
        """Test processing cohort with no parameters."""
        mock_logger = Mock()
        
        processor = BaseCohortProcessor(spark_session, mock_config, mock_logger)
        
        cohort_config = {"parameters": []}
        result = processor.process_cohort("test_cohort", cohort_config)
        
        # Should return empty DataFrame
        assert result.count() == 0
        assert len(result.columns) == 0

    @patch('src.jobs.cohorting.processors.base_cohort_processor.SQLQueryEngine')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.DataSourceManager')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.ParameterRegistry')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.IOUtils')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.Operations')
    def test_process_cohort_with_parameters(self, mock_operations, mock_io_utils, mock_parameter_registry, 
                                           mock_data_source_manager, mock_sql_query_engine, mock_config, spark_session):
        """Test processing cohort with parameters."""
        mock_logger = Mock()
        
        # Mock parameter registry
        mock_param_registry = Mock()
        mock_param_configs = {
            "param1": {
                "parameter_id": "param1",
                "data_source_class": "AUMDataSource",
                "sql_condition": "amount > 1000",
                "required_columns": ["account_id", "amount"],
                "output_columns": ["account_id", "user_id"],
                "group_by_columns": [],
                "join_keys": ["account_id"]
            }
        }
        mock_param_registry.get_parameter_configs.return_value = mock_param_configs
        mock_parameter_registry.return_value = mock_param_registry
        
        # Mock SQL engine
        mock_sql_engine = Mock()
        mock_joined_df = Mock()
        mock_final_df = Mock()
        mock_final_df.count.return_value = 5
        mock_joined_df.count.return_value = 10
        mock_sql_engine.join_parameter_dataframes.return_value = mock_joined_df
        mock_sql_query_engine.return_value = mock_sql_engine
        
        processor = BaseCohortProcessor(spark_session, mock_config, mock_logger)
        
        # Mock the private methods
        mock_param_df = Mock()
        mock_param_df.count.return_value = 15
        
        with patch.object(processor, '_process_parameter', return_value=mock_param_df), \
             patch.object(processor, '_apply_cohort_logic', return_value=mock_final_df):
            
            cohort_config = {
                "parameters": [{"parameter_id": "param1", "join_type": "inner"}],
                "cohort_logic": {}
            }
            
            result = processor.process_cohort("test_cohort", cohort_config)
            
            assert result == mock_final_df
            # get_parameter_configs is called twice - once in process_cohort and once in _join_parameter_dataframes
            assert mock_param_registry.get_parameter_configs.call_count == 2
            mock_param_registry.get_parameter_configs.assert_called_with(["param1"])

    @patch('src.jobs.cohorting.processors.base_cohort_processor.SQLQueryEngine')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.DataSourceManager')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.ParameterRegistry')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.IOUtils')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.Operations')
    def test_process_parameter_success(self, mock_operations, mock_io_utils, mock_parameter_registry, 
                                      mock_data_source_manager, mock_sql_query_engine, mock_config, spark_session):
        """Test successful parameter processing."""
        mock_logger = Mock()
        
        # Create test DataFrame with realistic data
        test_data = [("ACC001", "USER001", 1500), ("ACC002", "USER002", 2000), ("ACC003", "USER003", 500)]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("amount", IntegerType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        # Mock data source
        mock_data_source = Mock()
        mock_data_source.read.return_value = test_df
        
        mock_ds_manager = Mock()
        mock_ds_manager.get_data_source_instance.return_value = mock_data_source
        mock_data_source_manager.return_value = mock_ds_manager
        
        # Mock SQL engine - should return filtered data (amount > 1000)
        filtered_data = [("ACC001", "USER001", 1500), ("ACC002", "USER002", 2000)]
        filtered_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("amount", IntegerType(), True)
        ])
        filtered_df = spark_session.createDataFrame(filtered_data, filtered_schema)

        mock_sql_engine = Mock()
        mock_sql_engine.execute_sql.return_value = filtered_df
        mock_sql_query_engine.return_value = mock_sql_engine
        
        processor = BaseCohortProcessor(spark_session, mock_config, mock_logger)
        
        param_config = {
            "parameter_id": "test_param",
            "data_source_class": "AUMDataSource",
            "data_source_params": {"partition_date": "2025-10-15"},
            "sql_condition": "amount > 1000",
            "required_columns": ["account_id", "amount"],
            "output_columns": ["account_id", "user_id"],
            "group_by_columns": []
        }
        
        join_config = {"parameter_id": "test_param", "join_type": "inner"}
        
        result = processor._process_parameter(param_config, join_config)

        # Validate result structure and data content
        assert result.count() == 2  # Should filter out ACC003 (amount=500 < 1000)
        result_data = result.collect()

        # Validate data integrity using helpers
        validate_positive_integers(result_data, ["amount"])

        # Verify filtering logic and account-user mappings
        account_ids = {row["account_id"] for row in result_data}
        assert account_ids == {"ACC001", "ACC002"}  # Only accounts with amount > 1000

        account_user_map = {row["account_id"]: row["user_id"] for row in result_data}
        expected_mappings = {"ACC001": "USER001", "ACC002": "USER002"}
        assert account_user_map == expected_mappings

        mock_data_source.read.assert_called_once()
        mock_sql_engine.execute_sql.assert_called_once()

    @patch('src.jobs.cohorting.processors.base_cohort_processor.SQLQueryEngine')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.DataSourceManager')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.ParameterRegistry')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.IOUtils')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.Operations')
    def test_process_parameter_no_data(self, mock_operations, mock_io_utils, mock_parameter_registry, 
                                      mock_data_source_manager, mock_sql_query_engine, mock_config, spark_session):
        """Test parameter processing with no data."""
        mock_logger = Mock()
        
        # Create empty DataFrame
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        empty_df = spark_session.createDataFrame([], test_schema)
        
        # Mock data source
        mock_data_source = Mock()
        mock_data_source.read.return_value = empty_df
        
        mock_ds_manager = Mock()
        mock_ds_manager.get_data_source_instance.return_value = mock_data_source
        mock_data_source_manager.return_value = mock_ds_manager
        
        processor = BaseCohortProcessor(spark_session, mock_config, mock_logger)
        
        param_config = {
            "parameter_id": "test_param",
            "data_source_class": "AUMDataSource",
            "data_source_params": {},
            "sql_condition": "",
            "required_columns": ["account_id"],
            "output_columns": ["account_id", "user_id"],
            "group_by_columns": []
        }
        
        join_config = {}
        
        result = processor._process_parameter(param_config, join_config)
        
        assert result.count() == 0
        assert set(result.columns) == {"account_id", "user_id"}
        mock_logger.warning.assert_called_with("No data found for parameter test_param")

    @patch('src.jobs.cohorting.processors.base_cohort_processor.SQLQueryEngine')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.DataSourceManager')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.ParameterRegistry')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.IOUtils')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.Operations')
    def test_join_parameter_dataframes(self, mock_operations, mock_io_utils, mock_parameter_registry, 
                                      mock_data_source_manager, mock_sql_query_engine, mock_config, spark_session):
        """Test joining parameter DataFrames."""
        mock_logger = Mock()
        
        # Mock parameter registry
        mock_param_registry = Mock()
        mock_param_configs = {
            "param1": {"join_keys": ["account_id"]},
            "param2": {"join_keys": ["user_id"]}
        }
        mock_param_registry.get_parameter_configs.return_value = mock_param_configs
        mock_parameter_registry.return_value = mock_param_registry
        
        # Mock SQL engine
        mock_sql_engine = Mock()
        mock_joined_df = Mock()
        mock_sql_engine.join_parameter_dataframes.return_value = mock_joined_df
        mock_sql_query_engine.return_value = mock_sql_engine
        
        processor = BaseCohortProcessor(spark_session, mock_config, mock_logger)
        
        parameter_dfs = {"param1": Mock(), "param2": Mock()}
        parameters = [
            {"parameter_id": "param1", "join_type": "inner"},
            {"parameter_id": "param2", "join_type": "left"}
        ]
        
        result = processor._join_parameter_dataframes(parameter_dfs, parameters)
        
        assert result == mock_joined_df
        
        # Verify the enriched join configs were created correctly
        call_args = mock_sql_engine.join_parameter_dataframes.call_args
        enriched_configs = call_args[0][1]
        
        assert len(enriched_configs) == 2
        assert enriched_configs[0]["parameter_id"] == "param1"
        assert enriched_configs[0]["join_type"] == "inner"
        assert enriched_configs[0]["join_keys"] == ["account_id"]
        assert enriched_configs[1]["parameter_id"] == "param2"
        assert enriched_configs[1]["join_type"] == "left"
        assert enriched_configs[1]["join_keys"] == ["user_id"]

    @patch('src.jobs.cohorting.processors.base_cohort_processor.SQLQueryEngine')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.DataSourceManager')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.ParameterRegistry')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.IOUtils')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.Operations')
    def test_apply_cohort_logic_with_where_clause(self, mock_operations, mock_io_utils, mock_parameter_registry, 
                                                  mock_data_source_manager, mock_sql_query_engine, mock_config, spark_session):
        """Test applying cohort logic with where clause."""
        mock_logger = Mock()
        
        # Create test DataFrame
        test_data = [("ACC001", "USER001", 1500), ("ACC002", "USER002", 500)]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("amount", IntegerType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        processor = BaseCohortProcessor(spark_session, mock_config, mock_logger)
        
        # Mock cohort logic as a dict (simulating Row.asDict())
        cohort_logic_mock = Mock()
        cohort_logic_mock.asDict.return_value = {
            "select_clause": "account_id, user_id",
            "where_clause": "amount > 1000",
            "group_by_columns": []
        }
        
        cohort_config = {
            "cohort_logic": cohort_logic_mock
        }
        
        result = processor._apply_cohort_logic(test_df, cohort_config)
        
        # Should filter to only records with amount > 1000
        assert result.count() == 1
        assert result.collect()[0]["account_id"] == "ACC001"

    @patch('src.jobs.cohorting.processors.base_cohort_processor.SQLQueryEngine')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.DataSourceManager')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.ParameterRegistry')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.IOUtils')
    @patch('src.jobs.cohorting.processors.base_cohort_processor.Operations')
    def test_apply_cohort_logic_empty_logic(self, mock_operations, mock_io_utils, mock_parameter_registry, 
                                           mock_data_source_manager, mock_sql_query_engine, mock_config, spark_session):
        """Test applying cohort logic with empty logic."""
        mock_logger = Mock()
        
        # Create test DataFrame
        test_data = [("ACC001", "USER001"), ("ACC002", "USER002")]
        test_schema = StructType([
            StructField("account_id", StringType(), True),
            StructField("user_id", StringType(), True)
        ])
        test_df = spark_session.createDataFrame(test_data, test_schema)
        
        processor = BaseCohortProcessor(spark_session, mock_config, mock_logger)
        
        cohort_config = {"cohort_logic": {}}
        
        result = processor._apply_cohort_logic(test_df, cohort_config)
        
        # Should return original DataFrame unchanged
        assert result.count() == 2
        assert set(result.columns) == {"account_id", "user_id"}
