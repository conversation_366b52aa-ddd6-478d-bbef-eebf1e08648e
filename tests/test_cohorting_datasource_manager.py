"""
Unit tests for DataSourceManager.
"""

import unittest
from unittest.mock import Mock
from src.jobs.cohorting.data_sources.data_source_manager import DataSourceManager
from src.jobs.cohorting.data_sources.aum_data_source import AUMDataSource
from src.jobs.cohorting.data_sources.bigquery_app_events_data_source import BigQueryAppEventsDataSource


class TestDataSourceManager(unittest.TestCase):
    """Test cases for DataSourceManager."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_spark = Mock()
        self.mock_config = {
            "bucket_path": "s3a://test-bucket"
        }
        self.mock_logger = Mock()

    def test_register_and_get_available_sources(self):
        """Test registering and getting available data sources."""
        manager = DataSourceManager(self.mock_spark, self.mock_config, self.mock_logger)
        manager.register_data_source_class("AUMDataSource", AUMDataSource)
        manager.register_data_source_class("BigQueryAppEventsDataSource", BigQueryAppEventsDataSource)
        sources = manager.get_available_data_sources()
        self.assertIn("AUMDataSource", sources)
        self.assertIn("BigQueryAppEventsDataSource", sources)

    def test_get_data_source_instance(self):
        """Test getting data source instance."""
        manager = DataSourceManager(self.mock_spark, self.mock_config, self.mock_logger)
        manager.register_data_source_class("AUMDataSource", AUMDataSource)

        instance = manager.get_data_source_instance("AUMDataSource")
        self.assertIsInstance(instance, AUMDataSource)
        self.assertEqual(instance.name, "AUMDataSource")
        # Verify logger was passed correctly
        self.assertEqual(instance.logger, self.mock_logger)

    def test_get_data_source_instance_not_registered(self):
        """Test getting instance of unregistered data source."""
        manager = DataSourceManager(self.mock_spark, self.mock_config, self.mock_logger)

        with self.assertRaises(ValueError) as context:
            manager.get_data_source_instance("NonExistentDataSource")

        self.assertIn("not registered", str(context.exception))


if __name__ == '__main__':
    unittest.main()
