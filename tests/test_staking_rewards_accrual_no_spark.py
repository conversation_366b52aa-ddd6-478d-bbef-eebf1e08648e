import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch, call

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestStakingRewardsAccrualNoSpark:
    """No-Spark tests for StakingRewardsAccrual business logic."""

    def _setup_mocks(self, mock_crypto_staking_config):
        """Helper to setup common mocks for no-spark tests."""
        with patch('src.jobs.crypto_staking.staking_rewards_accrual.get_logger') as mock_logger, \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.SparkUtils'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.IOUtils'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.Operations'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.DateUtils') as mock_date_utils:
            
            mock_logger.return_value = Mock()
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
            return mock_date_utils

    def test_config_initialization_logic(self, mock_crypto_staking_config):
        """Test configuration initialization without Spark dependencies."""
        with patch('src.jobs.crypto_staking.staking_rewards_accrual.get_logger') as mock_logger, \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.SparkUtils'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.IOUtils'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.Operations'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.DateUtils') as mock_date_utils:
            
            mock_logger.return_value = Mock()
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
            
            from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
            processor = StakingRewardsAccrual(mock_crypto_staking_config)
            
            assert processor.bucket_path == "s3a://test-bucket"
            assert processor.offset == 0
            assert processor.execution_time == "jkt_day_end"
            assert processor.t_1 == "2025-01-15"
            assert processor.t_2 == "2025-01-14"
            assert processor.current_date == datetime(2025, 1, 15).date()

    def test_s3_path_construction_logic(self, mock_crypto_staking_config):
        """Test S3 path construction logic without Spark."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        with patch('src.jobs.crypto_staking.staking_rewards_accrual.get_logger'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.SparkUtils'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.IOUtils') as mock_io_utils, \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.Operations'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.DateUtils') as mock_date_utils:
            
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
            
            processor = StakingRewardsAccrual(mock_crypto_staking_config)
            processor.io_utils = mock_io_utils.return_value
            
            # Test accounts path construction
            expected_accounts_path = "s3a://test-bucket/snapshots/crypto_currency_staking_accounts/dt=2025-01-15/"
            expected_assets_path = "s3a://test-bucket/snapshots/crypto_currency_staking_assets/dt=2025-01-15/"
            
            # Mock the nested function calls and Spark operations
            mock_df = Mock()
            mock_df.withColumn.return_value = mock_df
            mock_df.select.return_value = mock_df
            mock_io_utils.return_value.read_parquet_data.return_value = mock_df

            # Mock Spark functions to avoid Spark context issues
            with patch('src.jobs.crypto_staking.staking_rewards_accrual.F') as mock_F, \
                 patch('src.jobs.crypto_staking.staking_rewards_accrual.col') as mock_col:

                mock_F.get_json_object.return_value = Mock()
                mock_col.return_value = Mock()

                # Call method that constructs paths
                processor.get_staking_snapshot_data()
            
            # Verify correct paths were constructed
            call_args = mock_io_utils.return_value.read_parquet_data.call_args_list
            called_paths = [call[0][0] for call in call_args]
            assert expected_accounts_path in called_paths
            assert expected_assets_path in called_paths

    def test_eligible_staked_quantity_calculation_logic(self):
        """Test eligible staked quantity calculation logic."""
        test_cases = [
            (100.0, 10.0, 110.0),           # Both positive
            (200.0, 0.0, 200.0),            # Only staked balance
            (0.0, 50.0, 50.0),              # Only unstaking balance
            (100.********, 10.********, 111.0)  # High precision
        ]
        
        for staked, unstaking, expected in test_cases:
            eligible_quantity = round(staked + unstaking, 8)
            assert abs(eligible_quantity - expected) < 0.********  # Allow for floating point precision

    def test_ref_id_generation_and_balance_filtering_logic(self):
        """Test ref_id generation and balance filtering logic without Spark."""
        current_date = datetime(2025, 1, 15).date()
        current_date_formatted = current_date.strftime("%Y%m%d")
        
        # Test ref_id generation
        test_cases = [
            (1001, 825, "1001_825_********"),
            (1002, 826, "1002_826_********")
        ]
        
        for account_id, crypto_id, expected in test_cases:
            ref_id = f"{account_id}_{crypto_id}_{current_date_formatted}"
            assert ref_id == expected
        
        # Test balance filtering
        balance_cases = [
            (100.0, 10.0, True),      # Positive balance
            (0.0, 0.0, False),        # Zero balance
            (-10.0, 5.0, False),      # Negative balance
            (0.********, 0.0, True)   # Very small positive
        ]
        
        for staked, unstaking, should_include in balance_cases:
            eligible_quantity = staked + unstaking
            assert (eligible_quantity > 0) == should_include

    def test_date_formatting_and_configuration_logic(self, mock_crypto_staking_config):
        """Test date formatting and configuration logic."""
        from src.jobs.crypto_staking.staking_rewards_accrual import StakingRewardsAccrual
        
        # Test date formatting
        test_dates = [
            (datetime(2025, 1, 15).date(), "********"),
            (datetime(2025, 12, 31).date(), "********"),
            (datetime(2024, 2, 29).date(), "20240229"),  # Leap year
            (datetime(2023, 1, 1).date(), "20230101")
        ]
        
        for test_date, expected_format in test_dates:
            formatted_date = test_date.strftime("%Y%m%d")
            assert formatted_date == expected_format
        
        # Test configuration
        with patch('src.jobs.crypto_staking.staking_rewards_accrual.get_logger') as mock_logger, \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.SparkUtils'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.IOUtils'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.Operations'), \
             patch('src.jobs.crypto_staking.staking_rewards_accrual.DateUtils') as mock_date_utils:
            
            mock_logger.return_value = Mock()
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
            
            processor = StakingRewardsAccrual(mock_crypto_staking_config)
            
            assert processor.config["kafka_topics"]["staking_user_accrued_rewards_topic"] == "staking_user_accrued_rewards"
            assert processor.config["crypto_staking_topic_actor_prop"] == "crypto_staking_actor"

    def test_precision_rounding_and_edge_cases(self):
        """Test precision rounding logic and business logic edge cases."""
        # Test precision rounding
        test_cases = [
            (100.********9, 10.********1, 111.0),  # Should round to 8 decimal places
            (0.999999999, 0.0********, 1.0),       # Edge case rounding
            (100.********, 10.********, 110.00000002),  # High precision
            (50.5, 25.5, 76.0),                    # Simple case
        ]
        
        for staked, unstaking, expected in test_cases:
            result = round(staked + unstaking, 8)
            assert abs(result - expected) < 0.********
        
        # Test edge cases
        edge_cases = [
            (999999999.99999999, 0.********, True),  # Maximum precision
            (0.********, 0.0, True),                 # Minimum positive
            (0.0, 0.0, False),                       # Exactly zero
        ]
        
        for staked, unstaking, should_process in edge_cases:
            eligible = staked + unstaking
            assert (eligible > 0) == should_process
        
        # Test floating point precision
        fp_issue = 0.1 + 0.2
        rounded_fp = round(fp_issue, 8)
        assert abs(rounded_fp - 0.3) < 0.********

    def test_data_validation_and_aggregation_logic(self):
        """Test data validation and aggregation logic for multiple scenarios."""
        # Test data validation
        valid_cases = [
            {"account_id": 1001, "crypto_currency_id": 825, "staked_balance": 100.0, "unstaking_balance": 10.0},
            {"account_id": 1002, "crypto_currency_id": 826, "staked_balance": 0.0, "unstaking_balance": 50.0},
            {"account_id": 1003, "crypto_currency_id": 827, "staked_balance": 0.********, "unstaking_balance": 0.0},
        ]
        
        for case in valid_cases:
            assert all(key in case for key in ["account_id", "crypto_currency_id", "staked_balance", "unstaking_balance"])
            assert isinstance(case["account_id"], int)
            assert isinstance(case["crypto_currency_id"], int)
            assert isinstance(case["staked_balance"], (int, float))
            assert isinstance(case["unstaking_balance"], (int, float))
        
        # Test aggregation scenarios
        date_str = "********"
        
        # Multiple users with same coin
        users_same_coin = [
            {"user_id": 101, "crypto_id": 825, "balance": 100.0},
            {"user_id": 102, "crypto_id": 825, "balance": 200.0},
            {"user_id": 103, "crypto_id": 825, "balance": 50.0},
        ]
        
        ref_ids = [f"{user['user_id']}_{user['crypto_id']}_{date_str}" for user in users_same_coin]
        assert len(ref_ids) == len(set(ref_ids))
        
        # Multiple coins per user
        user_multiple_coins = [
            {"user_id": 101, "crypto_id": 825, "balance": 100.0},
            {"user_id": 101, "crypto_id": 826, "balance": 200.0},
            {"user_id": 101, "crypto_id": 827, "balance": 50.0},
        ]
        
        ref_ids = [f"{entry['user_id']}_{entry['crypto_id']}_{date_str}" for entry in user_multiple_coins]
        assert len(ref_ids) == len(set(ref_ids))

    def test_duplicate_prevention_and_error_handling_logic(self):
        """Test duplicate prevention and error handling logic."""
        # Test duplicate prevention
        account_id = 1001
        crypto_id = 825
        date1 = datetime(2025, 1, 15).date()
        date2 = datetime(2025, 1, 15).date()  # Same date
        date3 = datetime(2025, 1, 16).date()  # Different date
        
        ref_id1 = f"{account_id}_{crypto_id}_{date1.strftime('%Y%m%d')}"
        ref_id2 = f"{account_id}_{crypto_id}_{date2.strftime('%Y%m%d')}"
        ref_id3 = f"{account_id}_{crypto_id}_{date3.strftime('%Y%m%d')}"
        
        assert ref_id1 == ref_id2  # Same date should produce same ref_id
        assert ref_id1 != ref_id3  # Different date should produce different ref_id
        
        # Test error handling
        # None values
        staked_balance = None
        unstaking_balance = 10.0
        
        try:
            if staked_balance is not None and unstaking_balance is not None:
                eligible = staked_balance + unstaking_balance
            else:
                eligible = None
            assert eligible is None
        except TypeError:
            pass
        
        # String values
        try:
            staked_balance = "100.0"
            unstaking_balance = 10.0
            if isinstance(staked_balance, str):
                staked_balance = float(staked_balance)
            eligible = staked_balance + unstaking_balance
            assert eligible == 110.0
        except (ValueError, TypeError):
            pass

    def test_configuration_validation_logic(self):
        """Test configuration validation logic."""
        required_config_keys = [
            "bucket_path", "offset", "execution_time", "t_1", "t_2",
            "crypto_staking_topic_actor_prop", "kafka_topics"
        ]
        
        mock_config = {
            "bucket_path": "s3a://test-bucket",
            "offset": 0,
            "execution_time": "jkt_day_end",
            "t_1": "2025-01-15",
            "t_2": "2025-01-14",
            "crypto_staking_topic_actor_prop": "crypto_staking_actor",
            "kafka_topics": {
                "staking_user_accrued_rewards_topic": "staking_user_accrued_rewards"
            }
        }
        
        for key in required_config_keys:
            assert key in mock_config, f"Required config key '{key}' is missing"
        
        assert "staking_user_accrued_rewards_topic" in mock_config["kafka_topics"]
