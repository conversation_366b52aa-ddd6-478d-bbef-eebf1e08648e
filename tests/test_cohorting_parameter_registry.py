"""
Unit tests for ParameterRegistry.
"""

import unittest
from unittest.mock import Mock, patch
from src.jobs.cohorting.engine.parameter_registry import ParameterRegistry


class TestParameterRegistry(unittest.TestCase):
    """Test cases for ParameterRegistry."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_spark = Mock()
        self.mock_config = {
            "data_store": {
                "reporting_mongo": {
                    "username": "test",
                    "password": "test",
                    "host": "localhost",
                    "collection": "test_db"
                }
            }
        }
        self.mock_logger = Mock()

        # Mock parameter data
        self.mock_parameters = [
            {
                "_id": "ticker_views_prev_day",
                "name": "Ticker views on previous day",
                "data_source": "APP_EVENTS",
                "sql_condition": "event_timestamp >= TIMESTAMP('{t_1} 00:00:00') - INTERVAL 24 HOURS",
                "join_keys": ["account_id"],
                "required_columns": ["account_id", "user_id", "asset_symbol", "event_timestamp"],
                "output_columns": ["account_id", "user_id", "asset_symbol", "view_count"],
                "group_by_columns": ["account_id", "user_id", "asset_symbol"]
            },
            {
                "_id": "invested_global_stock_crypto_accounts",
                "name": "Accounts with Global Stock/Crypto investments",
                "data_source": "AUM",
                "sql_condition": "LOWER(asset_type) IN ('global_stock','crypto')",
                "join_keys": ["account_id"],
                "required_columns": ["account_id", "user_id", "asset_type", "quantity"],
                "output_columns": ["account_id"],
                "group_by_columns": ["account_id"]
            }
        ]

    def test_get_parameter_config_from_cache(self):
        """Test getting parameter config from cache."""
        registry = ParameterRegistry(self.mock_spark, self.mock_config, self.mock_logger)

        # Manually add to cache
        registry._parameter_cache["test_param"] = {"parameter_id": "test_param", "name": "Test"}

        result = registry.get_parameter_configs(["test_param"])

        self.assertIn("test_param", result)
        self.assertEqual(result["test_param"]["parameter_id"], "test_param")
        self.assertEqual(result["test_param"]["name"], "Test")

    def test_get_parameter_config_from_mongo(self):
        """Test getting parameter config from MongoDB."""
        # Create mock rows with proper structure
        mock_rows = []
        for param in self.mock_parameters:
            mock_row = Mock()
            mock_row.parameter_id = param["_id"]
            mock_row.name = param["name"]
            mock_row.data_source_class = param["data_source"]
            mock_row.sql_condition = param["sql_condition"]
            mock_row.join_keys = param["join_keys"]
            mock_row.required_columns = param["required_columns"]
            mock_row.output_columns = param["output_columns"]
            mock_row.group_by_columns = param["group_by_columns"]
            mock_row.__getitem__ = lambda self, key: getattr(self, key)
            mock_rows.append(mock_row)

        mock_df = Mock()
        mock_df.collect.return_value = mock_rows

        registry = ParameterRegistry(self.mock_spark, self.mock_config, self.mock_logger)

        with patch.object(registry, 'io_utils') as mock_io_utils:
            mock_io_utils.read_from_mongodb.return_value = mock_df

            result = registry.get_parameter_configs(["ticker_views_prev_day"])

            self.assertIsNotNone(result)
            self.assertIn("ticker_views_prev_day", result)
            self.assertEqual(result["ticker_views_prev_day"]["parameter_id"], "ticker_views_prev_day")
            self.assertEqual(result["ticker_views_prev_day"]["data_source_class"], "APP_EVENTS")

    def test_get_parameter_configs_by_ids(self):
        """Test getting multiple parameter configs by IDs."""
        # Create mock rows with proper structure
        mock_rows = []
        for param in self.mock_parameters:
            mock_row = Mock()
            mock_row.parameter_id = param["_id"]
            mock_row.name = param["name"]
            mock_row.data_source_class = param["data_source"]
            mock_row.sql_condition = param["sql_condition"]
            mock_row.join_keys = param["join_keys"]
            mock_row.required_columns = param["required_columns"]
            mock_row.output_columns = param["output_columns"]
            mock_row.group_by_columns = param["group_by_columns"]
            mock_row.__getitem__ = lambda self, key: getattr(self, key)
            mock_rows.append(mock_row)

        mock_df = Mock()
        mock_df.collect.return_value = mock_rows

        registry = ParameterRegistry(self.mock_spark, self.mock_config, self.mock_logger)

        with patch.object(registry, 'io_utils') as mock_io_utils:
            mock_io_utils.read_from_mongodb.return_value = mock_df

            result = registry.get_parameter_configs(["ticker_views_prev_day", "invested_global_stock_crypto_accounts"])

            self.assertEqual(len(result), 2)
            self.assertIn("ticker_views_prev_day", result)
            self.assertIn("invested_global_stock_crypto_accounts", result)

    def test_validate_parameter_config_valid(self):
        """Test validation of valid parameter config."""
        registry = ParameterRegistry(self.mock_spark, self.mock_config, self.mock_logger)

        valid_config = {
            "parameter_id": "test_param",
            "data_source_class": "AUMDataSource",  # Changed from data_source to data_source_class
            "sql_condition": "asset_type = 'crypto'",
            "join_keys": ["account_id"],
            "required_columns": ["account_id", "asset_type"],
            "output_columns": ["account_id", "asset_type"]
        }

        is_valid = registry.validate_parameter_config(valid_config)

        self.assertTrue(is_valid)

    def test_validate_parameter_config_missing_required(self):
        """Test validation of parameter config with missing required fields."""
        registry = ParameterRegistry(self.mock_spark, self.mock_config, self.mock_logger)

        invalid_config = {
            "parameter_id": "test_param"
            # Missing required fields
        }

        is_valid = registry.validate_parameter_config(invalid_config)

        self.assertFalse(is_valid)


    def test_clear_cache(self):
        """Test clearing parameter cache."""
        registry = ParameterRegistry(self.mock_spark, self.mock_config, self.mock_logger)

        # Add something to cache
        registry._parameter_cache["test"] = {"test": "value"}

        # Clear cache
        registry.clear_cache()

        self.assertEqual(len(registry._parameter_cache), 0)

    def test_get_cached_parameters(self):
        """Test getting cached parameters."""
        registry = ParameterRegistry(self.mock_spark, self.mock_config, self.mock_logger)

        # Add to cache
        test_cache = {"param1": {"parameter_id": "param1"}}
        registry._parameter_cache = test_cache

        result = registry.get_cached_parameters()

        self.assertEqual(result, test_cache)


if __name__ == '__main__':
    unittest.main()
