import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestStakingRequestsProcessingNoSpark:
    """No-Spark tests for StakingRequestsProcessing business logic."""

    def _setup_mocks(self, mock_crypto_staking_config):
        """Helper to setup common mocks for no-spark tests."""
        with patch('src.jobs.crypto_staking.staking_requests_processing.get_logger') as mock_logger, \
             patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.Operations'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils') as mock_date_utils:
            
            mock_logger.return_value = Mock()
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
            return mock_date_utils

    def test_config_initialization_logic(self, mock_crypto_staking_config):
        """Test configuration initialization without Spark dependencies."""
        with patch('src.jobs.crypto_staking.staking_requests_processing.get_logger') as mock_logger, \
             patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.Operations'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils') as mock_date_utils:
            
            mock_logger.return_value = Mock()
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
            
            from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
            processor = StakingRequestsProcessing(mock_crypto_staking_config)
            
            assert processor.bucket_path == "s3a://test-bucket"
            assert processor.offset == 0
            assert processor.execution_time == "jkt_day_end"
            assert processor.t_1 == "2025-01-15"
            assert processor.t_2 == "2025-01-14"
            assert processor.current_date == datetime(2025, 1, 15).date()

    def test_s3_path_construction_logic(self, mock_crypto_staking_config):
        """Test S3 path construction logic without Spark."""
        from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
        
        with patch('src.jobs.crypto_staking.staking_requests_processing.get_logger'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils') as mock_io_utils, \
             patch('src.jobs.crypto_staking.staking_requests_processing.Operations'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils') as mock_date_utils:
            
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
            
            processor = StakingRequestsProcessing(mock_crypto_staking_config)
            processor.io_utils = mock_io_utils.return_value
            
            processor.get_requests_snapshot_data()
            
            expected_path = "s3a://test-bucket/snapshots/crypto_currency_staking_transactions/dt=2025-01-15/"
            mock_io_utils.return_value.read_parquet_data.assert_called_once_with(expected_path)

    def test_time_window_calculation_logic(self):
        """Test time window calculation logic for filtering transactions."""
        from src.utils.date_utils import DateUtils
        
        current_date = datetime(2025, 1, 15).date()
        
        with patch.object(DateUtils, 'get_jkt_date') as mock_get_jkt_date:
            mock_get_jkt_date.return_value = datetime(2025, 1, 14).date()
            
            yesterday = DateUtils.get_jkt_date(1)
            assert yesterday == datetime(2025, 1, 14).date()
            
            expected_start_time = datetime(2025, 1, 14, 9, 0, 0)
            assert expected_start_time.date() == yesterday

    def test_ref_id_generation_logic(self):
        """Test ref_id generation logic without Spark."""
        current_date = datetime(2025, 1, 15).date()
        current_date_formatted = current_date.strftime("%Y%m%d")
        
        test_cases = [(825, "BTC", "825_BTC_20250115"), (826, "ETH", "826_ETH_20250115")]
        
        for crypto_currency_id, symbol, expected_ref_id in test_cases:
            ref_id = f"{crypto_currency_id}_{symbol}_{current_date_formatted}"
            assert ref_id == expected_ref_id

    def test_status_filtering_logic(self):
        """Test status filtering logic without Spark."""
        from src.enums.crypto_staking import StakingStatus
        
        non_terminal_statuses = [s.value for s in StakingStatus.non_terminal_status()]
        expected_non_terminal = ["STAKING_REQUESTED", "STAKING_IN_PROGRESS", "UNSTAKING_REQUESTED", "UNSTAKING_IN_WAITING", "UNSTAKING_IN_PROGRESS"]
        assert set(non_terminal_statuses) == set(expected_non_terminal)
        
        terminal_statuses = [s.value for s in StakingStatus.terminal_status()]
        expected_terminal = ["STAKING_CANCELLED", "UNSTAKING_CANCELLED", "STAKED", "UNSTAKED"]
        assert set(terminal_statuses) == set(expected_terminal)

    def test_transaction_type_determination_logic(self):
        """Test transaction type determination logic."""
        test_cases = [(100.0, "STAKING"), (-50.0, "UNSTAKING"), (0.0, "STAKING")]
        
        for net_amount, expected_type in test_cases:
            transaction_type = "STAKING" if net_amount >= 0 else "UNSTAKING"
            assert transaction_type == expected_type

    def test_kafka_topic_configuration_logic(self, mock_crypto_staking_config):
        """Test Kafka topic configuration logic."""
        with patch('src.jobs.crypto_staking.staking_requests_processing.get_logger') as mock_logger, \
             patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.Operations'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils') as mock_date_utils:
            
            mock_logger.return_value = Mock()
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
            
            from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
            processor = StakingRequestsProcessing(mock_crypto_staking_config)
            
            assert processor.config["kafka_topics"]["staking_user_transactions_topic"] == "staking_user_transactions"
            assert processor.config["kafka_topics"]["crypto_net_staking_requests"] == "crypto_net_staking_requests"

    def test_actor_properties_configuration(self, mock_crypto_staking_config):
        """Test actor properties configuration logic."""
        with patch('src.jobs.crypto_staking.staking_requests_processing.get_logger') as mock_logger, \
             patch('src.jobs.crypto_staking.staking_requests_processing.SparkUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.IOUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.Operations'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.AssetUtils'), \
             patch('src.jobs.crypto_staking.staking_requests_processing.DateUtils') as mock_date_utils:
            
            mock_logger.return_value = Mock()
            mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15).date()
            
            from src.jobs.crypto_staking.staking_requests_processing import StakingRequestsProcessing
            processor = StakingRequestsProcessing(mock_crypto_staking_config)
            
            assert processor.config["crypto_staking_topic_actor_prop"] == "crypto_staking_actor"

    def test_date_handling_logic(self):
        """Test date handling and formatting logic."""
        test_dates = [(datetime(2025, 1, 15).date(), "20250115"), (datetime(2025, 12, 31).date(), "20251231")]
        
        for test_date, expected_format in test_dates:
            formatted_date = test_date.strftime("%Y%m%d")
            assert formatted_date == expected_format

    def test_validation_error_conditions(self):
        """Test validation error condition logic."""
        terminal_statuses = ["STAKED", "UNSTAKED", "STAKING_CANCELLED", "UNSTAKING_CANCELLED"]
        
        test_cases = [
            ("STAKED", None, True),  # Terminal with null next_transition_time (valid)
            ("STAKED", datetime(2025, 1, 16, 10, 0, 0), False),  # Terminal with non-null next_transition_time (invalid)
            ("STAKING_REQUESTED", datetime(2025, 1, 16, 10, 0, 0), True)  # Non-terminal with next_transition_time (valid)
        ]
        
        for status, next_transition_time, expected_valid in test_cases:
            if status in terminal_statuses:
                is_valid = next_transition_time is None
            else:
                is_valid = True  # Non-terminal statuses are always valid
            assert is_valid == expected_valid

    def test_quantity_aggregation_logic(self):
        """Test quantity aggregation logic for net staking amount."""
        transactions = [
            {"crypto_currency_id": 825, "quantity": 100.0, "system_status": "STAKING_REQUESTED"},
            {"crypto_currency_id": 825, "quantity": 50.0, "system_status": "STAKING_REQUESTED"},
            {"crypto_currency_id": 825, "quantity": 30.0, "system_status": "UNSTAKING_IN_WAITING"},
            {"crypto_currency_id": 826, "quantity": 200.0, "system_status": "STAKING_REQUESTED"},
            {"crypto_currency_id": 826, "quantity": 75.0, "system_status": "UNSTAKING_IN_WAITING"}
        ]
        
        aggregated = {}
        for txn in transactions:
            crypto_id = txn["crypto_currency_id"]
            if crypto_id not in aggregated:
                aggregated[crypto_id] = {"staking": 0.0, "unstaking": 0.0}
            
            if txn["system_status"] == "STAKING_REQUESTED":
                aggregated[crypto_id]["staking"] += txn["quantity"]
            elif txn["system_status"] == "UNSTAKING_IN_WAITING":
                aggregated[crypto_id]["unstaking"] += txn["quantity"]
        
        net_amounts = {crypto_id: amounts["staking"] - amounts["unstaking"] for crypto_id, amounts in aggregated.items()}
        
        assert net_amounts[825] == 120.0  # 150 - 30
        assert net_amounts[826] == 125.0  # 200 - 75

    def test_error_logging_conditions(self):
        """Test error logging condition logic."""
        test_cases = [
            (5, 3, True),   # Terminal status with non-null next_transition_time
            (5, 5, False),  # All terminal status records have null next_transition_time
        ]
        
        for terminal_count, null_next_time_count, should_log_error in test_cases:
            assert (null_next_time_count != terminal_count) == should_log_error
        
        # Invalid next_transition_time (past date)
        current_date = datetime(2025, 1, 15).date()
        next_transition_date = datetime(2025, 1, 14).date()
        assert next_transition_date < current_date

    def test_business_logic_edge_cases(self):
        """Test business logic edge cases."""
        test_cases = [(0.0, False), (0.00000001, True), (999999999.99999999, True), (-100.0, False)]
        
        for quantity, should_process in test_cases:
            assert (quantity > 0) == should_process
